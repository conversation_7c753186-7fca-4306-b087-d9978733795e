# BP_GestureManager Implementation Guide

## Overview
The BP_GestureManager is the central Actor Component that coordinates all gesture detection and processing for the MVS VR environment. It integrates with both Ultraleap and Varjo hand tracking systems.

## Blueprint Creation Steps

### 1. Create Blueprint Component
1. **Content Browser** > Right-click > **Blueprint Class**
2. **Parent Class**: Select **Actor Component**
3. **Name**: `BP_GestureManager`
4. **Save Location**: `Blueprints/GestureSystem/`

### 2. Component Setup

#### Variables to Add
```yaml
# Hand Tracking Components
- Name: LeapComponent
  Type: Leap Component (Object Reference)
  Category: "MVS Gestures|Components"
  Tooltip: "Ultraleap hand tracking component reference"

- Name: GestureComponents
  Type: Array of Actor Component (Object Reference)  
  Category: "MVS Gestures|Components"
  Tooltip: "Array of registered gesture components"

# Gesture State Management
- Name: bIsGestureSystemActive
  Type: Boolean
  Default: true
  Category: "MVS Gestures|State"
  Tooltip: "Master switch for gesture detection system"

- Name: HandTrackingConfidence
  Type: Float
  Default: 0.0
  Category: "MVS Gestures|State"
  Tooltip: "Current hand tracking confidence level"

# Configuration
- Name: GestureSensitivity
  Type: Float
  Default: 1.0
  Range: [0.1, 2.0]
  Category: "MVS Gestures|Configuration"
  Tooltip: "Global gesture sensitivity multiplier"

- Name: MinTrackingConfidence
  Type: Float
  Default: 0.6
  Range: [0.1, 1.0]
  Category: "MVS Gestures|Configuration"
  Tooltip: "Minimum confidence required for gesture detection"
```

#### Custom Events to Create
```yaml
# System Events
- Event: OnGestureSystemInitialized
  Description: "Fired when gesture system completes initialization"

- Event: OnHandTrackingLost  
  Parameters: [bool bIsLeftHand]
  Description: "Fired when hand tracking is lost"

- Event: OnHandTrackingRestored
  Parameters: [bool bIsLeftHand]  
  Description: "Fired when hand tracking is restored"

# Gesture Dispatch Events
- Event: OnGestureDetected
  Parameters: [FString GestureType, float Strength, bool bIsLeftHand, FVector Position]
  Description: "Central gesture detection event dispatcher"

- Event: OnGestureProgression
  Parameters: [FString GestureType, float Progress]
  Description: "Gesture progression update dispatcher"

- Event: OnGestureEnded
  Parameters: [FString GestureType, FVector EndPosition]  
  Description: "Gesture completion dispatcher"
```

### 3. Event Graph Implementation

#### BeginPlay Event
```blueprint
Event BeginPlay
  ↓
[Initialize Gesture System]
  ↓
[Get Leap Component Reference]
  → [IsValid?] → [Bind Leap Events] → [Setup Ultraleap Integration]
  ↓
[Setup Varjo OpenXR Integration] 
  ↓
[Register Gesture Components]
  → [For Each Loop: GestureComponents]
    → [Initialize Component]
    → [Bind Component Events]
  ↓
[Start Gesture Detection Tick]
  ↓
[Fire OnGestureSystemInitialized]
  ↓
[Print String: "MVS Gesture System Initialized"]
```

#### Tick Event (if needed)
```blueprint
Event Tick
  ↓
[Branch: bIsGestureSystemActive]
  → True:
    ↓
    [Get Motion Controller Data] (Left Hand)
      → [Process Hand Data: Left]
    ↓
    [Get Motion Controller Data] (Right Hand)  
      → [Process Hand Data: Right]
    ↓
    [Update Hand Tracking Confidence]
    ↓
    [Update All Gesture Components]
```

#### Ultraleap Event Bindings
```blueprint
# Bind these events in BeginPlay to the LeapComponent

[On Pinch] Event (from LeapComponent)
  ↓
[Process Pinch Gesture]
  → [Fire OnGestureDetected: "Pinch"]

[On Unpinch] Event (from LeapComponent)
  ↓  
[Process Unpinch Gesture]
  → [Fire OnGestureEnded: "Pinch"]

[On Grab] Event (from LeapComponent)
  ↓
[Process Grab Gesture] 
  → [Fire OnGestureDetected: "Grab"]

[On Release] Event (from LeapComponent)
  ↓
[Process Release Gesture]
  → [Fire OnGestureEnded: "Grab"]
```

### 4. Custom Functions to Implement

#### ProcessHandData Function
```blueprint
Function: ProcessHandData
Parameters: 
  - HandData: XRMotionControllerData (or FLeapHandData)
  - bIsLeftHand: Boolean

Logic:
  ↓
[Validate Hand Data]
  → [Check Tracking Confidence]
  ↓
[Calculate Gesture Metrics]
  → [Palm Position, Finger Positions, Hand Velocity]
  ↓  
[For Each Gesture Component]
  → [Update Component with Hand Data]
  → [Check Gesture Thresholds]
  → [Fire Appropriate Events]
```

#### RegisterGestureComponent Function
```blueprint
Function: RegisterGestureComponent  
Parameters:
  - GestureComponent: Actor Component

Logic:
  ↓
[Add to GestureComponents Array]
  ↓
[Bind Component Events]
  → [OnGestureDetected] → [Route to Central Dispatcher]
  → [OnGestureProgression] → [Route to Central Dispatcher]  
  → [OnGestureEnded] → [Route to Central Dispatcher]
```

#### SetGestureSensitivity Function
```blueprint
Function: SetGestureSensitivity
Parameters:
  - NewSensitivity: Float

Logic:
  ↓
[Set GestureSensitivity = NewSensitivity]
  ↓
[For Each Gesture Component]
  → [Update Component Sensitivity]
```

### 5. Integration Points

#### With VR Pawn
- Add this component to the VR Pawn (BP_XRPassthroughPawn)
- Bind gesture events to pawn actions
- Configure hand tracking visualization

#### With Gesture Components
- All individual gesture components register with this manager
- Central event dispatching ensures consistent behavior
- Shared configuration and state management

### 6. Debug Features
```blueprint
# Debug Visualization (enabled in Development builds only)
- Draw debug lines for palm positions
- Display gesture confidence values as text
- Show active gesture states with color coding
- Log gesture events to output log

# Console Commands (implement via Blueprint Callable functions)
- "mvs.gesture.sensitivity <float>" - Adjust sensitivity
- "mvs.gesture.debug <bool>" - Toggle debug visualization  
- "mvs.gesture.reset" - Reset gesture system
```

### 7. Error Handling
```blueprint
# Hand Tracking Loss
- Monitor tracking confidence levels
- Gracefully disable gestures when tracking is poor
- Provide user feedback for tracking issues
- Automatic recovery when tracking improves

# Component Failures  
- Validate component references before use
- Skip failed components without breaking system
- Log errors for debugging
- Provide fallback behavior when possible
```

### 8. Performance Considerations
- Use event-driven architecture instead of constant Tick updates
- Cache frequently accessed data
- Implement confidence-based early exits
- Profile gesture detection performance regularly

## Testing Checklist
- [ ] Component initializes without errors
- [ ] Leap Component binding works correctly  
- [ ] Varjo hand tracking data is processed
- [ ] Gesture events fire appropriately
- [ ] Performance remains above 90 FPS
- [ ] Debug visualization works in development
- [ ] Error handling responds correctly to tracking loss

## Integration with Other Components
This component will be referenced by:
- All individual gesture components (GS_Grab, GS_Teleport, etc.)
- BP_GestureVisualFeedback for visual indicators
- VR Pawn for gesture-based actions
- Level Blueprint for system-wide gesture handling