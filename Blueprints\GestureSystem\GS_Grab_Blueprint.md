# GS_Grab - Actual Blueprint Implementation

## Blueprint Creation Steps

### 1. Create Blueprint
1. **Content Browser** → Right-click → **Blueprint Class**
2. **Parent Class**: `Actor Component`
3. **Name**: `GS_Grab`
4. **Location**: `Content/Blueprints/GestureSystem/`

## Variables Setup

### Create These Variables (Details Panel)
```cpp
// Grab State Management
bIsGrabbing (Boolean) = false
  Category: "Grab|State"
  Tooltip: "True when actively grabbing an object"

bCanGrab (Boolean) = true
  Category: "Grab|State"
  Tooltip: "Master switch for grab functionality"

GrabbedObject (Actor | Object Reference) = None
  Category: "Grab|State"
  Tooltip: "Currently grabbed object reference"

GrabStartPosition (Vector) = (0,0,0)
  Category: "Grab|State"
  Tooltip: "Hand position when grab started"

ObjectOriginalLocation (Vector) = (0,0,0)
  Category: "Grab|State"
  Tooltip: "Original position of grabbed object"

GrabbingHand (Boolean) = false
  Category: "Grab|State"
  Tooltip: "True if left hand, false if right hand"

GestureManager (BP_GestureManager | Object Reference) = None
  Category: "Grab|References"
  Tooltip: "Reference to gesture manager component"

// Configuration Settings  
GrabRange (Float) = 15.0 [Range: 5.0, 50.0]
  Category: "Grab|Configuration"
  Tooltip: "Maximum distance to grab objects (cm)"

GrabThreshold (Float) = 0.7 [Range: 0.1, 1.0]
  Category: "Grab|Configuration"
  Tooltip: "Minimum grab strength required"

ReleaseThreshold (Float) = 0.4 [Range: 0.1, 0.8]
  Category: "Grab|Configuration"
  Tooltip: "Maximum grab strength for release"

GrabSmoothingFactor (Float) = 0.85 [Range: 0.1, 1.0]
  Category: "Grab|Configuration"
  Tooltip: "Smoothing factor for object movement"

// Physics Settings
bUsePhysicsGrab (Boolean) = true
  Category: "Grab|Physics"
  Tooltip: "Use physics-based grabbing instead of direct attachment"

GrabForceStrength (Float) = 1000.0 [Range: 100.0, 5000.0]
  Category: "Grab|Physics"
  Tooltip: "Force strength for physics-based grabs"

bPreserveObjectPhysics (Boolean) = false
  Category: "Grab|Physics"
  Tooltip: "Keep object physics active during grab"

// Detection
GrabDetectionSphere (Sphere Component | Object Reference) = None
  Category: "Grab|Detection"
  Tooltip: "Sphere component for grab detection"

HighlightedObjects (Array | Actor | Object Reference)
  Category: "Grab|Highlighting"
  Tooltip: "Currently highlighted grabbable objects"

bShowDebugInfo (Boolean) = true
  Category: "Grab|Debug"
  Tooltip: "Show debug information during development"
```

## Components Setup (Components Panel)

### Add These Components
```cpp
1. Add Component → Sphere Collision
   Name: "GrabDetectionSphere"
   Collision Profile: "OverlapAllDynamic"
   Sphere Radius: 15.0 (matches GrabRange)
   
2. Add Component → Physics Handle
   Name: "PhysicsHandle" (for physics-based grabs)
```

## Custom Events Setup

### Create These Custom Events
```cpp
OnObjectGrabbed (Custom Event)
  Inputs: GrabbedActor (Actor), bIsLeftHand (Boolean), GrabPosition (Vector)
  Description: "Fired when object is successfully grabbed"

OnObjectReleased (Custom Event)
  Inputs: ReleasedActor (Actor), ReleasePosition (Vector), ReleaseVelocity (Vector)
  Description: "Fired when object is released"

OnObjectMoved (Custom Event)
  Inputs: MovedActor (Actor), NewPosition (Vector)
  Description: "Fired continuously while moving grabbed object"

OnGrabAttempt (Custom Event)
  Inputs: TargetActor (Actor), Distance (Float)
  Description: "Fired when grab is attempted on an object"

OnGrabFailed (Custom Event)
  Inputs: TargetActor (Actor), FailReason (String)
  Description: "Fired when grab attempt fails"

OnGrabHighlight (Custom Event)
  Inputs: HighlightedActor (Actor), bIsHighlighted (Boolean)
  Description: "Fired to toggle object highlight state"
```

## Event Graph Implementation

### BeginPlay Event Chain
```
[Event BeginPlay]
    ↓
[Get Owner] → [Get Component by Class] (BP_GestureManager) → [Set GestureManager]
    ↓
[IsValid] (GestureManager)
    ↓ True
    [Call Function] (GestureManager.RegisterWithManager)
      Input: Self Reference
    ↓
    [Initialize Detection System] (Custom Function)
    ↓
    [Bind Gesture Events] (Custom Function)
    ↓
    [Print String] ("GS_Grab Initialized" | Green | 2.0)
    
    False ↓
    [Print String] ("ERROR: No Gesture Manager Found" | Red | 5.0)
```

### Initialize Detection System Function
```
[Initialize Detection System] (Custom Function)
    ↓
[Get Component by Class] (Sphere Component) → [Set GrabDetectionSphere]
    ↓
[Set Sphere Radius] (GrabDetectionSphere, GrabRange)
    ↓
[Set Collision Profile Name] (GrabDetectionSphere, "OverlapAllDynamic")
    ↓
[Bind Event to On Component Begin Overlap] (GrabDetectionSphere) → [OnGrabRangeEnter] (Event)
    ↓
[Bind Event to On Component End Overlap] (GrabDetectionSphere) → [OnGrabRangeExit] (Event)
    ↓
[Branch] (bShowDebugInfo)
    ↓ True
    [Print String] ("Grab Detection System Initialized" | Blue | 2.0)
```

### Bind Gesture Events Function
```
[Bind Gesture Events] (Custom Function)
    ↓
[GestureManager] → [Bind Event to OnGestureDetected] → [OnGrabGestureDetected] (Event)
    ↓
[GestureManager] → [Bind Event to OnGestureProgression] → [OnGrabProgression] (Event)
    ↓
[GestureManager] → [Bind Event to OnGestureEnded] → [OnGrabReleased] (Event)
```

### Grab Range Detection

#### OnGrabRangeEnter Event
```
[OnGrabRangeEnter] (Component Begin Overlap Event)
  Inputs: OverlappedComponent, OtherActor, OtherComponent, OtherBodyIndex, bFromSweep, SweepResult
    ↓
[Cast to Actor] (OtherActor) → CastResult
    ↓
[Branch] (IsValid CastResult)
    ↓ True
    [Actor Has Tag] (CastResult, "Grabbable")
        ↓ True
        [Find Grabbable Object] (Custom Function)
          Input: CastResult
          Output: bIsGrabbable (Boolean)
        ↓
        [Branch] (bIsGrabbable)
            ↓ True
            [Add Unique] (HighlightedObjects, CastResult)
            ↓
            [Highlight Object] (Custom Function)
              Inputs: CastResult, True
            ↓
            [OnGrabHighlight] (Call Event)
              HighlightedActor: CastResult
              bIsHighlighted: True
            ↓
            [Branch] (bShowDebugInfo)
                ↓ True
                [Print String] ("Object in grab range: " + CastResult.GetName() | Yellow | 1.0)
```

#### OnGrabRangeExit Event
```
[OnGrabRangeExit] (Component End Overlap Event)
  Inputs: OverlappedComponent, OtherActor, OtherComponent, OtherBodyIndex
    ↓
[Cast to Actor] (OtherActor) → CastResult
    ↓
[Remove Item] (HighlightedObjects, CastResult)
    ↓
[Highlight Object] (Custom Function)
      Inputs: CastResult, False
    ↓
[OnGrabHighlight] (Call Event)
      HighlightedActor: CastResult
      bIsHighlighted: False
```

### Grab Gesture Handlers

#### OnGrabGestureDetected Event
```
[OnGrabGestureDetected] (Event Dispatcher Bound)
  Inputs: GestureType (String), Strength (Float), bIsLeftHand (Boolean), Position (Vector)
    ↓
[Branch] (GestureType == "Grab")
    ↓ True
    [Branch] (Strength >= GrabThreshold)
        ↓ True
        [Branch] (!bIsGrabbing AND bCanGrab)
            ↓ True
            [Find Closest Grabbable Object] (Custom Function)
              Input: Position
              Output: ClosestObject (Actor), Distance (Float)
            ↓
            [Branch] (IsValid ClosestObject)
                ↓ True
                [OnGrabAttempt] (Call Event)
                  TargetActor: ClosestObject
                  Distance: Distance
                ↓
                [Validate Grab Conditions] (Custom Function)
                  Input: ClosestObject
                  Output: bCanGrabObject (Boolean), FailReason (String)
                ↓
                [Branch] (bCanGrabObject)
                    ↓ True
                    [Execute Grab] (Custom Function)
                      Inputs: ClosestObject, Position, bIsLeftHand
                    ↓
                    [OnObjectGrabbed] (Call Event)
                      GrabbedActor: ClosestObject
                      bIsLeftHand: bIsLeftHand
                      GrabPosition: Position
                    
                    False ↓
                    [OnGrabFailed] (Call Event)
                      TargetActor: ClosestObject
                      FailReason: FailReason
                
                False ↓
                [OnGrabFailed] (Call Event)
                  TargetActor: None
                  FailReason: "No Object in Range"
```

#### OnGrabProgression Event
```
[OnGrabProgression] (Event Dispatcher Bound)
  Inputs: GestureType (String), Progress (Float), CurrentHandPosition (Vector)
    ↓
[Branch] (GestureType == "Grab" AND bIsGrabbing)
    ↓ True
    [Branch] (IsValid GrabbedObject)
        ↓ True
        [Calculate New Object Position] (Custom Function)
          Inputs: CurrentHandPosition, GrabStartPosition, GrabSmoothingFactor
          Output: NewPosition (Vector)
        ↓
        [Branch] (bUsePhysicsGrab)
            ↓ True
            [Apply Physics Force] (Custom Function)
              Inputs: GrabbedObject, NewPosition
            
            False ↓
            [Direct Position Update] (Custom Function)
              Inputs: GrabbedObject, NewPosition
        ↓
        [OnObjectMoved] (Call Event)
          MovedActor: GrabbedObject
          NewPosition: NewPosition
```

#### OnGrabReleased Event
```
[OnGrabReleased] (Event Dispatcher Bound)
  Inputs: GestureType (String), FinalStrength (Float), HandPosition (Vector)
    ↓
[Branch] (GestureType == "Grab" AND bIsGrabbing)
    ↓ True
    [Branch] (FinalStrength <= ReleaseThreshold)
        ↓ True
        [Calculate Release Velocity] (Custom Function)
          Input: HandPosition
          Output: ReleaseVel (Vector)
        ↓
        [Release Object] (Custom Function)
          Inputs: GrabbedObject, ReleaseVel
        ↓
        [OnObjectReleased] (Call Event)
          ReleasedActor: GrabbedObject
          ReleasePosition: HandPosition
          ReleaseVelocity: ReleaseVel
        ↓
        [Reset Grab State] (Custom Function)
```

## Custom Functions Implementation

### Find Closest Grabbable Object Function
```
[Find Closest Grabbable Object] (Custom Function)
  Inputs: HandPosition (Vector)
  Outputs: ClosestObject (Actor), Distance (Float)
    ↓
[For Each Loop] (HighlightedObjects) → CurrentObject
    ↓
    [IsValid] (CurrentObject)
        ↓ True
        [Get Actor Location] (CurrentObject) → ObjectLocation
        ↓
        [Vector Distance] (HandPosition, ObjectLocation) → CurrentDistance
        ↓
        [Branch] (CurrentDistance < Distance OR !IsValid ClosestObject)
            ↓ True
            [Set ClosestObject] (CurrentObject)
            ↓
            [Set Distance] (CurrentDistance)
    ↓
[Return Node] ClosestObject, Distance
```

### Execute Grab Function
```
[Execute Grab] (Custom Function)
  Inputs: ObjectToGrab (Actor), HandPosition (Vector), bIsLeftHand (Boolean)
    ↓
[Set bIsGrabbing] (True)
    ↓
[Set GrabbedObject] (ObjectToGrab)
    ↓
[Set GrabStartPosition] (HandPosition)
    ↓
[Set GrabbingHand] (bIsLeftHand)
    ↓
[Get Actor Location] (ObjectToGrab) → [Set ObjectOriginalLocation]
    ↓
[Attach Object To Hand] (Custom Function)
      Inputs: ObjectToGrab, HandPosition, bUsePhysicsGrab
    ↓
[Branch] (bShowDebugInfo)
        ↓ True
        [Print String] ("Grabbed: " + ObjectToGrab.GetName() | Green | 2.0)
```

### Attach Object To Hand Function
```
[Attach Object To Hand] (Custom Function)
  Inputs: ObjectToGrab (Actor), HandPosition (Vector), bUsePhysics (Boolean)
    ↓
[Store Original Object State] (Custom Function)
      Input: ObjectToGrab
    ↓
[Branch] (bUsePhysics)
    ↓ True
    [Get Component by Class] (ObjectToGrab, Static Mesh Component) → MeshComp
    ↓
    [Branch] (IsValid MeshComp)
        ↓ True
        [Get Physics Handle Component] (Self) → PhysicsHandle
        ↓
        [Grab Component at Location] (PhysicsHandle)
          Component: MeshComp
          Location: HandPosition
          Bone Name: ""
        ↓
        [Set Linear Damping] (PhysicsHandle, 200.0)
        ↓
        [Set Angular Damping] (PhysicsHandle, 500.0)
    
    False ↓ (Direct Attachment)
    [Set Simulate Physics] (ObjectToGrab, False)
    ↓
    [Set Collision Enabled] (ObjectToGrab, "No Collision")
    ↓
    [Attach Actor to Actor] (ObjectToGrab, Get Owner, "", "Keep World", "Keep World", true)
```

### Calculate New Object Position Function
```
[Calculate New Object Position] (Custom Function)
  Inputs: CurrentHandPos (Vector), StartHandPos (Vector), SmoothingFactor (Float)
  Outputs: NewPosition (Vector)
    ↓
[Vector - Vector] (CurrentHandPos - StartHandPos) → HandDelta
    ↓
[Vector + Vector] (ObjectOriginalLocation + HandDelta) → TargetPosition
    ↓
[Get Actor Location] (GrabbedObject) → CurrentObjectPos
    ↓
[Lerp Vector] (CurrentObjectPos, TargetPosition, (1.0 - SmoothingFactor)) → SmoothedPosition
    ↓
[Return Node] SmoothedPosition
```

### Apply Physics Force Function
```
[Apply Physics Force] (Custom Function)
  Inputs: TargetObject (Actor), TargetPosition (Vector)
    ↓
[Get Physics Handle Component] (Self) → PhysicsHandle
    ↓
[IsValid] (PhysicsHandle)
        ↓ True
        [Set Target Location] (PhysicsHandle, TargetPosition)
        ↓
        [Set Interpolation Speed] (PhysicsHandle, 25.0)
```

### Direct Position Update Function
```
[Direct Position Update] (Custom Function)
  Inputs: TargetObject (Actor), NewPosition (Vector)
    ↓
[Set Actor Location] (TargetObject, NewPosition)
    ↓
[Branch] (bShowDebugInfo)
        ↓ True
        [Draw Debug Sphere] (NewPosition, 2.0, Green, 0.1, 1.0)
```

### Release Object Function
```
[Release Object] (Custom Function)
  Inputs: ObjectToRelease (Actor), ReleaseVelocity (Vector)
    ↓
[Branch] (IsValid ObjectToRelease)
    ↓ True
    [Branch] (bUsePhysicsGrab)
        ↓ True
        [Get Physics Handle Component] (Self) → PhysicsHandle
        ↓
        [Release Component] (PhysicsHandle)
        
        False ↓
        [Detach Actor from Actor] (ObjectToRelease, "Keep World", "Keep World")
    ↓
    [Restore Object State] (Custom Function)
          Input: ObjectToRelease
    ↓
    [Apply Release Physics] (Custom Function)
          Inputs: ObjectToRelease, ReleaseVelocity
    ↓
    [Branch] (bShowDebugInfo)
        ↓ True
        [Print String] ("Released: " + ObjectToRelease.GetName() | Cyan | 2.0)
```

### Highlight Object Function
```
[Highlight Object] (Custom Function)
  Inputs: TargetObject (Actor), bHighlight (Boolean)
    ↓
[Get Component by Class] (TargetObject, Static Mesh Component) → MeshComp
    ↓
[Branch] (IsValid MeshComp)
        ↓ True
        [Branch] (bHighlight)
            ↓ True
            [Create Dynamic Material Instance] (MeshComp, 0) → DynamicMat
            ↓
            [Set Vector Parameter Value] (DynamicMat, "EmissiveColor", Green)
            ↓
            [Set Scalar Parameter Value] (DynamicMat, "EmissiveIntensity", 2.0)
            
            False ↓
            [Set Material] (MeshComp, 0, [Original Material])
```

### Validate Grab Conditions Function
```
[Validate Grab Conditions] (Custom Function)
  Inputs: TargetObject (Actor)
  Outputs: bCanGrabObject (Boolean), FailReason (String)
    ↓
[Actor Has Tag] (TargetObject, "Grabbable")
    ↓ True
    [Get Component by Class] (TargetObject, Static Mesh Component) → MeshComp
    ↓
    [Branch] (IsValid MeshComp)
        ↓ True
        [Get All Actors of Class] (GS_Grab) → AllGrabComponents
        ↓
        [For Each Loop] (AllGrabComponents) → OtherGrabber
            ↓
            [Branch] (OtherGrabber != Self AND OtherGrabber.GrabbedObject == TargetObject)
                ↓ True
                [Return Node] bCanGrabObject: False, FailReason: "Already Grabbed"
        ↓
        [Return Node] bCanGrabObject: True, FailReason: ""
        
        False ↓
        [Return Node] bCanGrabObject: False, FailReason: "No Mesh Component"
    
    False ↓
    [Return Node] bCanGrabObject: False, FailReason: "Not Grabbable"
```

## Object Setup Requirements

### For Grabbable Objects
Add these to any object you want to be grabbable:

1. **Add Tag**: `"Grabbable"`
2. **Static Mesh Component**: Required for physics and rendering
3. **Collision**: Set to "Block All" for physics grabs
4. **Physics**: Enable "Simulate Physics" if using physics grabs

### Material Setup for Highlighting
Objects should have materials that support:
- **EmissiveColor** parameter (Vector)
- **EmissiveIntensity** parameter (Scalar)

## Testing Checklist

1. **Proximity Detection**: Objects highlight when hand enters range
2. **Grab Execution**: Objects attach to hand on grab gesture
3. **Movement**: Grabbed objects follow hand smoothly
4. **Release**: Objects release with appropriate velocity
5. **Physics**: Both physics and direct attachment modes work
6. **Multiple Objects**: Can grab different objects with different hands
7. **Error Handling**: Graceful handling of invalid objects or states

This blueprint provides complete object grabbing functionality with both physics-based and direct attachment options.