# GS_Grab Implementation Guide

## Overview
The GS_Grab component handles object grabbing, manipulation, and release. Users can grab objects using the pinch or grab gesture, move them around, and release them naturally.

## Blueprint Creation Steps

### 1. Create Blueprint Component
1. **Content Browser** > Right-click > **Blueprint Class**
2. **Parent Class**: Select **Actor Component**
3. **Name**: `GS_Grab`
4. **Save Location**: `Blueprints/GestureSystem/`

### 2. Component Setup

#### Variables to Add
```yaml
# Grab State Management
- Name: bIsGrabbing
  Type: Boolean
  Default: false
  Category: "Grab|State"
  Tooltip: "True when actively grabbing an object"

- Name: bCanGrab
  Type: Boolean
  Default: true
  Category: "Grab|State" 
  Tooltip: "Master switch for grab functionality"

- Name: GrabbedObject
  Type: Actor (Object Reference)
  Category: "Grab|State"
  Tooltip: "Currently grabbed object reference"

- Name: GrabStartPosition
  Type: Vector
  Category: "Grab|State"
  Tooltip: "Hand position when grab started"

- Name: ObjectOriginalLocation
  Type: Vector
  Category: "Grab|State"
  Tooltip: "Original position of grabbed object"

- Name: GrabbingHand
  Type: Boolean
  Category: "Grab|State"
  Tooltip: "True if left hand, false if right hand"

# Configuration Settings
- Name: GrabRange
  Type: Float
  Default: 15.0
  Range: [5.0, 50.0]
  Category: "Grab|Configuration"
  Tooltip: "Maximum distance to grab objects (cm)"

- Name: GrabThreshold
  Type: Float
  Default: 0.7
  Range: [0.1, 1.0]
  Category: "Grab|Configuration"
  Tooltip: "Minimum grab strength required"

- Name: ReleaseThreshold
  Type: Float
  Default: 0.4
  Range: [0.1, 0.8]
  Category: "Grab|Configuration"
  Tooltip: "Maximum grab strength for release"

- Name: GrabSmoothingFactor
  Type: Float
  Default: 0.85
  Range: [0.1, 1.0]
  Category: "Grab|Configuration"
  Tooltip: "Smoothing factor for object movement"

# Physics Settings
- Name: bUsePhysicsGrab
  Type: Boolean
  Default: true
  Category: "Grab|Physics"
  Tooltip: "Use physics-based grabbing instead of direct attachment"

- Name: GrabForceStrength
  Type: Float
  Default: 1000.0
  Range: [100.0, 5000.0]
  Category: "Grab|Physics"
  Tooltip: "Force strength for physics-based grabs"

- Name: bPreserveObjectPhysics
  Type: Boolean
  Default: false
  Category: "Grab|Physics"
  Tooltip: "Keep object physics active during grab"
```

#### Custom Events to Create
```yaml
# Core Grab Events
- Event: OnObjectGrabbed
  Parameters: [AActor GrabbedActor, bool bIsLeftHand, FVector GrabPosition]
  Description: "Fired when object is successfully grabbed"

- Event: OnObjectReleased
  Parameters: [AActor ReleasedActor, FVector ReleasePosition, FVector ReleaseVelocity]
  Description: "Fired when object is released"

- Event: OnObjectMoved
  Parameters: [AActor MovedActor, FVector NewPosition]
  Description: "Fired continuously while moving grabbed object"

# Grab Validation Events
- Event: OnGrabAttempt
  Parameters: [AActor TargetActor, float Distance]
  Description: "Fired when grab is attempted on an object"

- Event: OnGrabFailed
  Parameters: [AActor TargetActor, FString FailReason]
  Description: "Fired when grab attempt fails"

# Visual Feedback Events
- Event: OnGrabHighlight
  Parameters: [AActor HighlightedActor, bool bIsHighlighted]
  Description: "Fired to toggle object highlight state"
```

### 3. Event Graph Implementation

#### Component Initialization
```blueprint
Event BeginPlay
  ↓
[Get Gesture Manager Reference]
  ↓
[Register with Gesture Manager]
  ↓
[Setup Physics Handle Component] (if using physics grab)
  ↓
[Initialize Object Detection Sphere]
  → [Set Collision Profile: "GrabDetection"]
  → [Set Sphere Radius: GrabRange]
  ↓
[Bind to Gesture Manager Events]
  → [OnGestureDetected: "Grab"]
  → [OnGestureProgression: "Grab"]
  → [OnGestureEnded: "Grab"]
  ↓
[Setup Object Highlighting System]
  ↓
[Print String: "GS_Grab Initialized"]
```

#### Grab Detection Handler
```blueprint
Event: OnGrabDetected (from Gesture Manager)
Parameters: [float Strength, bool bIsLeftHand, FVector HandPosition]
  ↓
[Branch: Strength >= GrabThreshold]
  → True:
    ↓
    [Branch: !bIsGrabbing && bCanGrab]
      → True:
        ↓
        [Find Grabbable Object Near Hand]
          → [Sphere Overlap at HandPosition]
          → [Filter by "Grabbable" tag]
          → [Get Closest Valid Object]
        ↓
        [Branch: Valid Object Found]
          → True:
            ↓
            [Fire OnGrabAttempt]
            ↓
            [Validate Grab Conditions]
              → [Check Object Not Already Grabbed]
              → [Check Object Has Required Components]
              → [Check Distance Within Range]
            ↓
            [Branch: Grab Valid]
              → True:
                ↓
                [Execute Grab]
                  → [Set bIsGrabbing = true]
                  → [Set GrabbedObject = Target]
                  → [Set GrabbingHand = bIsLeftHand]
                  → [Store Original Positions]
                ↓
                [Setup Object Attachment/Physics]
                ↓
                [Fire OnObjectGrabbed]
                ↓
                [Start Object Movement Tracking]
              → False:
                ↓
                [Fire OnGrabFailed]
          → False:
            ↓
            [Fire OnGrabFailed: "No Object in Range"]
```

#### Grab Progression Handler
```blueprint
Event: OnGrabProgression (from Gesture Manager)
Parameters: [float Progress, FVector CurrentHandPosition]
  ↓
[Branch: bIsGrabbing && IsValid(GrabbedObject)]
  → True:
    ↓
    [Calculate New Object Position]
      → [Apply Smoothing: Lerp between current and target]
      → [Consider Hand Movement Delta]
      → [Apply Physics if enabled]
    ↓
    [Branch: bUsePhysicsGrab]
      → True:
        ↓
        [Apply Force to Object]
          → [Calculate Force Vector]
          → [Apply to Physics Component]
      → False:
        ↓
        [Direct Position Update]
          → [Set Actor Location]
          → [Set Actor Rotation] (if rotation enabled)
    ↓
    [Fire OnObjectMoved]
    ↓
    [Update Visual Feedback]
```

#### Grab Release Handler
```blueprint
Event: OnGrabEnded (from Gesture Manager)
Parameters: [float FinalStrength, FVector HandPosition]
  ↓
[Branch: bIsGrabbing]
  → True:
    ↓
    [Branch: FinalStrength <= ReleaseThreshold]
      → True:
        ↓
        [Calculate Release Velocity]
          → [Hand Movement History]
          → [Apply Velocity to Object]
        ↓
        [Release Object]
          → [Detach from Hand]
          → [Restore Physics State]
          → [Apply Release Velocity]
        ↓
        [Fire OnObjectReleased]
        ↓
        [Reset Grab State]
          → [Set bIsGrabbing = false]
          → [Clear GrabbedObject]
          → [Clear Position History]
        ↓
        [Update Visual Feedback]
```

### 4. Custom Functions to Implement

#### FindGrabbableObject Function
```blueprint
Function: FindGrabbableObject
Parameters:
  - HandPosition: Vector
  - SearchRadius: Float
Return: Actor

Logic:
  ↓
[Sphere Overlap at HandPosition]
  → [Radius: SearchRadius]
  → [Object Types: WorldDynamic, WorldStatic]
  → [Actors to Ignore: Self, VR Pawn]
  ↓
[Filter Results]
  → [Has "Grabbable" tag]
  → [Has Static Mesh or Skeletal Mesh]
  → [Not currently grabbed by another hand]
  ↓
[Find Closest Valid Object]
  → [Calculate distances to hand]
  → [Return nearest object]
  ↓
[Return Result] (null if none found)
```

#### AttachObjectToHand Function
```blueprint
Function: AttachObjectToHand
Parameters:
  - ObjectToGrab: Actor
  - HandPosition: Vector
  - bUsePhysics: Boolean

Logic:
  ↓
[Store Original Object State]
  → [Original Location, Rotation]
  → [Original Physics Settings]
  → [Original Collision Settings]
  ↓
[Branch: bUsePhysics]
  → True:
    ↓
    [Setup Physics Constraint]
      → [Create Physics Handle]
      → [Attach to Object's Physics Body]
      → [Set Target Location: HandPosition]
  → False:
    ↓
    [Direct Attachment]
      → [Disable Physics on Object]
      → [Attach to Hand Socket/Component]
      → [Set Relative Transform]
  ↓
[Update Object State]
  → [Set Collision: Ignore Pawn]
  → [Highlight Object] (optional)
  ↓
[Start Movement Tracking]
```

#### ReleaseObject Function
```blueprint
Function: ReleaseObject
Parameters:
  - ReleaseVelocity: Vector

Logic:
  ↓
[Branch: IsValid(GrabbedObject)]
  → True:
    ↓
    [Detach Object]
      → [Remove Physics Constraint]
      → [Detach from Hand]
    ↓
    [Restore Object State]
      → [Re-enable Physics]
      → [Restore Original Collision]
      → [Remove Highlight]
    ↓
    [Apply Release Physics]
      → [Set Linear Velocity: ReleaseVelocity]
      → [Apply Angular Velocity if spinning]
    ↓
    [Clean Up References]
      → [Clear GrabbedObject]
      → [Reset State Variables]
```

#### CalculateReleaseVelocity Function
```blueprint
Function: CalculateReleaseVelocity
Parameters:
  - HandPositionHistory: Array of Vector
  - DeltaTime: Float
Return: Vector

Logic:
  ↓
[Validate History Array]
  → [Minimum 3 samples required]
  ↓
[Calculate Average Velocity]
  → [Sample last N positions]
  → [Calculate movement delta]
  → [Divide by time intervals]
  ↓
[Apply Velocity Scaling]
  → [Multiply by Release Force Multiplier]
  → [Clamp to Maximum Velocity]
  ↓
[Return Calculated Velocity]
```

### 5. Visual Feedback Integration

#### Object Highlighting
```blueprint
# Highlight System
- Material Parameter: Emissive intensity
- Valid Grab: Green outline glow
- Grabbed: Blue outline glow  
- Invalid Grab: Red outline flash
- Animation: Gentle pulsing effect

# Implementation
[Set Scalar Parameter Value: "EmissiveIntensity"]
[Set Vector Parameter Value: "OutlineColor"]
[Timeline for pulsing animation]
```

#### Hand Visual Feedback
```blueprint
# Hand State Indicators
- Open Hand: Default material
- Hover Object: Slight glow
- Grabbing: Strong glow with particle effect
- Invalid Grab: Red flash

# Particle Effects
- Grab Success: Small burst at contact point
- Grab Release: Trail effect following object
- Invalid Grab: Red "X" particle at hand
```

### 6. Integration Points

#### With Gesture Manager
- Registers for grab gesture events
- Provides grab-specific processing
- Reports grab state changes

#### With Object System
- Objects must have "Grabbable" tag
- Objects should have appropriate collision
- Physics bodies required for physics-based grabs

#### With Visual Feedback System
- Highlights grabbable objects
- Shows grab state through materials
- Particle effects for grab actions

### 7. Object Requirements

#### Grabbable Object Setup
```blueprint
# Required Components
- Static Mesh or Skeletal Mesh Component
- Collision Component (ConvexHull or Complex)
- Optional: Physics Body for physics-based grabs

# Required Tags
- "Grabbable" - Marks object as grabbable
- "PhysicsGrab" - Use physics-based grabbing
- "NoRotation" - Prevent rotation during grab

# Material Setup
- Material with emissive parameters for highlighting
- Support for outline shader if using advanced highlighting
```

### 8. Performance Optimizations
```blueprint
# Efficient Object Detection
- Use collision spheres instead of traces
- Cache grabbable objects in range
- Update detection only when needed
- Use object pooling for frequent grabs

# Physics Optimization
- Disable complex collision during grab
- Use simplified physics bodies
- Limit physics updates to active grabs
- Prevent excessive velocity calculations
```

### 9. Error Handling
```blueprint
# Grab Failures
- Object already grabbed by other hand
- Object outside of grab range
- Object not properly tagged
- Physics component missing

# Runtime Errors
- Lost object reference during grab
- Physics constraint breaks
- Hand tracking lost during grab
- Component destruction during grab

# Recovery Strategies
- Automatic release on tracking loss
- Fallback to basic grab if physics fails
- Clear state on object destruction
- User feedback for failed operations
```

## Testing Checklist
- [ ] Objects can be grabbed within range
- [ ] Grabbed objects follow hand movement smoothly
- [ ] Objects are released properly with correct velocity
- [ ] Multiple objects can be grabbed simultaneously (different hands)
- [ ] Visual feedback shows grab states correctly
- [ ] Physics-based grabs work without jitter
- [ ] Direct attachment grabs work reliably
- [ ] Error handling prevents crashes
- [ ] Performance remains stable during grabs
- [ ] Objects return to proper physics state after release

## Integration Dependencies
- Requires BP_GestureManager for gesture events
- Requires objects with "Grabbable" tag and proper setup
- Optional: BP_GestureVisualFeedback for enhanced effects
- Optional: Physics Handle Component for physics-based grabs
- Optional: Haptic feedback for tactile response