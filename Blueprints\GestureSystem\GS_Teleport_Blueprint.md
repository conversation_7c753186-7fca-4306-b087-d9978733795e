# GS_Teleport - Actual Blueprint Implementation

## Blueprint Creation Steps

### 1. Create Blueprint
1. **Content Browser** → Right-click → **Blueprint Class**
2. **Parent Class**: `Actor Component`
3. **Name**: `GS_Teleport`
4. **Location**: `Content/Blueprints/GestureSystem/`

## Variables Setup

### Create These Variables (Details Panel)
```cpp
// Teleport State
bIsAiming (Boolean) = false
  Category: "Teleport|State"
  Tooltip: "True when user is aiming for teleport"

bCanTeleport (Boolean) = true
  Category: "Teleport|State" 
  Tooltip: "Master switch for teleport functionality"

PinchStartPosition (Vector) = (0,0,0)
  Category: "Teleport|State"
  Tooltip: "World position where pinch gesture started"

CurrentTargetLocation (Vector) = (0,0,0)
  Category: "Teleport|State"
  Tooltip: "Current calculated teleport destination"

GestureManager (BP_GestureManager | Object Reference) = None
  Category: "Teleport|References"
  Tooltip: "Reference to gesture manager component"

// Configuration
TeleportRange (Float) = 1000.0 [Range: 100.0, 5000.0]
  Category: "Teleport|Configuration"
  Tooltip: "Maximum teleport distance in Unreal units"

TeleportDistanceMultiplier (Float) = 2.0 [Range: 0.5, 5.0]
  Category: "Teleport|Configuration" 
  Tooltip: "Multiplier for hand movement to teleport distance"

PinchThreshold (Float) = 0.8 [Range: 0.1, 1.0]
  Category: "Teleport|Configuration"
  Tooltip: "Minimum pinch strength required to trigger teleport"

UnpinchThreshold (Float) = 0.3 [Range: 0.1, 0.8]
  Category: "Teleport|Configuration"
  Tooltip: "Maximum pinch strength for teleport release"

// Visual Components
TeleportArcSpline (Spline Component | Object Reference) = None
  Category: "Teleport|Visual"
  Tooltip: "Spline component for teleport arc visualization"

TargetIndicatorMesh (Static Mesh Component | Object Reference) = None
  Category: "Teleport|Visual"
  Tooltip: "Mesh component for teleport target indicator"

bShowDebugInfo (Boolean) = true
  Category: "Teleport|Debug"
  Tooltip: "Show debug information during development"
```

## Components Setup (Components Panel)

### Add These Components
```cpp
1. Add Component → Spline Component
   Name: "TeleportArcSpline"
   
2. Add Component → Static Mesh Component  
   Name: "TargetIndicatorMesh"
   Static Mesh: Engine/BasicShapes/Plane (or custom target mesh)
   Material: Create M_TeleportTarget material
   
3. Add Component → Static Mesh Component
   Name: "ArcMeshComponent" 
   Static Mesh: Engine/BasicShapes/Cube (for arc visualization)
   Material: Create M_TeleportArc material
```

## Custom Events Setup

### Create These Custom Events
```cpp
OnTeleportAimStart (Custom Event)
  Inputs: StartPosition (Vector), bIsLeftHand (Boolean)
  Description: "Fired when teleport aiming begins"

OnTeleportAiming (Custom Event)  
  Inputs: TargetPosition (Vector), Progress (Float)
  Description: "Fired continuously while aiming"

OnTeleportExecute (Custom Event)
  Inputs: TargetPosition (Vector), StartPosition (Vector)
  Description: "Fired when teleport is executed"

OnTeleportCancel (Custom Event)
  Inputs: LastPosition (Vector)
  Description: "Fired when teleport is cancelled"

OnTeleportLocationValid (Custom Event)
  Inputs: Location (Vector)
  Description: "Fired when teleport location is valid"

OnTeleportLocationInvalid (Custom Event)
  Inputs: Location (Vector), Reason (String)
  Description: "Fired when teleport location is invalid"
```

## Event Graph Implementation

### BeginPlay Event Chain
```
[Event BeginPlay]
    ↓
[Get Owner] → [Get Component by Class] (BP_GestureManager) → [Set GestureManager]
    ↓
[IsValid] (GestureManager)
    ↓ True
    [Call Function] (GestureManager.RegisterWithManager)
      Input: Self Reference
    ↓
    [Initialize Visual Components] (Custom Function)
    ↓
    [Bind Gesture Events] (Custom Function)
    ↓
    [Print String] ("GS_Teleport Initialized" | Green | 2.0)
    
    False ↓
    [Print String] ("ERROR: No Gesture Manager Found" | Red | 5.0)
```

### Initialize Visual Components Function
```
[Initialize Visual Components] (Custom Function)
    ↓
[Get Component by Class] (Spline Component) → [Set TeleportArcSpline]
    ↓
[Get Component by Class] (Static Mesh Component) → [Set TargetIndicatorMesh]
    ↓
[Set Visibility] (TargetIndicatorMesh, False)
    ↓
[Set Visibility] (TeleportArcSpline, False)
    ↓
[Branch] (bShowDebugInfo)
    ↓ True
    [Print String] ("Teleport Visual Components Initialized" | Blue | 2.0)
```

### Bind Gesture Events Function  
```
[Bind Gesture Events] (Custom Function)
    ↓
[GestureManager] → [Bind Event to OnGestureDetected] → [OnPinchDetected] (Event)
    ↓
[GestureManager] → [Bind Event to OnGestureProgression] → [OnPinchProgression] (Event)
    ↓
[GestureManager] → [Bind Event to OnGestureEnded] → [OnPinchReleased] (Event)
```

### Pinch Detection Handler
```
[OnPinchDetected] (Event Dispatcher Bound)
  Inputs: GestureType (String), Strength (Float), bIsLeftHand (Boolean), Position (Vector)
    ↓
[Branch] (GestureType == "Pinch")
    ↓ True
    [Branch] (Strength >= PinchThreshold)
        ↓ True
        [Branch] (!bIsAiming AND bCanTeleport)
            ↓ True
            [Set bIsAiming] (True)
            ↓
            [Set PinchStartPosition] (Position)
            ↓
            [Show Teleport Arc] (Custom Function)
            ↓
            [Show Target Indicator] (Custom Function)
            ↓
            [OnTeleportAimStart] (Call Event)
              StartPosition: Position
              bIsLeftHand: bIsLeftHand
            ↓
            [Branch] (bShowDebugInfo)
                ↓ True
                [Print String] ("Teleport Aiming Started" | Yellow | 1.0)
```

### Pinch Progression Handler
```
[OnPinchProgression] (Event Dispatcher Bound)
  Inputs: GestureType (String), Progress (Float), CurrentHandPosition (Vector)
    ↓
[Branch] (GestureType == "Pinch" AND bIsAiming)
    ↓ True
    [Calculate Teleport Target] (Custom Function)
      Inputs: CurrentHandPosition, PinchStartPosition, TeleportDistanceMultiplier
      Output: CalculatedTarget (Vector)
    ↓
    [Validate Teleport Location] (Custom Function)
      Input: CalculatedTarget
      Output: bLocationValid (Boolean), ValidationReason (String)
    ↓
    [Branch] (bLocationValid)
        ↓ True
        [Set CurrentTargetLocation] (CalculatedTarget)
        ↓
        [Update Arc Visualization] (Custom Function)
          Inputs: PinchStartPosition, CalculatedTarget, True
        ↓
        [Update Target Indicator] (Custom Function)
          Inputs: CalculatedTarget, Green Color
        ↓
        [OnTeleportLocationValid] (Call Event)
          Location: CalculatedTarget
        
        False ↓
        [Update Target Indicator] (Custom Function)
          Inputs: CalculatedTarget, Red Color
        ↓
        [OnTeleportLocationInvalid] (Call Event)
          Location: CalculatedTarget
          Reason: ValidationReason
    ↓
    [OnTeleportAiming] (Call Event)
      TargetPosition: CalculatedTarget
      Progress: Progress
```

### Pinch Release Handler
```
[OnPinchReleased] (Event Dispatcher Bound)
  Inputs: GestureType (String), FinalStrength (Float), HandPosition (Vector)
    ↓
[Branch] (GestureType == "Pinch" AND bIsAiming)
    ↓ True
    [Branch] (FinalStrength <= UnpinchThreshold)
        ↓ True
        [Validate Teleport Location] (Custom Function)
          Input: CurrentTargetLocation
          Output: bLocationValid (Boolean)
        ↓
        [Branch] (bLocationValid)
            ↓ True
            [Execute Teleport] (Custom Function)
              Input: CurrentTargetLocation
            ↓
            [OnTeleportExecute] (Call Event)
              TargetPosition: CurrentTargetLocation
              StartPosition: PinchStartPosition
            
            False ↓
            [OnTeleportCancel] (Call Event)
              LastPosition: CurrentTargetLocation
    ↓
    [Hide Visual Elements] (Custom Function)
    ↓
    [Set bIsAiming] (False)
    ↓
    [Reset State Variables] (Custom Function)
```

## Custom Functions Implementation

### Calculate Teleport Target Function
```
[Calculate Teleport Target] (Custom Function)
  Inputs: CurrentHandPos (Vector), StartHandPos (Vector), Multiplier (Float)
  Outputs: TargetPosition (Vector)
    ↓
[Vector - Vector] (CurrentHandPos - StartHandPos) → HandDelta
    ↓
[Vector * Float] (HandDelta * Multiplier) → TeleportDelta
    ↓
[Get Owner] → [Cast to Pawn] → [Get Actor Forward Vector] → ForwardVector
    ↓
[Project Vector on to Plane] (TeleportDelta, ForwardVector) → ProjectedDelta
    ↓
[Vector + Vector] (StartHandPos + ProjectedDelta) → RawTarget
    ↓
[Vector Length] (ProjectedDelta) → Distance
    ↓
[Clamp Float] (Distance, 0.0, TeleportRange) → ClampedDistance
    ↓
[Normalize] (ProjectedDelta) → Direction
    ↓
[Vector * Float] (Direction * ClampedDistance) → FinalDelta
    ↓
[Vector + Vector] (StartHandPos + FinalDelta) → [Return Node] (TargetPosition)
```

### Validate Teleport Location Function
```
[Validate Teleport Location] (Custom Function)
  Inputs: TargetLocation (Vector)
  Outputs: bIsValid (Boolean), FailReason (String)
    ↓
[Vector + Vector] (TargetLocation + (0,0,200)) → TraceStart
    ↓
[Vector + Vector] (TargetLocation + (0,0,-200)) → TraceEnd
    ↓
[Line Trace by Channel] 
      Start: TraceStart
      End: TraceEnd  
      Trace Channel: Visibility
      → Hit (Boolean), HitResult (Hit Result)
    ↓
[Branch] (!Hit)
    ↓ True
    [Return Node] bIsValid: False, FailReason: "No Ground Found"
    
    False ↓
    [Break Hit Result] (HitResult) → HitLocation, HitNormal
    ↓
    [Dot Product] (HitNormal • (0,0,1)) → SurfaceAngle
    ↓
    [Branch] (SurfaceAngle > 0.7)
        ↓ True
        [Sphere Trace by Channel]
          Start: HitLocation + (0,0,100)
          End: HitLocation + (0,0,100)
          Radius: 50.0
          Trace Channel: Pawn
          → ObstacleHit (Boolean)
        ↓
        [Branch] (!ObstacleHit)
            ↓ True
            [Vector Distance] (TargetLocation, PinchStartPosition) → Distance  
            ↓
            [Branch] (Distance <= TeleportRange)
                ↓ True
                [Return Node] bIsValid: True, FailReason: ""
                
                False ↓
                [Return Node] bIsValid: False, FailReason: "Out of Range"
            
            False ↓
            [Return Node] bIsValid: False, FailReason: "Obstacle Detected"
        
        False ↓
        [Return Node] bIsValid: False, FailReason: "Surface Too Steep"
```

### Update Arc Visualization Function
```
[Update Arc Visualization] (Custom Function)
  Inputs: StartPos (Vector), EndPos (Vector), bIsValid (Boolean)
    ↓
[IsValid] (TeleportArcSpline)
    ↓ True
    [Clear Spline Points] (TeleportArcSpline)
    ↓
    [Vector Distance] (StartPos, EndPos) → ArcDistance
    ↓
[For Loop] (0 to 20) → Index
    ↓
    [Float / Float] (Index / 20.0) → Alpha
    ↓
    [Lerp Vector] (StartPos, EndPos, Alpha) → BasePoint
    ↓
    [Float * Float] (Alpha * (1.0 - Alpha) * 4.0 * ArcDistance * 0.3) → ArcHeight
    ↓
    [Vector + Vector] (BasePoint + (0,0,ArcHeight)) → ArcPoint
    ↓
    [Add Spline Point] (TeleportArcSpline, ArcPoint, World)
    ↓
[Update Spline] (TeleportArcSpline)
    ↓
[Set Visibility] (TeleportArcSpline, True)
    ↓
[Branch] (bIsValid)
    ↓ True
    [Set Vector Parameter Value] (ArcMaterial, "Color", Green)
    
    False ↓
    [Set Vector Parameter Value] (ArcMaterial, "Color", Red)
```

### Execute Teleport Function
```
[Execute Teleport] (Custom Function)
  Input: TargetPosition (Vector)
    ↓
[Get Owner] → [Cast to Pawn] → VRPawn
    ↓
[IsValid] (VRPawn)
    ↓ True
    [Set Actor Location] (VRPawn, TargetPosition)
    ↓
    [Branch] (bShowDebugInfo)
        ↓ True
        [Print String] ("Teleported to: " + TargetPosition.ToString() | Green | 2.0)
    ↓
    [Play Teleport Effects] (Custom Function)
      Input: TargetPosition
```

### Visual Management Functions

#### Show Teleport Arc Function
```
[Show Teleport Arc] (Custom Function)
    ↓
[Set Visibility] (TeleportArcSpline, True)
    ↓
[Set Visibility] (ArcMeshComponent, True)
```

#### Show Target Indicator Function  
```
[Show Target Indicator] (Custom Function)
    ↓
[Set Visibility] (TargetIndicatorMesh, True)
    ↓
[Set Scalar Parameter Value] (TargetMaterial, "Opacity", 0.8)
```

#### Hide Visual Elements Function
```
[Hide Visual Elements] (Custom Function)
    ↓
[Set Visibility] (TeleportArcSpline, False)
    ↓
[Set Visibility] (TargetIndicatorMesh, False)
    ↓
[Set Visibility] (ArcMeshComponent, False)
```

#### Update Target Indicator Function
```
[Update Target Indicator] (Custom Function)
  Inputs: Location (Vector), Color (Linear Color)
    ↓
[Set World Location] (TargetIndicatorMesh, Location)
    ↓
[Create Dynamic Material Instance] (TargetMaterial) → DynamicMaterial
    ↓
[Set Vector Parameter Value] (DynamicMaterial, "BaseColor", Color)
    ↓
[Set Material] (TargetIndicatorMesh, 0, DynamicMaterial)
```

## Material Setup Required

### Create These Materials

#### M_TeleportArc Material
```cpp
Material Properties:
- Blend Mode: Translucent  
- Shading Model: Unlit
- Two Sided: True

Material Graph:
[Vector Parameter] "Color" (Default: Blue) 
    → [Multiply] × [Scalar Parameter] "Intensity" (Default: 2.0)
    → [Emissive Color]

[Scalar Parameter] "Opacity" (Default: 0.6)
    → [Opacity]
```

#### M_TeleportTarget Material  
```cpp
Material Properties:
- Blend Mode: Translucent
- Shading Model: Unlit

Material Graph:
[Vector Parameter] "BaseColor" (Default: Green)
    → [Multiply] × [Scalar Parameter] "Brightness" (Default: 1.5)
    → [Emissive Color]
    
[Scalar Parameter] "Opacity" (Default: 0.8)
    → [Sine] (Time * 3.0) × 0.3 + 0.7  // Pulsing effect
    → [Opacity]
```

## Integration Notes

### Add to VR Pawn
```cpp
1. In VR Pawn Blueprint (BP_XRPassthroughPawn):
   - Add Component → GS_Teleport
   - Ensure BP_GestureManager is also added
   - Compile and test
```

### Testing Sequence
```cpp
1. Verify gesture manager detects pinch
2. Check arc visualization appears
3. Test target location validation
4. Confirm teleport execution
5. Validate visual cleanup
```

This blueprint provides complete pinch-to-teleport functionality with visual feedback and validation.