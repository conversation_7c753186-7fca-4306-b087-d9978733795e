# GS_Teleport Implementation Guide

## Overview
The GS_Teleport component handles pinch-to-teleport functionality with visual feedback. Users pinch and hold to aim, then release to teleport to the target location.

## Blueprint Creation Steps

### 1. Create Blueprint Component
1. **Content Browser** > Right-click > **Blueprint Class**
2. **Parent Class**: Select **Actor Component**
3. **Name**: `GS_Teleport`
4. **Save Location**: `Blueprints/GestureSystem/`

### 2. Component Setup

#### Variables to Add
```yaml
# Teleport State Management
- Name: bIsAiming
  Type: Boolean
  Default: false
  Category: "Teleport|State"
  Tooltip: "True when user is aiming for teleport"

- Name: bCanTeleport
  Type: Boolean  
  Default: true
  Category: "Teleport|State"
  Tooltip: "Master switch for teleport functionality"

- Name: PinchStartPosition
  Type: Vector
  Category: "Teleport|State"
  Tooltip: "World position where pinch gesture started"

- Name: CurrentTargetLocation
  Type: Vector
  Category: "Teleport|State" 
  Tooltip: "Current calculated teleport destination"

# Configuration Settings
- Name: TeleportRange
  Type: Float
  Default: 1000.0
  Range: [100.0, 5000.0]
  Category: "Teleport|Configuration"
  Tooltip: "Maximum teleport distance in Unreal units"

- Name: TeleportDistanceMultiplier
  Type: Float
  Default: 2.0
  Range: [0.5, 5.0]
  Category: "Teleport|Configuration"
  Tooltip: "Multiplier for hand movement to teleport distance"

- Name: PinchThreshold
  Type: Float
  Default: 0.8
  Range: [0.1, 1.0]
  Category: "Teleport|Configuration"
  Tooltip: "Minimum pinch strength required to trigger teleport"

- Name: UnpinchThreshold
  Type: Float
  Default: 0.3
  Range: [0.1, 0.8]
  Category: "Teleport|Configuration"
  Tooltip: "Maximum pinch strength for teleport release"

# Visual Feedback References
- Name: TeleportArcComponent
  Type: Spline Component (Object Reference)
  Category: "Teleport|Visual"
  Tooltip: "Spline component for teleport arc visualization"

- Name: TargetIndicatorMesh
  Type: Static Mesh Component (Object Reference)
  Category: "Teleport|Visual"
  Tooltip: "Mesh component for teleport target indicator"

- Name: ArcMaterial
  Type: Material Interface (Object Reference)
  Category: "Teleport|Visual"
  Tooltip: "Material for teleport arc visualization"
```

#### Custom Events to Create
```yaml
# Core Teleport Events
- Event: OnTeleportAimStart
  Parameters: [FVector StartPosition, bool bIsLeftHand]
  Description: "Fired when teleport aiming begins"

- Event: OnTeleportAiming
  Parameters: [FVector TargetPosition, float Progress]
  Description: "Fired continuously while aiming (for arc updates)"

- Event: OnTeleportExecute
  Parameters: [FVector TargetPosition, FVector StartPosition]
  Description: "Fired when teleport is executed"

- Event: OnTeleportCancel
  Parameters: [FVector LastPosition]
  Description: "Fired when teleport is cancelled"

# Validation Events
- Event: OnTeleportLocationValid
  Parameters: [FVector Location]
  Description: "Fired when teleport location is valid"

- Event: OnTeleportLocationInvalid
  Parameters: [FVector Location, FString Reason]
  Description: "Fired when teleport location is invalid"
```

### 3. Event Graph Implementation

#### Component Initialization
```blueprint
Event BeginPlay
  ↓
[Get Gesture Manager Reference]
  ↓
[Register with Gesture Manager]
  ↓
[Initialize Visual Components]
  → [Setup Teleport Arc]
  → [Setup Target Indicator]
  → [Hide Visual Elements Initially]
  ↓
[Bind to Gesture Manager Events]
  → [OnGestureDetected: "Pinch"]
  → [OnGestureProgression: "Pinch"]
  → [OnGestureEnded: "Pinch"]
  ↓
[Print String: "GS_Teleport Initialized"]
```

#### Pinch Detection Handler
```blueprint
Event: OnPinchDetected (from Gesture Manager)
Parameters: [float Strength, bool bIsLeftHand, FVector HandPosition]
  ↓
[Branch: Strength >= PinchThreshold]
  → True:
    ↓
    [Branch: !bIsAiming && bCanTeleport]
      → True:
        ↓
        [Set bIsAiming = true]
        ↓
        [Set PinchStartPosition = HandPosition]
        ↓
        [Show Teleport Arc]
        ↓
        [Show Target Indicator]
        ↓
        [Fire OnTeleportAimStart]
        ↓
        [Start Teleport Target Calculation]
```

#### Pinch Progression Handler
```blueprint
Event: OnPinchProgression (from Gesture Manager)  
Parameters: [float Progress, FVector CurrentHandPosition]
  ↓
[Branch: bIsAiming]
  → True:
    ↓
    [Calculate Teleport Target]
      → Input: [CurrentHandPosition, PinchStartPosition, TeleportDistanceMultiplier]
      → Output: [CalculatedTarget]
    ↓
    [Validate Teleport Location]
      → [Line Trace to Ground]
      → [Check for Obstacles]
      → [Verify Within Range]
    ↓
    [Branch: Location Valid]
      → True:
        ↓
        [Set CurrentTargetLocation = CalculatedTarget]
        ↓
        [Update Arc Visualization]
        ↓
        [Update Target Indicator (Green)]
        ↓
        [Fire OnTeleportLocationValid]
      → False:
        ↓
        [Update Target Indicator (Red)]
        ↓
        [Fire OnTeleportLocationInvalid]
    ↓
    [Fire OnTeleportAiming]
```

#### Pinch Release Handler
```blueprint
Event: OnPinchEnded (from Gesture Manager)
Parameters: [float FinalStrength, FVector HandPosition]
  ↓
[Branch: bIsAiming]
  → True:
    ↓
    [Branch: FinalStrength <= UnpinchThreshold]
      → True:
        ↓
        [Validate Final Target Location]
        ↓
        [Branch: Location Valid]
          → True:
            ↓
            [Execute Teleport]
              → [Get Owner (VR Pawn)]
              → [Set Actor Location: CurrentTargetLocation]
              → [Add Teleport Effects]
            ↓
            [Fire OnTeleportExecute]
          → False:
            ↓
            [Fire OnTeleportCancel]
        ↓
        [Hide Visual Elements]
        ↓
        [Set bIsAiming = false]
        ↓
        [Reset State Variables]
```

### 4. Custom Functions to Implement

#### CalculateTeleportTarget Function
```blueprint
Function: CalculateTeleportTarget
Parameters:
  - CurrentHandPos: Vector
  - StartHandPos: Vector  
  - Multiplier: Float
Return: Vector

Logic:
  ↓
[Calculate Hand Movement Delta]
  → HandDelta = CurrentHandPos - StartHandPos
  ↓
[Apply Distance Multiplier]
  → TeleportDelta = HandDelta * Multiplier
  ↓
[Get VR Pawn Forward Vector]
  ↓
[Calculate Target Position]
  → StartPos + (TeleportDelta projected onto forward plane)
  ↓
[Clamp to Maximum Range]
  → Distance = min(TeleportRange, calculated distance)
  ↓
[Return Final Target Position]
```

#### ValidateTeleportLocation Function
```blueprint
Function: ValidateTeleportLocation
Parameters:
  - TargetLocation: Vector
Return: Boolean

Logic:
  ↓
[Line Trace from Above Target to Ground]
  → Start: TargetLocation + (0,0,200)
  → End: TargetLocation + (0,0,-200)
  → Channel: Visibility
  ↓
[Check Hit Result]
  → No Hit: Return False (no ground)
  → Hit: Continue
  ↓
[Check Surface Angle]
  → Surface Normal dot Up Vector > 0.7 (walkable)
  ↓
[Check for Obstacles at Player Height]
  → Sphere Trace at target location
  → Radius: Player capsule radius
  ↓
[Check Distance from Start]
  → Within TeleportRange
  ↓
[Return Validation Result]
```

#### UpdateArcVisualization Function
```blueprint
Function: UpdateArcVisualization
Parameters:
  - StartPos: Vector
  - EndPos: Vector
  - bIsValid: Boolean

Logic:
  ↓
[Calculate Arc Points]
  → Generate parabolic arc from start to end
  → Number of points: 20
  ↓
[Update Spline Component]
  → Clear existing points
  → Add new arc points
  → Set tangent vectors for smooth curve
  ↓
[Update Arc Material]
  → Valid: Green tint
  → Invalid: Red tint
  ↓
[Update Target Indicator]
  → Position at EndPos
  → Material based on bIsValid
```

### 5. Visual Feedback Setup

#### Teleport Arc Configuration
```blueprint
# Spline Component Setup
- Spline Points: Generate dynamically based on hand movement
- Spline Material: Semi-transparent with animated flow texture
- Width: 2.0 units
- Segments: 20 points for smooth curve

# Arc Calculation
- Start Point: VR Pawn head position  
- End Point: Calculated target location
- Arc Height: Based on distance (parabolic curve)
- Update Rate: Every frame while aiming
```

#### Target Indicator Setup
```blueprint
# Static Mesh Component
- Mesh: Simple circle or target reticle
- Scale: 1.5x1.5x0.1
- Material: 
  - Valid: Green emissive
  - Invalid: Red emissive
  - Animation: Gentle pulsing effect
- Visibility: Only when aiming
```

### 6. Integration Points

#### With Gesture Manager
- Registers for pinch gesture events
- Provides teleport-specific gesture processing
- Reports teleport state changes

#### With VR Pawn
- Executes actual teleportation by moving pawn
- Accesses pawn's collision capsule for validation
- Uses pawn's forward vector for direction calculations

#### With Visual Feedback System
- Triggers particle effects on teleport
- Provides haptic feedback through controllers
- Manages UI indicators for teleport state

### 7. Performance Optimizations
```blueprint
# Efficient Updates
- Only calculate arc when aiming
- Use object pooling for visual effects
- Limit validation traces to reasonable frequency
- Cache frequently accessed components

# LOD System
- Reduce arc resolution at longer distances
- Simplify target indicator when far from player
- Use timer-based updates instead of tick when possible
```

### 8. Error Handling & Edge Cases
```blueprint
# Tracking Loss
- Cancel teleport if hand tracking is lost
- Provide user feedback about tracking issues
- Automatically hide visual elements

# Invalid Locations
- Clear visual feedback for impossible teleports
- Provide audio/haptic feedback for invalid attempts
- Maintain user context about why teleport failed

# System Failures
- Graceful degradation if components fail
- Fallback to basic teleport without visual feedback
- Error logging for debugging
```

## Testing Checklist
- [ ] Pinch gesture triggers teleport aiming
- [ ] Arc visualization appears and updates smoothly
- [ ] Target indicator shows valid/invalid states correctly
- [ ] Teleport executes on pinch release
- [ ] Visual feedback hides after teleport/cancel
- [ ] Performance maintains 90+ FPS during use
- [ ] Works with both left and right hands
- [ ] Validation prevents teleporting to invalid locations
- [ ] Range limiting works correctly
- [ ] Error handling responds to edge cases

## Integration Dependencies
- Requires BP_GestureManager for gesture events
- Requires VR Pawn with movement capability
- Optional: BP_GestureVisualFeedback for enhanced effects
- Optional: Haptic feedback components for tactile response