# BP_StartupOptimizer - Actual Blueprint Implementation

## Blueprint Creation Steps

### 1. Create Blueprint
1. **Content Browser** → Right-click → **Blueprint Class**
2. **Parent Class**: `Actor`
3. **Name**: `BP_StartupOptimizer`
4. **Location**: `Content/Blueprints/Optimization/`

## Variables Setup

### Create These Variables (Details Panel)
```cpp
// Optimization State
bOptimizationApplied (Boolean) = false
  Category: "Optimization|State"
  Tooltip: "True when optimization has been applied"

bEnableDebugLogging (Boolean) = true
  Category: "Optimization|Debug"
  Tooltip: "Enable detailed logging of applied optimizations"

OptimizationStartTime (Float) = 0.0
  Category: "Optimization|Debug"
  Tooltip: "Time when optimization process started"

// VR Settings
TargetFrameRate (Integer) = 90 [Range: 72, 120]
  Category: "Optimization|VR"
  Tooltip: "Target VR frame rate"

bEnableMixedReality (Boolean) = true
  Category: "Optimization|VR"
  Tooltip: "Enable mixed reality passthrough"

ScreenPercentage (Float) = 100.0 [Range: 50.0, 150.0]
  Category: "Optimization|Rendering"
  Tooltip: "Screen percentage for resolution scaling"

// Performance Settings
StreamingPoolSize (Integer) = 3000 [Range: 1000, 8000]
  Category: "Optimization|Performance"
  Tooltip: "Texture streaming pool size in MB"

ViewDistanceScale (Float) = 2.0 [Range: 0.5, 4.0]
  Category: "Optimization|Performance"  
  Tooltip: "View distance scaling factor"

bEnableDLSS (Boolean) = false
  Category: "Optimization|DLSS"
  Tooltip: "Enable DLSS if supported (UE5.4 required)"

// Console Commands
ConsoleCommands (Array | String)
  Category: "Optimization|Commands"
  Tooltip: "Array of console commands to execute"

// Performance Monitoring
BaselineFPS (Float) = 0.0
  Category: "Optimization|Monitoring"
  Tooltip: "FPS before optimization"

OptimizedFPS (Float) = 0.0
  Category: "Optimization|Monitoring"
  Tooltip: "FPS after optimization"
```

## Custom Events Setup

### Create These Custom Events
```cpp  
OnOptimizationStarted (Custom Event)
  Description: "Fired when optimization process begins"

OnOptimizationComplete (Custom Event)
  Inputs: Duration (Float), bSuccess (Boolean)
  Description: "Fired when optimization process completes"

OnCommandExecuted (Custom Event)
  Inputs: Command (String), bSuccess (Boolean)
  Description: "Fired after each console command execution"

OnPerformanceBaseline (Custom Event)
  Inputs: FPS (Float), FrameTime (Float)
  Description: "Fired with initial performance metrics"

OnPerformanceImprovement (Custom Event)
  Inputs: OldFPS (Float), NewFPS (Float), Improvement (Float)
  Description: "Fired with performance improvement data"
```

## Event Graph Implementation

### BeginPlay Event Chain
```
[Event BeginPlay]
    ↓
[Set OptimizationStartTime] (Get Game Time in Seconds)
    ↓
[OnOptimizationStarted] (Call Event)
    ↓
[Branch] (!bOptimizationApplied)
    ↓ True
    [Capture Baseline Performance] (Custom Function)
    ↓
    [Delay] (1.0) // Allow system to stabilize
    ↓
    [Initialize Console Commands Array] (Custom Function)
    ↓
    [Execute VR Optimizations] (Custom Function)
    ↓
    [Execute Rendering Optimizations] (Custom Function)
    ↓
    [Execute Performance Optimizations] (Custom Function)
    ↓
    [Execute DLSS Configuration] (Custom Function)
    ↓
    [Execute Mixed Reality Setup] (Custom Function)
    ↓
    [Delay] (2.0) // Allow optimizations to take effect
    ↓
    [Validate Applied Settings] (Custom Function)
    ↓
    [Set bOptimizationApplied] (True)
    ↓
    [Capture Final Performance] (Custom Function)
    ↓
    [Calculate Performance Improvement] (Custom Function)
    ↓
    [OnOptimizationComplete] (Call Event)
      Duration: (Get Game Time in Seconds - OptimizationStartTime)
      bSuccess: True
    ↓
    [Branch] (bEnableDebugLogging)
        ↓ True
        [Print Optimization Summary] (Custom Function)
    
    False ↓
    [Print String] ("Optimization already applied" | Yellow | 2.0)
```

## Custom Functions Implementation

### Initialize Console Commands Array Function
```
[Initialize Console Commands Array] (Custom Function)
    ↓
[Clear Array] (ConsoleCommands)
    ↓
[Add Item] (ConsoleCommands, "vr.bEnableHMD 1")
    ↓
[Add Item] (ConsoleCommands, "vr.bEnableStereo 1")
    ↓
[Add Item] (ConsoleCommands, "r.ForwardShading 1")
    ↓
[Add Item] (ConsoleCommands, "r.AntiAliasingMethod 1")
    ↓
[Add String to Array] (ConsoleCommands, "r.ScreenPercentage " + ScreenPercentage)
    ↓
[Add String to Array] (ConsoleCommands, "r.Streaming.PoolSize " + StreamingPoolSize)
    ↓
[Branch] (bEnableDebugLogging)
    ↓ True
    [Print String] ("Console Commands Array Initialized" | Blue | 2.0)
```

### Execute VR Optimizations Function
```
[Execute VR Optimizations] (Custom Function)
    ↓
[Execute Console Command] (Custom Function)
      Input: "vr.bEnableHMD 1"
    ↓
[Execute Console Command] (Custom Function)  
      Input: "vr.bEnableStereo 1"
    ↓
[Execute Console Command] (Custom Function)
      Input: "vr.WorldToMetersScale 100"
    ↓
[Execute Console Command] (Custom Function)
      Input: "vr.TrackingOrigin 1"
    ↓
[Branch] (bEnableDebugLogging)
    ↓ True
    [Print String] ("VR Optimizations Applied" | Green | 2.0)
```

### Execute Rendering Optimizations Function
```
[Execute Rendering Optimizations] (Custom Function)
    ↓
[String + String] ("r.ScreenPercentage " + Float to String(ScreenPercentage)) → ScreenPercentageCmd
    ↓
[Execute Console Command] (Custom Function)
      Input: ScreenPercentageCmd
    ↓
[Execute Console Command] (Custom Function)
      Input: "r.ForwardShading 1"
    ↓
[Execute Console Command] (Custom Function)
      Input: "r.AntiAliasingMethod 1"
    ↓
[Execute Console Command] (Custom Function)
      Input: "r.MotionBlurQuality 0"
    ↓
[Execute Console Command] (Custom Function)
      Input: "r.Tonemapper.Sharpen 1.0"
    ↓
[Execute Console Command] (Custom Function)
      Input: "r.CustomDepth 3"
    ↓
[Branch] (bEnableDebugLogging)
    ↓ True
    [Print String] ("Rendering Optimizations Applied" | Green | 2.0)
```

### Execute Performance Optimizations Function
```
[Execute Performance Optimizations] (Custom Function)
    ↓
[String + String] ("r.Streaming.PoolSize " + Integer to String(StreamingPoolSize)) → PoolSizeCmd
    ↓
[Execute Console Command] (Custom Function)
      Input: PoolSizeCmd
    ↓
[String + String] ("r.ViewDistanceScale " + Float to String(ViewDistanceScale)) → ViewDistanceCmd
    ↓
[Execute Console Command] (Custom Function)
      Input: ViewDistanceCmd
    ↓
[Execute Console Command] (Custom Function)
      Input: "r.ForceLOD 0"
    ↓
[Execute Console Command] (Custom Function)
      Input: "r.HZBOcclusion 1"
    ↓
[Execute Console Command] (Custom Function)
      Input: "r.Streaming.Boost 1"
    ↓
[Execute Console Command] (Custom Function)
      Input: "r.Lumen.DiffuseIndirect.Allow 0"
    ↓
[Execute Console Command] (Custom Function)
      Input: "r.Lumen.Reflections.Allow 0"
    ↓
[Execute Console Command] (Custom Function)
      Input: "r.Nanite.Enable 0"
    ↓
[Execute Console Command] (Custom Function)
      Input: "r.VirtualShadowMaps.Enable 0"
    ↓
[Branch] (bEnableDebugLogging)
    ↓ True
    [Print String] ("Performance Optimizations Applied" | Green | 2.0)
```

### Execute Mixed Reality Setup Function
```
[Execute Mixed Reality Setup] (Custom Function)
    ↓
[Branch] (bEnableMixedReality)
    ↓ True
    [Execute Console Command] (Custom Function)
          Input: "xr.OpenXREnvironmentBlendMode 3"
    ↓
    [Execute Console Command] (Custom Function)  
          Input: "r.SceneColorFormat 3"
    ↓
    [Execute Console Command] (Custom Function)
          Input: "r.PostProcessAAQuality 6"
    ↓
    [Branch] (bEnableDebugLogging)
        ↓ True
        [Print String] ("Mixed Reality Mode Enabled" | Cyan | 2.0)
    
    False ↓
    [Execute Console Command] (Custom Function)
          Input: "xr.OpenXREnvironmentBlendMode 1"
    ↓
    [Branch] (bEnableDebugLogging)
        ↓ True
        [Print String] ("Opaque VR Mode Set" | Cyan | 2.0)
```

### Execute DLSS Configuration Function
```
[Execute DLSS Configuration] (Custom Function)
    ↓
[Branch] (bEnableDLSS)
    ↓ True
    [Execute Console Command] (Custom Function)
          Input: "r.NGX.DLSS.Enable 1"
    ↓
    [Delay] (0.5) // Allow DLSS to initialize
    ↓
    [Execute Console Command] (Custom Function)
          Input: "r.NGX.DLSS.Quality 2"
    ↓
    [Execute Console Command] (Custom Function)
          Input: "r.NGX.DLSS.Sharpness 0.5"
    ↓
    [Branch] (bEnableDebugLogging)
        ↓ True
        [Print String] ("DLSS Configuration Applied" | Purple | 2.0)
    
    False ↓
    [Branch] (bEnableDebugLogging)
        ↓ True
        [Print String] ("DLSS Disabled" | Gray | 2.0)
```

### Execute Console Command Function
```
[Execute Console Command] (Custom Function)
  Inputs: Command (String)
  Outputs: bSuccess (Boolean)
    ↓
[Get Player Controller] (0) → PlayerController
    ↓
[Branch] (IsValid PlayerController)
    ↓ True
    [Execute Console Command] (PlayerController, Command)
    ↓
    [OnCommandExecuted] (Call Event)
          Command: Command
          bSuccess: True
    ↓
    [Branch] (bEnableDebugLogging)
        ↓ True
        [Print String] ("Executed: " + Command | White | 1.0)
    ↓
    [Return Node] bSuccess: True
    
    False ↓
    [Print String] ("ERROR: No Player Controller for command: " + Command | Red | 3.0)
    ↓
    [OnCommandExecuted] (Call Event)
          Command: Command
          bSuccess: False
    ↓
    [Return Node] bSuccess: False
```

### Capture Baseline Performance Function
```
[Capture Baseline Performance] (Custom Function)
    ↓
[Execute Console Command] (Custom Function)
      Input: "stat fps"
    ↓
[Delay] (1.0) // Allow stat to update
    ↓
[Get Current FPS] (Custom Function) → CurrentFPS
    ↓
[Set BaselineFPS] (CurrentFPS)
    ↓
[OnPerformanceBaseline] (Call Event)
      FPS: CurrentFPS
      FrameTime: (1000.0 / CurrentFPS)
    ↓
[Branch] (bEnableDebugLogging)
    ↓ True
    [Print String] ("Baseline FPS: " + Float to String(CurrentFPS) | Yellow | 2.0)
```

### Capture Final Performance Function
```
[Capture Final Performance] (Custom Function)
    ↓
[Delay] (2.0) // Allow optimizations to stabilize
    ↓
[Get Current FPS] (Custom Function) → FinalFPS
    ↓
[Set OptimizedFPS] (FinalFPS)
    ↓
[Branch] (bEnableDebugLogging)
    ↓ True
    [Print String] ("Final FPS: " + Float to String(FinalFPS) | Green | 2.0)
```

### Calculate Performance Improvement Function
```
[Calculate Performance Improvement] (Custom Function)
    ↓
[Float - Float] (OptimizedFPS - BaselineFPS) → FPSImprovement
    ↓
[Float / Float] (FPSImprovement / BaselineFPS * 100.0) → PercentImprovement
    ↓
[OnPerformanceImprovement] (Call Event)
      OldFPS: BaselineFPS
      NewFPS: OptimizedFPS
      Improvement: PercentImprovement
    ↓
[Branch] (bEnableDebugLogging)
    ↓ True
    [String + String] ("Performance Improvement: " + Float to String(FPSImprovement) + " FPS (" + Float to String(PercentImprovement) + "%)") → ImprovementText
    ↓
    [Print String] (ImprovementText | Green | 5.0)
```

### Get Current FPS Function (Approximation)
```
[Get Current FPS] (Custom Function)
  Outputs: CurrentFPS (Float)
    ↓
[Get World Delta Seconds] → DeltaTime
    ↓
[Branch] (DeltaTime > 0.0)
    ↓ True
    [Float / Float] (1.0 / DeltaTime) → CalculatedFPS
    ↓
    [Clamp Float] (CalculatedFPS, 1.0, 200.0) → ClampedFPS
    ↓
    [Return Node] ClampedFPS
    
    False ↓
    [Return Node] 60.0 // Default fallback
```

### Validate Applied Settings Function
```
[Validate Applied Settings] (Custom Function)
    ↓
[Execute Console Command] (Custom Function)
      Input: "stat unit"
    ↓
[Get Current FPS] (Custom Function) → ValidationFPS
    ↓
[Branch] (ValidationFPS >= (TargetFrameRate * 0.8))
    ↓ True
    [Branch] (bEnableDebugLogging)
        ↓ True
        [Print String] ("Performance Target Met: " + Float to String(ValidationFPS) + "/" + Integer to String(TargetFrameRate) + " FPS" | Green | 3.0)
    
    False ↓
    [Branch] (bEnableDebugLogging)
        ↓ True
        [Print String] ("WARNING: Performance below target: " + Float to String(ValidationFPS) + "/" + Integer to String(TargetFrameRate) + " FPS" | Orange | 5.0)
```

### Print Optimization Summary Function
```
[Print Optimization Summary] (Custom Function)
    ↓
[Print String] ("=== MVS VR Optimization Summary ===" | White | 10.0)
    ↓
[Print String] ("Baseline FPS: " + Float to String(BaselineFPS) | Yellow | 8.0)
    ↓
[Print String] ("Optimized FPS: " + Float to String(OptimizedFPS) | Green | 8.0)
    ↓
[Float - Float] (OptimizedFPS - BaselineFPS) → Improvement
    ↓
[Print String] ("Improvement: " + Float to String(Improvement) + " FPS" | Cyan | 8.0)
    ↓
[Print String] ("Target Frame Rate: " + Integer to String(TargetFrameRate) + " FPS" | White | 8.0)
    ↓
[Print String] ("Mixed Reality: " + Boolean to String(bEnableMixedReality) | White | 8.0)
    ↓
[Print String] ("DLSS Enabled: " + Boolean to String(bEnableDLSS) | White | 8.0)
    ↓
[Get Game Time in Seconds] → CurrentTime
    ↓
[Float - Float] (CurrentTime - OptimizationStartTime) → TotalTime
    ↓
[Print String] ("Optimization Duration: " + Float to String(TotalTime) + "s" | White | 8.0)
    ↓
[Print String] ("=== Optimization Complete ===" | Green | 10.0)
```

## Public Interface Functions

### Manual Optimization Control
```cpp
[ReapplyOptimizations] (Custom Function | CallInEditor: true)
  Description: "Manually reapply all optimizations"
    ↓
[Set bOptimizationApplied] (False)
    ↓
[Call Event BeginPlay Logic]

[ToggleMixedReality] (Custom Function)
  Description: "Toggle mixed reality mode on/off"
    ↓
[Set bEnableMixedReality] (!bEnableMixedReality)
    ↓
[Execute Mixed Reality Setup] (Custom Function)

[SetTargetFrameRate] (Custom Function)
  Inputs: NewFrameRate (Integer)
  Description: "Change target frame rate and reapply optimizations"
    ↓
[Set TargetFrameRate] (NewFrameRate)
    ↓
[Execute Performance Optimizations] (Custom Function)
```

## Console Commands Interface

### Add These Console Command Bindings (Optional)
```cpp
// These can be called from the console during gameplay
"mvs.optimizer.status"     → Print current optimization status
"mvs.optimizer.reapply"    → Call ReapplyOptimizations
"mvs.optimizer.benchmark"  → Run performance benchmark
"mvs.optimizer.fps <int>"  → Set target frame rate
"mvs.optimizer.mr <bool>"  → Toggle mixed reality
```

## Level Integration

### Place in Level
1. Drag `BP_StartupOptimizer` into any level
2. Set desired configuration in Details Panel
3. Optimization runs automatically on level start
4. Monitor output log for results

### Multiple Instances
- Only place **one** BP_StartupOptimizer per level
- Multiple instances will conflict with each other
- Use level-specific configurations if needed

## Performance Monitoring

### Built-in Monitoring
The optimizer includes automatic performance monitoring:
- Baseline FPS capture before optimization
- Final FPS capture after optimization  
- Performance improvement calculation
- Target frame rate validation

### Debug Output
When `bEnableDebugLogging` is enabled:
- Each console command execution logged
- Performance metrics displayed on screen
- Optimization summary printed at completion
- Warning messages for performance issues

## Testing and Validation

### Verification Steps
1. **Place optimizer in level**
2. **Play in VR Preview** and monitor console output
3. **Check FPS improvement** in optimization summary
4. **Verify target frame rate** is maintained
5. **Test mixed reality** if enabled
6. **Validate visual quality** after optimizations

### Performance Expectations
- **Baseline**: 60-80 FPS typical in unoptimized VR
- **Target**: 90+ FPS after optimization
- **Improvement**: 10-30 FPS gain typical
- **Stability**: Consistent frame times without hitches

This blueprint provides comprehensive VR performance optimization with detailed monitoring and validation for the MVS gesture control system.