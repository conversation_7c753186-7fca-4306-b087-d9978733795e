# BP_StartupOptimizer Implementation Guide

## Overview
The BP_StartupOptimizer automatically configures VR-specific performance settings and optimizations on level start. It executes console commands to maximize frame rate and ensure optimal VR experience on Varjo XR-4.

## Blueprint Creation Steps

### 1. Create Blueprint Actor
1. **Content Browser** > Right-click > **Blueprint Class**
2. **Parent Class**: Select **Actor**
3. **Name**: `BP_StartupOptimizer`
4. **Save Location**: `Blueprints/Optimization/`

### 2. Component Setup

#### Variables to Add
```yaml
# Optimization State
- Name: bOptimizationApplied
  Type: Boolean
  Default: false
  Category: "Optimization|State"
  Tooltip: "True when optimization has been applied"

- Name: bEnableDebugLogging
  Type: Boolean
  Default: true
  Category: "Optimization|Debug"
  Tooltip: "Enable detailed logging of applied optimizations"

- Name: OptimizationStartTime
  Type: Float
  Category: "Optimization|Debug"
  Tooltip: "Time when optimization process started"

# VR Settings
- Name: TargetFrameRate
  Type: Integer
  Default: 90
  Range: [72, 120]
  Category: "Optimization|VR"
  Tooltip: "Target VR frame rate"

- Name: bEnableMixedReality
  Type: Boolean
  Default: true
  Category: "Optimization|VR"
  Tooltip: "Enable mixed reality passthrough"

- Name: ScreenPercentage
  Type: Float
  Default: 100.0
  Range: [50.0, 150.0]
  Category: "Optimization|Rendering"
  Tooltip: "Screen percentage for resolution scaling"

# Performance Settings
- Name: StreamingPoolSize
  Type: Integer
  Default: 3000
  Range: [1000, 8000]
  Category: "Optimization|Performance"
  Tooltip: "Texture streaming pool size in MB"

- Name: ViewDistanceScale
  Type: Float
  Default: 2.0
  Range: [0.5, 4.0]
  Category: "Optimization|Performance"
  Tooltip: "View distance scaling factor"

- Name: bEnableDLSS
  Type: Boolean
  Default: false
  Category: "Optimization|DLSS"
  Tooltip: "Enable DLSS if supported (UE5.4 required)"

# Console Commands Array
- Name: ConsoleCommands
  Type: Array of String
  Category: "Optimization|Commands"
  Tooltip: "Array of console commands to execute"
```

#### Custom Events to Create
```yaml
# Optimization Events
- Event: OnOptimizationStarted
  Description: "Fired when optimization process begins"

- Event: OnOptimizationComplete
  Parameters: [float Duration, bool bSuccess]
  Description: "Fired when optimization process completes"

- Event: OnCommandExecuted
  Parameters: [FString Command, bool bSuccess]
  Description: "Fired after each console command execution"

# Performance Events
- Event: OnPerformanceBaseline
  Parameters: [float FPS, float FrameTime]
  Description: "Fired with initial performance metrics"

- Event: OnPerformanceImprovement
  Parameters: [float OldFPS, float NewFPS, float Improvement]
  Description: "Fired with performance improvement data"
```

### 3. Event Graph Implementation

#### BeginPlay Event
```blueprint
Event BeginPlay
  ↓
[Record Start Time]
  → [Set OptimizationStartTime = GetGameTimeInSeconds]
  ↓
[Fire OnOptimizationStarted]
  ↓
[Branch: !bOptimizationApplied]
  → True:
    ↓
    [Capture Baseline Performance]
      → [Get stat FPS]
      → [Fire OnPerformanceBaseline]
    ↓
    [Initialize Console Commands Array]
    ↓
    [Execute VR Optimizations]
    ↓
    [Execute Rendering Optimizations]
    ↓
    [Execute Performance Optimizations]
    ↓
    [Execute DLSS Configuration] (if enabled)
    ↓
    [Execute Mixed Reality Setup] (if enabled)
    ↓
    [Validate Applied Settings]
    ↓
    [Set bOptimizationApplied = true]
    ↓
    [Capture Final Performance]
    ↓
    [Calculate Performance Improvement]
    ↓
    [Fire OnOptimizationComplete]
    ↓
    [Branch: bEnableDebugLogging]
      → True: [Print Optimization Summary]
```

#### Console Command Execution Sequence
```blueprint
Function: ExecuteVROptimizations
  ↓
[Execute Command: "vr.bEnableHMD 1"]
  → [Log Success/Failure]
  → [Fire OnCommandExecuted]
  ↓
[Execute Command: "vr.bEnableStereo 1"]
  → [Log Success/Failure]
  → [Fire OnCommandExecuted]
  ↓
[Execute Command: "vr.WorldToMetersScale 100"]
  → [Log Success/Failure]
  → [Fire OnCommandExecuted]
  ↓
[Execute Command: "vr.TrackingOrigin 1"] (Eye Level)
  → [Log Success/Failure]
  → [Fire OnCommandExecuted]

Function: ExecuteRenderingOptimizations
  ↓
[Execute Command: "r.ScreenPercentage " + ScreenPercentage]
  → [Log Success/Failure]
  → [Fire OnCommandExecuted]
  ↓
[Execute Command: "r.Tonemapper.Sharpen 1.0"]
  → [Log Success/Failure]
  → [Fire OnCommandExecuted]
  ↓
[Execute Command: "r.SceneColorFormat 3"] (PF_FloatRGB)
  → [Log Success/Failure]
  → [Fire OnCommandExecuted]
  ↓
[Execute Command: "r.CustomDepth 3"] (Enabled with Stencil)
  → [Log Success/Failure]
  → [Fire OnCommandExecuted]

Function: ExecutePerformanceOptimizations
  ↓
[Execute Command: "r.Streaming.PoolSize " + StreamingPoolSize]
  → [Log Success/Failure]
  → [Fire OnCommandExecuted]
  ↓
[Execute Command: "r.ViewDistanceScale " + ViewDistanceScale]
  → [Log Success/Failure]
  → [Fire OnCommandExecuted]
  ↓
[Execute Command: "r.ForceLOD 0"] (Highest quality LOD)
  → [Log Success/Failure]
  → [Fire OnCommandExecuted]
  ↓
[Execute Command: "r.HZBOcclusion 1"] (Enable HZB occlusion)
  → [Log Success/Failure]
  → [Fire OnCommandExecuted]
  ↓
[Execute Command: "r.Streaming.Boost 1"]
  → [Log Success/Failure]
  → [Fire OnCommandExecuted]
```

#### Mixed Reality Configuration
```blueprint
Function: ExecuteMixedRealitySetup
  ↓
[Branch: bEnableMixedReality]
  → True:
    ↓
    [Execute Command: "xr.OpenXREnvironmentBlendMode 3"] (Alpha Blend)
      → [Log Success/Failure]
      → [Fire OnCommandExecuted]
    ↓
    [Execute Command: "r.PostProcessAAQuality 6"] (High quality AA for MR)
      → [Log Success/Failure]
      → [Fire OnCommandExecuted]
  → False:
    ↓
    [Execute Command: "xr.OpenXREnvironmentBlendMode 1"] (Opaque)
      → [Log Success/Failure]
      → [Fire OnCommandExecuted]
```

#### DLSS Configuration (UE5.4 Compatible)
```blueprint
Function: ExecuteDLSSConfiguration
  ↓
[Branch: bEnableDLSS]
  → True:
    ↓
    [Check DLSS Availability]
      → [Execute Command: "r.NGX.DLSS.Enable 1"]
      → [Branch: Command Successful]
        → True:
          ↓
          [Execute Command: "r.NGX.DLSS.Quality 2"] (Balanced)
          ↓
          [Execute Command: "r.NGX.DLSS.Sharpness 0.5"]
          ↓
          [Print String: "DLSS Enabled Successfully"]
        → False:
          ↓
          [Print String: "DLSS Not Available - Falling back to standard rendering"]
          ↓
          [Set bEnableDLSS = false]
```

### 4. Custom Functions to Implement

#### ExecuteConsoleCommand Function
```blueprint
Function: ExecuteConsoleCommand
Parameters:
  - Command: String
  - bLogResult: Boolean
Return: Boolean

Logic:
  ↓
[Get Player Controller]
  ↓
[Branch: IsValid(PlayerController)]
  → True:
    ↓
    [Execute Console Command Node]
      → [Input: Command]
    ↓
    [Branch: bLogResult && bEnableDebugLogging]
      → True:
        ↓
        [Print String: "Executed: " + Command]
        ↓
        [Log to Output Log]
    ↓
    [Fire OnCommandExecuted]
    ↓
    [Return True]
  → False:
    ↓
    [Print String: "ERROR: No valid Player Controller"]
    ↓
    [Return False]
```

#### ValidateSettings Function
```blueprint
Function: ValidateSettings
Return: Boolean

Logic:
  ↓
[Check VR HMD Status]
  → [Execute Command: "stat fps"]
  → [Capture Current FPS]
  ↓
[Validate Frame Rate]
  → [Branch: FPS >= (TargetFrameRate * 0.8)]
    → True: [Continue Validation]
    → False: [Log Performance Warning]
  ↓
[Check Rendering Settings]
  → [Validate Screen Percentage applied]
  → [Validate Scene Color Format]
  ↓
[Check Streaming Settings]
  → [Validate Pool Size]
  → [Check Available Memory]
  ↓
[Return Validation Result]
```

#### CapturePerformanceMetrics Function
```blueprint
Function: CapturePerformanceMetrics
Parameters:
  - MetricType: String ("Baseline" or "Final")
Return: Struct (FPS, FrameTime, MemoryUsage)

Logic:
  ↓
[Execute Command: "stat fps"]
  → [Capture FPS Value]
  ↓
[Execute Command: "stat unit"]
  → [Capture Frame Time]
  ↓
[Execute Command: "stat memory"]
  → [Capture Memory Usage]
  ↓
[Branch: bEnableDebugLogging]
  → True:
    ↓
    [Print String: MetricType + " - FPS: " + FPS + " Frame Time: " + FrameTime]
  ↓
[Return Performance Struct]
```

### 5. Optimization Command Sets

#### Core VR Commands
```yaml
Essential Commands:
  - "vr.bEnableHMD 1"                    # Enable VR headset
  - "vr.bEnableStereo 1"                 # Enable stereo rendering
  - "vr.WorldToMetersScale 100"          # Set world scale
  - "r.ScreenPercentage 100"             # Set resolution scale
```

#### Advanced Performance Commands
```yaml
Rendering Optimizations:
  - "r.Tonemapper.Sharpen 1.0"           # Sharpen for VR clarity
  - "r.SceneColorFormat 3"               # PF_FloatRGB for alpha
  - "r.PostProcessAAQuality 4"           # High quality AA
  - "r.MotionBlurQuality 0"              # Disable motion blur in VR

Streaming Optimizations:
  - "r.Streaming.PoolSize 3000"          # Large texture pool
  - "r.Streaming.Boost 1"                # Boost streaming priority
  - "r.ViewDistanceScale 2.0"            # Extend view distance

LOD Optimizations:
  - "r.ForceLOD 0"                       # Highest quality LODs
  - "r.SkeletalMeshLODBias -1"           # Improve skeletal mesh quality
  - "r.StaticMeshLODBias -1"             # Improve static mesh quality
```

#### Varjo-Specific Commands
```yaml
Mixed Reality:
  - "xr.OpenXREnvironmentBlendMode 3"    # Enable passthrough
  - "r.CustomDepth 3"                    # Enable custom depth
  - "r.SceneColorFormat 3"               # Float RGB for alpha

Foveated Rendering:
  - "vr.FoveatedRendering 1"             # Enable if supported
  - "vr.VariableRateShading 1"           # Enable VRS if supported
```

### 6. Performance Monitoring

#### Continuous Monitoring Setup
```blueprint
Function: StartPerformanceMonitoring
  ↓
[Create Timer: MonitorPerformance]
  → [Interval: 5.0 seconds]
  → [Looping: True]
  ↓
[Enable stat tracking]
  → ["stat fps"]
  → ["stat unit"]
  → ["stat memory"]

Function: MonitorPerformance (Timer Callback)
  ↓
[Capture Current Metrics]
  ↓
[Check Frame Rate]
  → [Branch: FPS < (TargetFrameRate * 0.8)]
    → True: [Log Performance Warning]
  ↓
[Check Memory Usage]
  → [Branch: Memory > 80% of available]
    → True: [Log Memory Warning]
  ↓
[Update Performance Dashboard] (if UI enabled)
```

### 7. Error Handling & Fallbacks

#### Command Execution Failures
```blueprint
Function: HandleCommandFailure
Parameters:
  - FailedCommand: String
  - ErrorMessage: String

Logic:
  ↓
[Log Error Details]
  ↓
[Branch: Command Type]
    → "DLSS": [Disable DLSS, continue with standard rendering]
    → "VR": [Try alternative VR settings]
    → "Performance": [Use conservative performance settings]
    → "Other": [Continue with warning]
  ↓
[Update Optimization Status]
  ↓
[Provide User Feedback if critical]
```

#### System Compatibility Checks
```blueprint
Function: CheckSystemCompatibility
Return: Boolean

Logic:
  ↓
[Check VR Headset Connection]
  ↓
[Check Graphics Card Compatibility]
  ↓
[Check Available Memory]
  ↓
[Check UE Version for DLSS Support]
  ↓
[Return Compatibility Status]
```

### 8. Debug and Development Features

#### Debug Console Commands
```yaml
Development Commands:
  - "mvs.optimizer.reset"          # Reset all optimizations
  - "mvs.optimizer.benchmark"      # Run performance benchmark
  - "mvs.optimizer.status"         # Show current optimization status
  - "mvs.optimizer.reapply"        # Reapply optimizations
```

#### Visual Debug Information
```blueprint
Function: ShowDebugInfo
  ↓
[Display on Screen]
  → Current FPS
  → Frame Time
  → Memory Usage
  → Applied Optimizations Status
  → DLSS Status
  → VR Headset Status
  ↓
[Update Every Frame] (in debug builds only)
```

### 9. Integration Points

#### Level Integration
- Place one BP_StartupOptimizer actor per level
- Automatically runs on level start
- Can be manually triggered for testing

#### With Gesture System
- Optimizations enhance gesture detection performance
- Reduced latency improves gesture responsiveness
- Better tracking through improved frame rates

#### With VR Pawn
- Optimizations improve overall VR experience
- Better performance for complex scenes
- Enhanced mixed reality capabilities

## Testing Checklist
- [ ] All console commands execute without errors
- [ ] VR frame rate meets target (90+ FPS)
- [ ] Mixed reality mode works correctly
- [ ] DLSS enables when supported
- [ ] Performance metrics show improvement
- [ ] Debug logging provides useful information
- [ ] Error handling works for failed commands
- [ ] Optimization persists throughout gameplay
- [ ] Memory usage remains stable
- [ ] System works on target hardware (Varjo XR-4)

## Performance Targets
- **VR Frame Rate**: 90 FPS minimum, 120 FPS target
- **Frame Time**: < 11.1ms for 90 FPS
- **Memory Usage**: < 80% of available VRAM
- **Gesture Latency**: < 20ms end-to-end
- **Optimization Time**: < 2 seconds to apply all settings

## Platform-Specific Notes

### Varjo XR-4 Optimizations
- Enable mixed reality passthrough
- Use PF_FloatRGB for proper alpha channel
- Enable custom depth for hand occlusion
- Optimize for high-resolution displays

### RTX Graphics Cards
- Enable DLSS when UE5.4 available
- Use ray tracing optimizations if needed
- Leverage variable rate shading
- Optimize streaming for high VRAM