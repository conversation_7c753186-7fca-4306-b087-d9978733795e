# MVS Gesture Control System - Project Setup Guide

## Plugin Configuration

### Required Plugins to Enable
In Unreal Engine Editor: Edit > Project Settings > Plugins

**Enable These Plugins:**
- VarjoOpenXR
- OpenXR  
- OpenXRHandTracking
- UltraleapTracking (if compatible with UE5.5)

**Disable These Plugins:**
- OculusVR
- SteamVR
- Leave only OpenXR enabled

### Project Settings Configuration

#### Rendering Settings
**Edit > Project Settings > Engine > Rendering**

- **Custom Depth-Stencil Pass**: `Enabled with <PERSON>encil`
- **Alpha Channel Support**: `Allow through tonemapper`  
- **Scene Color Format**: `PF_FloatRGB` (for mixed reality support)

#### VR Settings  
**Edit > Project Settings > Engine > Rendering > VR**

- **Instanced Stereo Rendering**: `Disabled`
- **Mobile Multi-View**: `Disabled`

#### Performance Settings
**Edit > Project Settings > Engine > General Settings**

- **Smooth Frame Rate**: `Disabled`

#### Input Mapping
**Edit > Project Settings > Engine > Input**

- Extend existing `IMC_XRGestureMapping` context
- Add gesture action mappings for fallback controller inputs

## Directory Structure VerificationI 

Ensure the following directory structure exists:
```
Blueprints/
├── GestureSystem/          # Core gesture component blueprints
├── Optimization/           # Performance optimization blueprints  
└── UI/                     # Visual feedback system blueprints
```

## Hardware Requirements

### Supported Hardware
- **VR Headset**: Varjo XR-4 (primary target)
- **Hand Tracking**: Ultraleap Hand Tracking Camera
- **GPU**: RTX 3070 or better (for DLSS support when available)

### Environment Setup
- Ensure adequate ambient lighting for hand tracking
- Disable Varjo camera passthrough for optimal tracking
- Position Ultraleap camera for optimal hand detection range

## Performance Targets
- **VR Frame Rate**: 90-120 FPS minimum
- **Hand Tracking Latency**: < 20ms end-to-end
- **Gesture Recognition**: < 100ms detection time

## Next Steps
1. Verify all plugins are enabled and project settings configured
2. Create Blueprint components following the implementation guides
3. Test VR environment setup with hardware
4. Validate performance meets target metrics