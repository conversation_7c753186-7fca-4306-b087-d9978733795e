# MVS Gesture Control System - UE5.5 Corrected Project Setup Guide

## ⚠️ CORRECTED VERSION - UE5.5 Verified Settings

This is the corrected version with verified UE5.5 project settings paths and options.

## Plugin Configuration

### Required Plugins to Enable
In Unreal Engine Editor: **Edit > Project Settings > Plugins**

**Enable These Plugins:**
- ✅ **VarjoOpenXR** (if available for UE5.5)
- ✅ **OpenXR** 
- ✅ **OpenXRHandTracking**
- ✅ **UltraleapTracking** (verify UE5.5 compatibility - may require manual installation)

**Disable These Plugins:**
- ❌ **OculusVR** 
- ❌ **SteamVR**
- ❌ **OculusXR** (if present)

**Note**: Some plugins may not be available for UE5.5 yet. Check plugin compatibility before proceeding.

## Project Settings Configuration - VERIFIED PATHS

### Rendering Settings
**Edit > Project Settings > Engine > Rendering**

#### Custom Depth Configuration
- **Path**: **Rendering > Postprocessing > Custom Depth-Stencil Pass**
- **Setting**: Set to `Enabled with Stencil`
- **Note**: Editor restart required after changing this setting

#### Forward Rendering (Recommended for VR)
- **Path**: **Rendering > Forward Renderer > Forward Shading**  
- **Setting**: ✅ **Enabled**
- **Note**: Better VR performance than deferred rendering

#### Anti-Aliasing for VR
- **Path**: **Rendering > Default Settings > Anti-Aliasing Method**
- **Setting**: `Multisample Anti-Aliasing (MSAA)`
- **Note**: Better for VR than TAA which can cause motion blur

#### Scene Color Format (for Mixed Reality)
- **Path**: **Rendering > Postprocessing**
- **Setting**: Look for scene color or alpha channel settings
- **Note**: May be named differently in UE5.5 - look for alpha channel support

### XR/VR Specific Settings
**Edit > Project Settings > XR/VR** (Path may vary in UE5.5)

#### VR Rendering Options (if available)
- **Instanced Stereo Rendering**: Location varies by UE version
- **Mobile Multi-View**: May not be relevant for PC VR
- **Start in VR**: **Project Settings > Description > Start in VR** ✅

#### Console Variable Alternatives
If GUI settings are missing, use console commands in BP_StartupOptimizer:
```
r.CustomDepth 3                    // Enable custom depth with stencil
r.AntiAliasingMethod 1             // MSAA
r.ForwardShading 1                 // Enable forward shading
r.InstancedStereo 1                // Enable instanced stereo if supported
```

### Performance Settings
**Edit > Project Settings > Engine > General Settings**

#### Frame Rate Settings
- **Path**: **General Settings > Framerate**
- **Setting**: ❌ **Disable "Smooth Frame Rate"** 
- **Reason**: VR requires consistent frame timing

#### Memory/Streaming Settings
- **Path**: **Engine > Streaming**
- **Settings**: Configure texture streaming pool (handled by BP_StartupOptimizer)

## Hardware Requirements - UPDATED

### Verified Compatibility
- **VR Headset**: Varjo XR-4 with latest Varjo Base software
- **Hand Tracking**: Ultraleap Hand Tracking Camera with Gemini V5.2+
- **GPU**: RTX 3070 or better (RTX 4070+ recommended for UE5.5)
- **CPU**: Intel i7-10700K / AMD Ryzen 7 3700X or better
- **RAM**: 32GB recommended (16GB minimum)
- **Storage**: NVMe SSD for best performance

### Software Requirements
- **OS**: Windows 10 21H2 or Windows 11
- **UE Version**: 5.5.x (latest stable)
- **Varjo Base**: Latest version compatible with UE5.5
- **Ultraleap Software**: Gemini V5.2+ with UE5.5 support

## Plugin Compatibility Status - UE5.5

### ✅ Confirmed Working
- **OpenXR**: Native UE5.5 support
- **OpenXRHandTracking**: Native UE5.5 support

### ⚠️ Requires Verification
- **VarjoOpenXR**: Check if updated for UE5.5
- **UltraleapTracking**: May require manual plugin installation

### 🔧 Alternative Solutions
If plugins are not available:
- Use generic OpenXR hand tracking
- Check for community-maintained plugin versions
- Consider staying on UE5.4 until plugins are updated

## Directory Structure Verification

Ensure the following directory structure exists:
```
Content/
├── Blueprints/
│   ├── GestureSystem/          # Gesture component blueprints
│   ├── Optimization/           # Performance optimization blueprints  
│   └── UI/                     # Visual feedback system blueprints
└── [Your existing content]
```

## Console Command Fallbacks

If GUI settings are not available, use these console commands in BP_StartupOptimizer:

### Essential VR Commands
```cpp
// Core VR Settings
r.VR.EnableHMD 1                           // Enable VR headset
r.VR.EnableStereo 1                        // Enable stereo rendering

// Rendering Settings  
r.CustomDepth 3                            // Enable custom depth with stencil
r.ForwardShading 1                         // Enable forward rendering
r.AntiAliasingMethod 1                     // Set to MSAA
r.MotionBlurQuality 0                      // Disable motion blur for VR

// Performance Settings
r.ScreenPercentage 100                     // Set resolution scale
r.Tonemapper.Sharpen 1.0                   // Sharpen for VR clarity
r.Streaming.PoolSize 3000                  // Texture pool size (MB)
r.ViewDistanceScale 2.0                    // Extend view distance

// XR Settings (if applicable)
xr.OpenXREnvironmentBlendMode 3            // Mixed reality mode
```

## Troubleshooting Common UE5.5 Issues

### Plugin Loading Issues
```yaml
Problem: Plugin fails to load
Solutions:
  - Check plugin compatibility with UE5.5
  - Try manual plugin installation
  - Verify plugin dependencies
  - Check engine logs for specific errors
```

### VR Performance Issues
```yaml
Problem: Poor VR performance in UE5.5
Solutions:
  - Enable Forward Shading
  - Use MSAA instead of TAA
  - Disable expensive post-process effects
  - Monitor GPU/CPU usage
  - Use BP_StartupOptimizer console commands
```

### Hand Tracking Issues
```yaml
Problem: Hand tracking not working
Solutions:
  - Verify hardware connections
  - Check plugin versions
  - Test with generic OpenXR hand tracking
  - Verify tracking software installation
  - Check lighting conditions
```

## Validation Checklist - UE5.5 Specific

### Project Settings Verification
- [ ] Custom Depth-Stencil Pass enabled (Editor restarted)
- [ ] Forward Shading enabled
- [ ] MSAA selected for anti-aliasing
- [ ] Smooth Frame Rate disabled
- [ ] Start in VR enabled (if desired)

### Plugin Verification  
- [ ] Required plugins loaded without errors
- [ ] Plugin versions compatible with UE5.5
- [ ] No plugin conflicts in output log
- [ ] XR runtime properly detected

### Hardware Verification
- [ ] VR headset detected and functional
- [ ] Hand tracking camera connected
- [ ] Adequate performance in VR Preview
- [ ] All hardware drivers up to date

### Performance Verification
- [ ] 90+ FPS achieved in empty VR scene
- [ ] No frame drops during head movement
- [ ] Hand tracking responsive (< 20ms latency)
- [ ] Memory usage stable during VR session

## Next Steps

1. **Verify Plugin Compatibility**: Check if all required plugins work with UE5.5
2. **Test Hardware Setup**: Ensure VR headset and hand tracking work
3. **Performance Baseline**: Measure initial performance before gesture system
4. **Incremental Implementation**: Add components one by one and test

## UE5.5 Specific Notes

- **Lumen**: May need to be disabled for VR performance
- **Nanite**: May conflict with VR rendering - test carefully  
- **Virtual Shadow Maps**: May impact VR performance
- **World Partition**: Consider impact on VR level streaming

## Console Commands for Debugging

```cpp
// Performance Monitoring
stat fps                                   // Show frame rate
stat unit                                  // Show frame timing
stat memory                                // Show memory usage

// VR Debugging  
vr.SpectatorScreen.Mode 1                  // Show VR view on monitor
showdebug xr                               // Show XR debugging info

// Plugin Verification
xr.ListDevices                             // List connected XR devices
```

This corrected setup guide provides verified paths and settings for UE5.5, along with fallback solutions for missing GUI options.