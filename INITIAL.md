Here is your **initial blueprint document** for the gesture control system in the MVS (Multi-Vendor System) VR experience. This PRP is structured using the base template, integrating plugin libraries (Ultraleap & Varjo), user-story-based gesture breakdowns, and system-wide optimization via blueprint.

---

````md
# Gesture Control System & Optimization Blueprint (MVS)

## Purpose

Define and document the implementation of the Gesture Control System for the MVS VR experience, using Ultraleap and Varjo plugin libraries. All gestures will be drag-and-drop Blueprint components, preconfigured for the MVS context. This also includes a second Blueprint system focused on runtime optimization, including DLSS and rendering settings for Varjo XR-4.

---

## Goal

Build a fully Blueprint-based gesture control system that uses hand tracking and finger detection from Ultraleap and Varjo plugins. Each gesture will trigger specific actions mapped to the user journey in the MVS platform. Implement an additional optimization blueprint to ensure runtime performance and visual fidelity in high-end VR environments.

---

## Why

- Provide intuitive, controller-free interaction in VR
- Maximize immersion and reduce friction for end-users
- Leverage hardware already available (Varjo XR4 + Ultraleap 2)
- Reduce dev effort by making everything modular and drag-and-drop
- Enhance runtime performance using DLSS and other proven console-level optimization methods

---

## What

A full system of gestures for:

- Navigation
- Object interaction
- Spatial control (teleport, rotate, scale)
- Confirmation and cancellation
- UI interaction
- Delete / drop zones

A second Blueprint system will:

- Auto-apply DLSS and VR-specific Unreal optimizations
- Adapt to scene load (auto scalability)
- Run once on level start

---

## Success Criteria

- [ ] All gestures work reliably using only hand input
- [ ] All gesture blueprints are drag-and-drop usable
- [ ] System is modular and variable-light (preset for this MVS context)
- [ ] Optimization blueprint successfully configures DLSS, texture streaming, instancing, shadow settings, and resolution on launch
- [ ] Performance meets 90 FPS in VR using Varjo XR-4

---

## All Needed Context

### Documentation & References

```yaml
- doc: Ultraleap Unity/Unreal Plugin Docs
  section: Blueprint API reference
  critical: Gesture classification nodes and palm transform access

- doc: Varjo Plugin Docs
  section: Blueprint interface and headset configuration
  critical: Ensuring compatibility between head tracking and hand tracking

- docfile: /INITIAL.md
  why: Describes initial setup and gesture control expectations

- plugin: Varjo XR plugin (UE5.5 verified)
- plugin: Ultraleap Tracking SDK (UE5.5 ready, LeapC backend)
````

---

## Desired Codebase Tree

```bash
Blueprints/
├── GestureSystem/
│   ├── GS_Grab.uasset
│   ├── GS_eRotate.uasset
│   ├── GS_Delete.uasset
│   ├── GS_Teleport.uasset
│   ├── GS_Confirm.uasset
│   └── GS_Cancel.uasset
├── Optimization/
│   └── BP_StartupOptimizer.uasset
```

---

## Known Gotchas & Library Quirks

```text
# Ultraleap requires IsPinching() and IsGrabbing() nodes for consistent logic
# Hand tracking needs ambient light and Varjo camera passthrough disabled
# Drag + hover detection must use GetPalmWorldTransform() with forward vector
# DLSS must be enabled manually in project settings + ConsoleManager
# Some rendering settings only take effect in standalone build
```

---

## Implementation Blueprint

### Data Model

Gesture → Mapped Action

```yaml
Gesture: Grab on object 
Description: Pick up and move object
Trigger: IsGrabbing() == true + forward movement
Action: Attach to palm, move with transform, release on ungrab

Gesture: Tap object   
Description: Select or interact with it
Trigger: isSelected() == true
Action: Show object details, activate switches or enable gestures for selected objects

Gesture: Swipe Rotate
Description: Rotate selected object around Y-axis
Trigger: Swipe left/right with closed fingers
Action: Rotate attached object incrementally

Gesture: Grab + Down hand movement
Description: Delete gesture
Trigger: Hover over bin zone + swipe down
Action: Object fades out, removed from scene

Gesture: Pinch index + thumb
Description: Aim to teleport 
Trigger: Hold index finger + thumb to aim for the teleport area
Action: Once user opens fingers they are teleported to the target area. If the drop their hand, the teleport is cancelled

Gesture: Index Tap
Description: Confirm
Trigger: Index tap
Action: Accept current interaction or confirm UI action

Gesture: Open Flick (up)
Description: Cancel
Trigger: Open hand flick upwards
Action: Cancel or back out of UI
```

---

### Tasks

```yaml
Task 1: Import Ultraleap & Varjo Plugins
  - VERIFY compatibility with UE5.5
  - ADD to Plugins folder
  - ENABLE in Project Settings

Task 2: Create Gesture Component Blueprints
  - FOR each gesture:
      - Use LeapRightHandComponent / IsPinching()
      - Use GetPalmWorldTransform for direction
      - Connect to interaction logic
      - Modularize as component BP
      - PRESET all parameters for MVS only (e.g., movement speed, distance thresholds)

Task 3: Setup Drag-and-Drop Behavior
  - DESIGN: Each BP should expose a single node (e.g., Activate Gesture)
  - Default settings auto-bind logic; minimal editing

Task 4: Create BP_StartupOptimizer
  - SET: r.ScreenPercentage, r.Tonemapper.Sharpen, DLSS.Enable 1, etc.
  - ADD: r.ForceLOD, r.HZBOcclusion, r.Streaming.Boost
  - TRIGGER on EventBeginPlay
  - LOG output using PrintString or console command execution

Task 5: Test Gestures in MVS Context
  - LOAD standard MVS scene
  - TEST each gesture with debug print/log
  - VALIDATE no interference between gestures

Task 6: QA Optimization Blueprint
  - ENABLE stat FPS, stat UnitGraph
  - COMPARE pre/post BP_StartupOptimizer
  - CHECK for shadow/light streaming issues
```

---

### Per Task Pseudocode

```blueprint
# GS_Grab
Event Tick
 → If IsGrabbing() == true
     → Get PalmTransform
     → Set Actor Location to Palm

# BP_StartupOptimizer
Event BeginPlay
 → ExecuteConsoleCommand("r.Streaming.PoolSize 3000")
 → ExecuteConsoleCommand("DLSS.Enable 1")
 → ExecuteConsoleCommand("r.Tonemapper.Sharpen 1.0")
 → ExecuteConsoleCommand("r.ViewDistanceScale 2")
```

---

## Validation Loop

### Manual Checklist

* [ ] All gestures respond smoothly in VR headset
* [ ] All gestures are usable without modification
* [ ] The blueprint supports camera passhthrough to show the real player's hands and body
* [ ] Each gesture component is modular
* [ ] Optimization blueprint executes on BeginPlay
* [ ] DLSS and console commands persist in build
* [ ] Scene runs at target 90 to 120 FPS

---

## Anti-Patterns to Avoid

* ❌ Avoid using delay timers instead of tracked motion events
* ❌ Don’t hardcode object IDs – use tags
* ❌ Don’t use gesture logic in Level Blueprint – always use components
* ❌ Avoid shared variables across gestures – use local instancing
* ❌ Don’t rely on Tick for everything – use gesture events where possible

---

## Confidence Score: 9.5/10

The implementation uses tested plugin logic, with known performance tools. Only risk is overlap between gestures or scene-specific optimization interference. These are resolved with scoped blueprints and debug overlays.

---

## Notes

Further enhancements may include:

* Haptic feedback (via haptic glover or varjo controller)
* Gesture recording/replay system
* Profile-based adjustments for hand size/speed
* Support features that auto enable assets such as:
    - all objects in 2m radius of the player is interatible
    - blueprints turn add context based on tags - i.e. a blueprint with these tags is a door with hidnges on the right, and a handle object: tags: door, h_right, handle=true (would ask to select mesh and tag it handle)
* autmatic navMesh calculation for the optimal travel
