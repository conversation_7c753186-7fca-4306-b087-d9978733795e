# Gesture Control System & Optimization Blueprint (MVS) - PRP

## Goal
Build a fully Blueprint-based gesture control system that uses hand tracking and finger detection from Ultraleap and Varjo plugins. Each gesture will trigger specific actions mapped to the user journey in the MVS platform. Implement an additional optimization blueprint to ensure runtime performance and visual fidelity in high-end VR environments targeting 90-120 FPS on Varjo XR-4.

## Why
- Provide intuitive, controller-free interaction in VR environments
- Maximize immersion and reduce friction for end-users navigating the MVS platform
- Leverage hardware already available (Varjo XR4 + Ultraleap 2) for natural hand-based interactions
- Reduce development effort by making everything modular and drag-and-drop
- Enhance runtime performance using DLSS (when UE5.4 compatible) and proven console-level optimization methods
- Enable seamless spatial control for navigation, object manipulation, and UI interaction

## What
A comprehensive system of Blueprint-based gesture recognition for:

### Core Gestures:
- **Navigation**: Pinch-to-teleport with visual feedback
- **Object interaction**: Grab, move, rotate, and release objects
- **Spatial control**: Teleport, rotate, and scale operations  
- **Confirmation and cancellation**: Index tap confirm, open hand flick cancel
- **UI interaction**: Touch and selection for interface elements
- **Delete/drop zones**: Gesture-based object removal

### Performance Optimization System:
- Auto-apply DLSS and VR-specific Unreal optimizations
- Adapt to scene load with auto scalability
- Console command execution on level start
- VR-specific rendering optimizations for Varjo XR-4

## Success Criteria
- [ ] All gestures work reliably using only hand input in VR environment
- [ ] All gesture blueprints are drag-and-drop usable with minimal configuration
- [ ] System is modular and lightweight (preset for MVS context)
- [ ] Optimization blueprint successfully configures DLSS, texture streaming, instancing, shadow settings, and resolution on launch
- [ ] Performance consistently meets 90 FPS minimum in VR using Varjo XR-4
- [ ] Hand occlusion and passthrough work correctly with virtual objects
- [ ] All gestures have appropriate visual and haptic feedback

## All Needed Context

### Documentation & References
```yaml
# MUST READ - Include these in your context window

- url: https://docs.ultraleap.com/xr-and-tabletop/xr/unreal/getting-started/index.html
  why: Ultraleap plugin setup, Blueprint API, gesture detection methods
  critical: LeapComponent integration, gesture event binding patterns

- url: https://developer.varjo.com/docs/unreal/ue5/hand-tracking-with-unreal5  
  why: Varjo OpenXR hand tracking Blueprint integration
  critical: GetMotionControllerData usage, XRMotionControllerData structure

- url: https://docs.ultraleap.com/ultralab/pose-detection-blog.html
  why: Advanced pose detection system implementation
  critical: UPinchDetector, custom gesture events, threshold configuration

- file: SS/Source/MyProject/Public/MVSGesturesPawn.h
  why: Existing C++ pattern for gesture integration with Ultraleap
  critical: Component architecture, event binding, AIEPawnHands inheritance

- file: SS/Source/MyProject/Private/MVSGesturesPawn.cpp  
  why: Implementation patterns for gesture callbacks and teleportation
  critical: OnPinch/OnUnPinch event handling, TeleportationComponent usage

- file: SS/Content/Blueprints/Characters/BP_XRHandsPawn_Documentation.md
  why: Blueprint event graph patterns and gesture detection logic
  critical: Custom events (OnPinch_BP, OnGrab_BP), variable management

- file: SS/Content/Config/HandTrackingSetup.md
  why: Configuration requirements and plugin setup
  critical: Project settings, material setup for occlusion, collision configuration

- docfile: REF/Varjo_OpenXR_Plugin_for_Unreal_UE5.5_1.2.5/
  why: Plugin source code and integration patterns
  
- docfile: examples/Plugins/DLSS/
  why: DLSS integration patterns and console command usage
  critical: Console command execution, performance optimization blueprints
```

### Current Codebase Structure
```bash
C:\Users\<USER>\BPs\
├── SS\                                 # Existing VR/gesture implementation
│   ├── Content\
│   │   ├── Blueprints\
│   │   │   ├── BP_HandTrackingSetup.uasset      # Hand tracking foundation
│   │   │   └── Characters\
│   │   │       ├── BP_XRHandsPawn_Documentation.md  # Blueprint patterns
│   │   │       └── BP_XRPassthroughPawn.uasset      # VR pawn with passthrough
│   │   ├── Config\
│   │   │   └── HandTrackingSetup.md             # Setup documentation
│   │   └── Input\
│   │       ├── Actions\
│   │       │   └── IA_GestureTrigger.uasset     # Input action mapping
│   │       └── IMC_XRGestureMapping.uasset      # Gesture mapping context
│   └── Source\MyProject\
│       ├── Private\MVSGesturesPawn.cpp          # C++ implementation reference
│       └── Public\MVSGesturesPawn.h             # C++ header patterns
├── REF\                                # Reference plugins and documentation
│   ├── Varjo_OpenXR_Plugin_for_Unreal_UE5.5_1.2.5\  # Varjo plugin source
│   └── UnrealPlugin-main\              # Ultraleap plugin source
└── examples\                           # DLSS and optimization examples
    ├── Documentation\                  # DLSS programming guides
    └── Plugins\DLSS\                   # DLSS plugin implementation patterns
```

### Desired Codebase Structure
```bash
Blueprints/
├── GestureSystem/                      # Core gesture component blueprints
│   ├── GS_Grab.uasset                 # Grab gesture component (drag-and-drop)
│   ├── GS_Rotate.uasset               # Rotate gesture component  
│   ├── GS_Delete.uasset               # Delete gesture component
│   ├── GS_Teleport.uasset             # Teleport gesture component
│   ├── GS_Confirm.uasset              # Confirmation gesture component
│   ├── GS_Cancel.uasset               # Cancellation gesture component
│   └── BP_GestureManager.uasset       # Central gesture management system
├── Optimization/
│   └── BP_StartupOptimizer.uasset     # Performance optimization blueprint
└── UI/
    └── BP_GestureVisualFeedback.uasset # Visual feedback system for gestures
```

### Known Gotchas & Library Quirks
```unreal
# CRITICAL: Ultraleap requires IsPinching() and IsGrabbing() nodes for consistent logic
# Hand tracking needs ambient light and Varjo camera passthrough disabled for optimal tracking
# Drag + hover detection must use GetPalmWorldTransform() with forward vector calculations
# DLSS must be enabled manually in project settings + ConsoleManager (UE5.4 max support currently)
# Some rendering settings only take effect in standalone build, not PIE
# Varjo hand tracking does not require coordinate offsets (unlike Ultraleap integration)
# OpenXR hand tracking data accessed via GetMotionControllerData with XRMotionControllerData structure
# Custom Depth-Stencil Pass must be enabled for proper hand occlusion
# LeapComponent must be bound to custom events in BeginPlay for gesture detection
# Gesture thresholds: Pinch >= 0.8, Grab >= 0.7, Point >= 0.6 for reliable detection
# Teleport distance scales with hand movement from pinch start position
# ExecuteConsoleCommand nodes only work in standalone builds for optimization commands
```

## Implementation Blueprint

### Data Model - Gesture Architecture
```yaml
Gesture Component Pattern:
- Base Class: UActorComponent or USceneComponent
- Input: Hand tracking data (FLeapHandData or XRMotionControllerData)
- Output: Gesture events (OnGestureDetected, OnGestureProgression, OnGestureEnded)
- Configuration: Thresholds, sensitivity, hand preference
- State Management: bIsActive, CurrentStrength, StartTime, StartPosition

Gesture → Action Mapping:
Grab Gesture:
  Trigger: IsGrabbing() >= 0.7 + forward movement
  Action: Attach to palm transform, move with hand, release on ungrab
  Visual: Object outline highlight, grab indicator

Tap Gesture:  
  Trigger: Extended finger with quick movement toward object
  Action: Select object, show details, activate switches
  Visual: Ripple effect at contact point

Swipe Rotate:
  Trigger: Closed fingers + lateral hand movement
  Action: Rotate attached object around Y-axis
  Visual: Rotation arc indicator

Delete Gesture:
  Trigger: Grab + downward movement over delete zone
  Action: Object fade out and removal from scene
  Visual: Delete zone highlight, particle effects

Pinch Teleport:
  Trigger: Index + thumb pinch, hold to aim
  Action: Teleport to target location on release
  Visual: Teleport arc, landing zone preview

Index Tap Confirm:
  Trigger: Extended index finger quick tap motion
  Action: Confirm current interaction or UI action
  Visual: Confirmation ripple effect

Open Hand Cancel:
  Trigger: Open hand flick upward movement
  Action: Cancel current interaction, back out of UI
  Visual: Cancel wave effect
```

### Task Implementation Sequence

```yaml
Task 1: Setup Base VR Environment and Plugin Integration
VERIFY: Unreal Engine 5.5 compatibility with available plugins
ENABLE plugins in Project Settings:
  - VarjoOpenXR, OpenXR, OpenXRHandTracking
  - UltraleapTracking (if compatible with UE5.5)
  - Disable OculusVR and SteamVR
CONFIGURE project settings:
  - Enable Custom Depth-Stencil Pass
  - Set Custom Depth-Stencil Pass to "Enabled with Stencil"
  - Enable Alpha channel support for mixed reality
CREATE base VR pawn extending existing BP_XRPassthroughPawn pattern

Task 2: Create Core Gesture Component Architecture
CREATE BP_GestureManager blueprint (Actor Component):
  - Add LeapComponent for Ultraleap integration
  - Add hand tracking data processing logic
  - Bind to GetMotionControllerData for Varjo data
  - Implement gesture event dispatcher system
DEFINE base gesture component interface:
  - OnGestureDetected (float Strength, bool bIsLeftHand, FVector Position)
  - OnGestureProgression (float Progress)  
  - OnGestureEnded (FVector EndPosition)

Task 3: Implement Individual Gesture Components
FOR each gesture type (Grab, Rotate, Delete, Teleport, Confirm, Cancel):
  CREATE gesture-specific blueprint component:
    - Inherit from base gesture interface
    - Implement detection logic using appropriate thresholds
    - Add visual feedback triggers
    - Configure as drag-and-drop component with minimal setup
  MIRROR existing patterns from SS/Source/MyProject/MVSGesturesPawn.cpp:
    - Use PinchDetector pattern for pinch-based gestures
    - Implement OnPinch/OnUnPinch event handling
    - Follow TeleportationComponent integration pattern
  PRESET all parameters for MVS context:
    - Movement speed, distance thresholds, visual styles
    - Hand preference settings, gesture sensitivity

Task 4: Visual Feedback and UI Integration System  
CREATE BP_GestureVisualFeedback blueprint:
  - Particle systems for gesture confirmation
  - UI indicators for active gestures
  - Debug visualization for development
  - Hand occlusion material setup following HandTrackingSetup.md patterns
IMPLEMENT feedback triggers:
  - Connect to gesture component events
  - Provide immediate visual response for user actions
  - Include teleport arc visualization and landing zone preview

Task 5: Performance Optimization Blueprint
CREATE BP_StartupOptimizer blueprint:
  - EVENT BeginPlay execution trigger
  - ExecuteConsoleCommand nodes for VR optimization:
    * "r.ScreenPercentage 100" 
    * "r.Tonemapper.Sharpen 1.0"
    * "vr.bEnableHMD 1"
    * "xr.OpenXREnvironmentBlendMode 3" (for mixed reality)
    * "r.SceneColorFormat 3" (PF_FloatRGB for alpha channel)
    * "r.Streaming.PoolSize 3000"
    * "r.ViewDistanceScale 2"
    * "r.ForceLOD 0"
    * "r.HZBOcclusion 1"
    * "r.Streaming.Boost 1"
  - LOG output using PrintString for verification
  - CONDITIONAL DLSS enable if UE5.4 compatibility available

Task 6: Integration Testing and Validation
LOAD standard MVS scene with gesture components
TEST each gesture individually:
  - Debug print/log output for detection events
  - Visual feedback verification
  - Performance impact measurement
VALIDATE gesture interaction:
  - No interference between simultaneous gestures
  - Proper hand occlusion with virtual objects
  - Consistent tracking across different lighting conditions
  - Frame rate maintenance at 90+ FPS

Task 7: Drag-and-Drop Component Finalization
DESIGN component interface:
  - Single "Activate Gesture" node exposure
  - Auto-bind logic with default settings
  - Minimal configuration required for implementation
  - Clear tooltip documentation for each component
PACKAGE components for easy integration:
  - Component library organization
  - Example implementation scenes
  - Quick start documentation
```

### Per Task Pseudocode

```blueprint
# Task 2: BP_GestureManager
Event BeginPlay:
  → Get Leap Component Reference
  → Bind to OnPinch/OnUnPinch events (for Ultraleap)
  → Setup GetMotionControllerData (for Varjo)
  → Initialize gesture component array
  → Start gesture detection tick

Event Tick:
  → Get current hand tracking data
  → For each registered gesture component:
    → Process hand data against gesture thresholds
    → Fire appropriate gesture events
    → Update visual feedback

# Task 3: GS_Teleport Component
Event OnPinchDetected:
  → If bIsAiming == false:
    → Set bIsAiming = true
    → Store PinchStartPosition
    → Enable teleport arc visualization
    → Start teleport target calculation

Event OnPinchProgression:
  → If bIsAiming == true:
    → Calculate target position from hand movement
    → Update arc visualization
    → Validate landing zone

Event OnPinchReleased:
  → If bIsAiming == true:
    → Execute teleport to calculated position
    → Set bIsAiming = false
    → Disable visualization
    → Trigger teleport complete event

# Task 5: BP_StartupOptimizer  
Event BeginPlay:
  → ExecuteConsoleCommand("r.Streaming.PoolSize 3000")
  → ExecuteConsoleCommand("xr.OpenXREnvironmentBlendMode 3")
  → ExecuteConsoleCommand("r.SceneColorFormat 3")
  → ExecuteConsoleCommand("vr.bEnableHMD 1")
  → PrintString("VR Optimization Applied")
  → Log performance baseline metrics
```

### Integration Points
```yaml
VR PAWN:
  - Extend existing BP_XRPassthroughPawn
  - Add BP_GestureManager component
  - Configure LeapComponent with gesture bindings
  - Setup Varjo OpenXR integration

PROJECT SETTINGS:
  - Plugins: Enable VarjoOpenXR, OpenXR, OpenXRHandTracking
  - Rendering: Custom Depth-Stencil Pass enabled
  - VR: Disable instanced stereo, mobile multi-view
  - Performance: Disable smooth frame rate

INPUT MAPPING:
  - Extend IMC_XRGestureMapping context
  - Add gesture action mappings
  - Configure fallback controller inputs

LEVEL INTEGRATION:
  - Place BP_StartupOptimizer in level
  - Configure gesture interaction volumes
  - Setup delete zones and teleport areas
```

## Validation Loop

### Level 1: Plugin Compatibility & Setup Verification
```bash
# Verify plugin installations and compatibility
# Check Project Settings > Plugins for enabled status:
# - VarjoOpenXR: Enabled
# - OpenXR: Enabled  
# - OpenXRHandTracking: Enabled
# - UltraleapTracking: Enabled (if UE5.5 compatible)

# Verify project settings configuration:
# Edit > Project Settings > Engine > Rendering
# - Custom Depth-Stencil Pass: Enabled with Stencil
# - Alpha Channel Support: Allow through tonemapper

# Expected: All plugins load without errors, project settings applied correctly
```

### Level 2: Component Blueprint Compilation
```bash
# Compile all gesture component blueprints
# Open each blueprint in Blueprints/GestureSystem/ and verify:
# - No compilation errors in Blueprint editor
# - All custom events properly defined
# - Component interfaces correctly implemented
# - Visual feedback nodes connected

# Verify BP_StartupOptimizer:
# - All ExecuteConsoleCommand nodes present
# - BeginPlay event properly connected
# - PrintString debug outputs configured

# Expected: All blueprints compile without errors or warnings
```

### Level 3: VR Runtime Testing
```bash
# Test in VR Preview mode with Varjo XR-4 connected:
# 1. Launch VR Preview from toolbar
# 2. Verify hand tracking visibility and accuracy
# 3. Test each gesture individually:
#    - Pinch teleport with arc visualization
#    - Grab object interaction with visual feedback
#    - Rotate gesture with rotation indicators
#    - Delete gesture in delete zones
#    - Confirm/cancel gestures with UI elements

# Performance validation:
# - Monitor FPS using stat FPS console command
# - Verify 90+ FPS maintenance during active gestures
# - Check stat UnitGraph for performance bottlenecks

# Expected: All gestures respond smoothly, no tracking lag, stable frame rate
```

### Level 4: Integration and Compatibility Testing
```bash
# Mixed Reality functionality:
# - Enable passthrough using xr.OpenXREnvironmentBlendMode 3
# - Verify hand occlusion with virtual objects
# - Test gesture recognition with real-world background

# Optimization verification:
# - Compare performance before/after BP_StartupOptimizer execution
# - Verify console commands execute correctly in standalone build
# - Monitor memory usage and streaming performance

# Multi-gesture interaction:
# - Test simultaneous gesture detection (different hands)
# - Verify no interference between gesture types
# - Confirm gesture priority and conflict resolution

# Expected: Stable mixed reality, optimized performance, robust multi-gesture support
```

## Final Validation Checklist
- [ ] All gesture components compile and function in VR environment
- [ ] Hand tracking works reliably with both Ultraleap and Varjo systems
- [ ] Visual feedback provides immediate gesture response
- [ ] Performance optimization maintains 90+ FPS in VR
- [ ] Mixed reality passthrough integration functions correctly
- [ ] All gestures are drag-and-drop usable with minimal configuration
- [ ] Documentation covers setup and usage patterns
- [ ] No interference between multiple active gestures
- [ ] Graceful fallback for tracking loss or hardware disconnection

## Anti-Patterns to Avoid
- ❌ Don't use delay timers instead of tracked motion events
- ❌ Don't hardcode object IDs – use tags and component references
- ❌ Don't put gesture logic in Level Blueprint – always use components
- ❌ Don't share variables across gestures – use local instancing and event dispatchers
- ❌ Don't rely exclusively on Tick for gesture detection – use event-driven patterns
- ❌ Don't ignore gesture threshold tuning – test with multiple users
- ❌ Don't skip visual feedback – users need immediate response confirmation
- ❌ Don't assume tracking accuracy – implement confidence checks and fallbacks
- ❌ Don't optimize prematurely – establish baseline performance before applying optimizations

## Confidence Score: 8.5/10

This implementation leverages extensively tested plugin patterns from the existing codebase, follows established VR performance optimization practices, and uses proven gesture detection methodologies. The main risks are:

1. **UE5.5 Plugin Compatibility**: Ultraleap plugin may require updates for full UE5.5 support
2. **DLSS Integration**: Currently limited to UE5.4, may need alternative optimization strategies
3. **Hardware-Specific Tuning**: Gesture thresholds may need adjustment for different hand sizes and tracking conditions

These risks are mitigated through:
- Fallback patterns based on existing SS/ implementation
- Alternative optimization approaches using console commands
- Configurable gesture parameters with sensible defaults
- Comprehensive testing framework with multiple validation levels

The modular component architecture ensures that issues with individual gestures don't affect the overall system, and the extensive existing codebase provides proven patterns for all major implementation challenges.