# UE5.5 Remote Control Setup & Implementation Guide

## Overview

This guide implements VS Code integration with Unreal Engine 5.5's Remote Control API, allowing you to control any running UE5.5 project directly from your code editor.

## Prerequisites

- Unreal Engine 5.5 installed
- VS Code with C++ development setup (see `vscode.md`)
- Node.js 16+ (for VS Code extension development)
- Basic understanding of HTTP/REST APIs

## Part 1: Unreal Engine Setup

### 1.1 Enable Remote Control Plugin

**Works with ANY UE5.5 project - no project-specific configuration needed!**

1. Open any UE5.5 project in the Editor
2. Go to **Edit → Plugins**
3. Search for "Remote Control API" in the **Messaging** category
4. Check **Enabled** → Click **Yes** to accept Beta warning
5. Click **Restart Now**

### 1.2 Start Remote Control Server

**Method 1: Console Command (Manual)**
```
1. Window → Developer Tools → Output Log
2. In Cmd bar, type: WebControl.StartServer
3. Look for: "LogRemoteControl: Remote Control server started"
```

**Method 2: Auto-start (Recommended)**
```
1. Console command: WebControl.EnableServerOnStartup
2. Or launch with flags: YourProject.uproject -RCWebControlEnable -RCWebInterfaceEnable
```

**Method 3: Network Access (Team Development)**
```
1. Edit Config/DefaultEngine.ini in your project
2. Add:
   [HTTPServer.Listeners]
   DefaultBindAddress=0.0.0.0
3. Restart UE Editor
```

### 1.3 Verify Connection

Test basic connectivity:
```bash
# Windows Command Prompt
curl -X PUT http://localhost:30010/remote/object/property ^
     -H "Content-Type: application/json" ^
     -d "{\"objectPath\":\"/Engine/Engine.Engine\",\"access\":\"READ_ACCESS\",\"propertyName\":\"GameViewport\"}"

# PowerShell
Invoke-RestMethod -Uri "http://localhost:30010/remote/object/property" -Method PUT -ContentType "application/json" -Body '{"objectPath":"/Engine/Engine.Engine","access":"READ_ACCESS","propertyName":"GameViewport"}'
```

Expected response: JSON object with GameViewport information.

## Part 2: VS Code Integration

### 2.1 Enhanced Workspace Configuration

Add to your existing `.vscode/tasks.json`:

```json
{
  "version": "2.0.0",
  "tasks": [
    // Your existing tasks...
    {
      "label": "UE: Launch with Remote Control",
      "type": "shell",
      "command": "cmd",
      "args": [
        "/c", "start", "",
        "${workspaceFolder}/${workspaceFolderBasename}.uproject",
        "-RCWebControlEnable", "-RCWebInterfaceEnable"
      ],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "problemMatcher": []
    },
    {
      "label": "UE: Test Remote Control Connection",
      "type": "shell",
      "command": "curl",
      "args": [
        "-X", "PUT",
        "http://localhost:30010/remote/object/property",
        "-H", "Content-Type: application/json",
        "-d", "{\"objectPath\":\"/Engine/Engine.Engine\",\"access\":\"READ_ACCESS\",\"propertyName\":\"GameViewport\"}"
      ],
      "group": "test",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      }
    },
    {
      "label": "UE: Start Remote Control Server",
      "type": "shell",
      "command": "powershell",
      "args": [
        "-Command",
        "Invoke-RestMethod -Uri 'http://localhost:30010/remote/control/start' -Method POST"
      ],
      "group": "build"
    }
  ]
}
```

### 2.2 VS Code Settings

Add to your `.vscode/settings.json`:

```json
{
  // Your existing settings...
  "ue.remoteControl.host": "localhost",
  "ue.remoteControl.port": 30010,
  "ue.remoteControl.autoConnect": true,
  "ue.remoteControl.timeout": 5000
}
```

## Part 3: Basic Usage Examples

### 3.1 Reading Properties

```bash
# Get actor location
curl -X PUT http://localhost:30010/remote/object/property ^
     -H "Content-Type: application/json" ^
     -d "{\"objectPath\":\"/Game/Maps/YourLevel:PersistentLevel.YourActor\",\"access\":\"READ_ACCESS\",\"propertyName\":\"ActorLocation\"}"
```

### 3.2 Setting Properties

```bash
# Set actor location
curl -X PUT http://localhost:30010/remote/object/property ^
     -H "Content-Type: application/json" ^
     -d "{\"objectPath\":\"/Game/Maps/YourLevel:PersistentLevel.YourActor\",\"access\":\"WRITE_ACCESS\",\"propertyName\":\"ActorLocation\",\"propertyValue\":{\"X\":100,\"Y\":200,\"Z\":300}}"
```

### 3.3 Calling Functions

```bash
# Call Blueprint function
curl -X PUT http://localhost:30010/remote/object/call ^
     -H "Content-Type: application/json" ^
     -d "{\"objectPath\":\"/Game/Blueprints/YourBlueprint\",\"functionName\":\"YourFunction\",\"parameters\":{\"Param1\":\"Value1\"},\"generateTransaction\":true}"
```

## Part 4: Common Object Paths

### Finding Object Paths

1. **World Outliner Method**:
   - Select object in World Outliner
   - Right-click → Copy Reference
   - Format: `/Game/Maps/MapName:PersistentLevel.ActorName`

2. **Blueprint Method**:
   - Open Blueprint
   - Path format: `/Game/Blueprints/BlueprintName`

3. **Component Method**:
   - Actor components: `/Game/Maps/MapName:PersistentLevel.ActorName.ComponentName`

### Common Paths Examples

```
# Engine objects
/Engine/Engine.Engine
/Engine/GameViewportClient

# Level objects  
/Game/Maps/ThirdPersonExampleMap:PersistentLevel.PlayerStart
/Game/Maps/ThirdPersonExampleMap:PersistentLevel.DirectionalLight_0

# Blueprint objects
/Game/ThirdPersonBP/Blueprints/ThirdPersonCharacter
/Game/Blueprints/GameMode

# Components
/Game/Maps/Level:PersistentLevel.StaticMeshActor.StaticMeshComponent0
/Game/Maps/Level:PersistentLevel.DirectionalLight.LightComponent0
```

## Part 5: Troubleshooting

### Connection Issues

1. **Port 30010 not responding**:
   - Check if Remote Control plugin is enabled
   - Verify server started: look for "Remote Control server started" in Output Log
   - Try manual start: `WebControl.StartServer`

2. **"Connection refused"**:
   - UE Editor must be running
   - Remote Control server must be started
   - Check Windows Firewall settings

3. **Network access issues**:
   - For remote access, set `DefaultBindAddress=0.0.0.0` in DefaultEngine.ini
   - Ensure port 30010 is open in firewall
   - Use actual IP address instead of localhost

### Common Errors

1. **"Object not found"**:
   - Verify object path is correct
   - Object must exist in current level/world
   - Use Copy Reference from World Outliner

2. **"Property not accessible"**:
   - Property must be exposed to Blueprint/Python
   - Check access level (READ_ACCESS vs WRITE_ACCESS)
   - Some properties are read-only

3. **"Function not found"**:
   - Function must be marked as BlueprintCallable
   - Check function name spelling
   - Verify object supports the function

## Part 5: Creating Remote Control Presets

### 5.1 Open Remote Control Panel

In UE5.5:
1. Go to **Window → Remote Control Panel**
2. Click the **"+"** button to create a new preset
3. Name your preset (e.g., "LightingControl", "GameplayControl")

### 5.2 Expose Objects for Remote Control

1. **Open World Outliner** (Window → World Outliner)
2. **Drag actors** from World Outliner into the Remote Control Panel
3. **Select properties** to expose (Location, Rotation, Scale, etc.)
4. **Expose functions** by dragging Blueprint functions

### 5.3 Common Objects to Expose

- **DirectionalLight** - For lighting control
- **SkySphere/SkyAtmosphere** - For sky and weather
- **Player Character** - For player manipulation
- **Cameras** - For camera control
- **Game Mode** - For gameplay settings

### 5.4 Test Your Preset

After creating presets, test with:
```bash
node automation/discover-objects.js
```

## Next Steps

- Implement VS Code extension for easier control
- Create automation scripts for common tasks
- Set up Remote Control presets for frequently used objects
- Explore WebSocket connections for real-time updates

## Security Considerations

- **Never expose Remote Control to the internet**
- Use only on trusted local networks
- Consider VPN for remote team access
- Remote Control is intended for development only
