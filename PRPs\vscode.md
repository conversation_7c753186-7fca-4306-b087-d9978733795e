Below is a **curated “starter pack” of up-to-date resources** for driving **Unreal Engine 5.5** entirely from **Visual Studio Code**. Skim the starred links first, then dive deeper as needed.

| 🔗 Resource                                                                 | Why you need it / what you’ll learn                                                                                                                                                             |
| --------------------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **⭐ Official guide – “Setting Up VS Code for Unreal Engine 5.5”**           | Epic’s step-by-step: required extensions, MSVC / Clang tool-sets, generating the `.code-workspace`, refreshing it from the Editor, and enabling IntelliSense paths ([Epic Games Developers][1]) |
| **Generate project files for VS Code (`GenerateProjectFiles.bat -vscode`)** | Command-line alternative that spits out the workspace without opening the Editor ([Epic Games Developer][2])                                                                                    |
| **Blog: “Building UE5 from Source for VS Code” (2024)**                     | Practical notes on compiling the Engine itself, setting launch/debug profiles inside VS Code, and using Ctrl-F5 to start the Editor ([voithos.io][3])                                           |
| **VS Code extension pack: “C/C++”, “C#”, “Clangd”, “Unreal Snippets”**      | Foundation for IntelliSense, build, and navigation (listed in Epic’s doc) ([Epic Games Developers][1])                                                                                          |
| **GitHub – `ue4-intellisense-fixes`**                                       | One-click VS Code plug-in that patches Unreal’s huge include graph, killing red squiggles and speeding up symbol search ([GitHub][4])                                                           |
| **Unreal Build Tool tasks / launch presets**                                | Epic’s doc shows how to add **tasks.json** (Build, Hot-Reload) and **launch.json** (attach debugger) once the workspace is generated ([Epic Games Developers][1])                               |
| **MSVC Build Tools 2022 download**                                          | The lean tool-chain (you don’t need full Visual Studio) ([Epic Games Developers][1])                                                                                                            |
| **Community forum thread “Using UE with VS Code” (Dec 2023)**               | Tips from devs who switched, including key-bindings (`Ctrl+Shift+B` to build, `Ctrl+F5` to run) ([Epic Developer Community Forums][5])                                                          |

---

## Quick-start checklist (Windows)

1. **Install VS Code** + the *C/C++* extension pack and *C#* extension.
2. **Install MSVC Build Tools 2022** (only the “Desktop C++” workload is required).
3. In the Unreal Editor → **Edit ▸ Editor Preferences ▸ Source Code**, set **Visual Studio Code** and restart.
4. **Generate workspace**
   *From Explorer:* right-click your `.uproject` → *Generate VS Code project*.
   *CLI:* `GenerateProjectFiles.bat -vscode`.
5. Open the resulting `<Project>.code-workspace` in VS Code.
6. Press **Ctrl + Shift + B** → *Build Project* (uses `tasks.json` auto-generated by UBT).
7. In the **Run & Debug** panel choose *Launch <Project>Editor (Development)* → hit ▶︎.
8. Optional: install the **`ue4-intellisense-fixes`** VSIX and the *Unreal Snippets* extension for cleaner autocomplete.

> **Tip:** whenever you add new C++ files, hit **Tools ▸ Refresh VS Code Project** in the Editor or re-run `GenerateProjectFiles.bat -vscode` to update IntelliSense.

---

### Hot-reload & debugging

* Live coding is supported: set `"liveCoding": true` in your **launch.json** or tick *“Enable Live Coding”* in the Editor; rebuild (Ctrl+Alt+F11) and your module reloads without restarting the Editor.
* Attach to a running game: choose the **“Attach to UE Process”** preset created in your workspace and pick the game exe.

---

### When to prefer full Visual Studio or Rider

VS Code lacks some engine-specific niceties (Blueprint reflectors, class wizards). For very large C++ code-bases—or if you need in-IDE visual debugging of shaders—switching to **Visual Studio 2022 + UnrealVS extension** or **JetBrains Rider (Unreal edition)** can save time. But for everyday gameplay coding, **VS Code is now first-class** thanks to Epic’s official support in 5.5.

Happy coding!

[1]: https://dev.epicgames.com/documentation/en-us/unreal-engine/setting-up-visual-studio-code-for-unreal-engine "Setting Up Visual Studio Code for Unreal Engine | Unreal Engine 5.6 Documentation | Epic Developer Community"
[2]: https://docs.unrealengine.com/4.27/en-US/ProductionPipelines/BuildTools/UnrealBuildTool/ProjectFilesForIDEs "Project Files for IDEs | Unreal Engine 4.27 Documentation | Epic Developer Community"
[3]: https://voithos.io/articles/building-unreal-engine-from-source-for-vscode/ "
    Building Unreal Engine From Source for VSCode | voithos.io
  "
[4]: https://github.com/boocs/ue4-intellisense-fixes "GitHub - boocs/ue4-intellisense-fixes: Automatically fixes VSCode/Unreal Engine Intellisense Config bugs on startup"
[5]: https://forums.unrealengine.com/t/using-unreal-engine-with-vs-code/1570695?utm_source=chatgpt.com "Using Unreal Engine with Vs Code - Getting Started & Setup"
