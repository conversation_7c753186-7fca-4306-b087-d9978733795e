// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "VarjoOpenXRRuntimeSettings/Public/VarjoOpenXRRuntimeSettings.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeVarjoOpenXRRuntimeSettings() {}

// Begin Cross Module References
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
UPackage* Z_Construct_UPackage__Script_VarjoOpenXRRuntimeSettings();
VARJOOPENXRRUNTIMESETTINGS_API UClass* Z_Construct_UClass_UVarjoOpenXRRuntimeSettings();
VARJOOPENXRRUNTIMESETTINGS_API UClass* Z_Construct_UClass_UVarjoOpenXRRuntimeSettings_NoRegister();
VARJOOPENXRRUNTIMESETTINGS_API UEnum* Z_Construct_UEnum_VarjoOpenXRRuntimeSettings_VarjoRenderingMode();
// End Cross Module References

// Begin Enum VarjoRenderingMode
static FEnumRegistrationInfo Z_Registration_Info_UEnum_VarjoRenderingMode;
static UEnum* VarjoRenderingMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_VarjoRenderingMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_VarjoRenderingMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_VarjoOpenXRRuntimeSettings_VarjoRenderingMode, (UObject*)Z_Construct_UPackage__Script_VarjoOpenXRRuntimeSettings(), TEXT("VarjoRenderingMode"));
	}
	return Z_Registration_Info_UEnum_VarjoRenderingMode.OuterSingleton;
}
template<> VARJOOPENXRRUNTIMESETTINGS_API UEnum* StaticEnum<VarjoRenderingMode>()
{
	return VarjoRenderingMode_StaticEnum();
}
struct Z_Construct_UEnum_VarjoOpenXRRuntimeSettings_VarjoRenderingMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/VarjoOpenXRRuntimeSettings.h" },
		{ "VarjoRenderingMode_QuadView.DisplayName", "Quad View" },
		{ "VarjoRenderingMode_QuadView.Name", "VarjoRenderingMode_QuadView" },
		{ "VarjoRenderingMode_Stereo.DisplayName", "Stereo" },
		{ "VarjoRenderingMode_Stereo.Name", "VarjoRenderingMode_Stereo" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "VarjoRenderingMode_QuadView", (int64)VarjoRenderingMode_QuadView },
		{ "VarjoRenderingMode_Stereo", (int64)VarjoRenderingMode_Stereo },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_VarjoOpenXRRuntimeSettings_VarjoRenderingMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_VarjoOpenXRRuntimeSettings,
	nullptr,
	"VarjoRenderingMode",
	"VarjoRenderingMode",
	Z_Construct_UEnum_VarjoOpenXRRuntimeSettings_VarjoRenderingMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_VarjoOpenXRRuntimeSettings_VarjoRenderingMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::Regular,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_VarjoOpenXRRuntimeSettings_VarjoRenderingMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_VarjoOpenXRRuntimeSettings_VarjoRenderingMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_VarjoOpenXRRuntimeSettings_VarjoRenderingMode()
{
	if (!Z_Registration_Info_UEnum_VarjoRenderingMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_VarjoRenderingMode.InnerSingleton, Z_Construct_UEnum_VarjoOpenXRRuntimeSettings_VarjoRenderingMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_VarjoRenderingMode.InnerSingleton;
}
// End Enum VarjoRenderingMode

// Begin Class UVarjoOpenXRRuntimeSettings
void UVarjoOpenXRRuntimeSettings::StaticRegisterNativesUVarjoOpenXRRuntimeSettings()
{
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(UVarjoOpenXRRuntimeSettings);
UClass* Z_Construct_UClass_UVarjoOpenXRRuntimeSettings_NoRegister()
{
	return UVarjoOpenXRRuntimeSettings::StaticClass();
}
struct Z_Construct_UClass_UVarjoOpenXRRuntimeSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "IncludePath", "VarjoOpenXRRuntimeSettings.h" },
		{ "ModuleRelativePath", "Public/VarjoOpenXRRuntimeSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RenderingMode_MetaData[] = {
		{ "Category", "Rendering Mode" },
		{ "DisplayName", "Rendering Mode" },
		{ "ModuleRelativePath", "Public/VarjoOpenXRRuntimeSettings.h" },
		{ "Tooltip", "Please note that Quad View Rendering is usually required for high performance, high quality rendering, but Stereo Rendering might be better supported with some rendering features." },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoveatedRendering_MetaData[] = {
		{ "Category", "Foveated Rendering" },
		{ "DisplayName", "Enable Foveated Rendering" },
		{ "ModuleRelativePath", "Public/VarjoOpenXRRuntimeSettings.h" },
		{ "Tooltip", "Enable Foveated Rendering. Please note that this option is usually required for high performance, high quality rendering." },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_RenderingMode;
	static void NewProp_FoveatedRendering_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_FoveatedRendering;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UVarjoOpenXRRuntimeSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UVarjoOpenXRRuntimeSettings_Statics::NewProp_RenderingMode = { "RenderingMode", nullptr, (EPropertyFlags)0x0010000000044001, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UVarjoOpenXRRuntimeSettings, RenderingMode), Z_Construct_UEnum_VarjoOpenXRRuntimeSettings_VarjoRenderingMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RenderingMode_MetaData), NewProp_RenderingMode_MetaData) }; // 2672920161
void Z_Construct_UClass_UVarjoOpenXRRuntimeSettings_Statics::NewProp_FoveatedRendering_SetBit(void* Obj)
{
	((UVarjoOpenXRRuntimeSettings*)Obj)->FoveatedRendering = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UVarjoOpenXRRuntimeSettings_Statics::NewProp_FoveatedRendering = { "FoveatedRendering", nullptr, (EPropertyFlags)0x0010000000044001, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UVarjoOpenXRRuntimeSettings), &Z_Construct_UClass_UVarjoOpenXRRuntimeSettings_Statics::NewProp_FoveatedRendering_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoveatedRendering_MetaData), NewProp_FoveatedRendering_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UVarjoOpenXRRuntimeSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UVarjoOpenXRRuntimeSettings_Statics::NewProp_RenderingMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UVarjoOpenXRRuntimeSettings_Statics::NewProp_FoveatedRendering,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UVarjoOpenXRRuntimeSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UVarjoOpenXRRuntimeSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_VarjoOpenXRRuntimeSettings,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UVarjoOpenXRRuntimeSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UVarjoOpenXRRuntimeSettings_Statics::ClassParams = {
	&UVarjoOpenXRRuntimeSettings::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UVarjoOpenXRRuntimeSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UVarjoOpenXRRuntimeSettings_Statics::PropPointers),
	0,
	0x001000A6u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UVarjoOpenXRRuntimeSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UVarjoOpenXRRuntimeSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UVarjoOpenXRRuntimeSettings()
{
	if (!Z_Registration_Info_UClass_UVarjoOpenXRRuntimeSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UVarjoOpenXRRuntimeSettings.OuterSingleton, Z_Construct_UClass_UVarjoOpenXRRuntimeSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UVarjoOpenXRRuntimeSettings.OuterSingleton;
}
template<> VARJOOPENXRRUNTIMESETTINGS_API UClass* StaticClass<UVarjoOpenXRRuntimeSettings>()
{
	return UVarjoOpenXRRuntimeSettings::StaticClass();
}
UVarjoOpenXRRuntimeSettings::UVarjoOpenXRRuntimeSettings(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UVarjoOpenXRRuntimeSettings);
UVarjoOpenXRRuntimeSettings::~UVarjoOpenXRRuntimeSettings() {}
// End Class UVarjoOpenXRRuntimeSettings

// Begin Registration
struct Z_CompiledInDeferFile_FID_HostProject_Plugins_VarjoOpenXR_Source_VarjoOpenXRRuntimeSettings_Public_VarjoOpenXRRuntimeSettings_h_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ VarjoRenderingMode_StaticEnum, TEXT("VarjoRenderingMode"), &Z_Registration_Info_UEnum_VarjoRenderingMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2672920161U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UVarjoOpenXRRuntimeSettings, UVarjoOpenXRRuntimeSettings::StaticClass, TEXT("UVarjoOpenXRRuntimeSettings"), &Z_Registration_Info_UClass_UVarjoOpenXRRuntimeSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UVarjoOpenXRRuntimeSettings), 3632854278U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_HostProject_Plugins_VarjoOpenXR_Source_VarjoOpenXRRuntimeSettings_Public_VarjoOpenXRRuntimeSettings_h_1238181863(TEXT("/Script/VarjoOpenXRRuntimeSettings"),
	Z_CompiledInDeferFile_FID_HostProject_Plugins_VarjoOpenXR_Source_VarjoOpenXRRuntimeSettings_Public_VarjoOpenXRRuntimeSettings_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_HostProject_Plugins_VarjoOpenXR_Source_VarjoOpenXRRuntimeSettings_Public_VarjoOpenXRRuntimeSettings_h_Statics::ClassInfo),
	nullptr, 0,
	Z_CompiledInDeferFile_FID_HostProject_Plugins_VarjoOpenXR_Source_VarjoOpenXRRuntimeSettings_Public_VarjoOpenXRRuntimeSettings_h_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_HostProject_Plugins_VarjoOpenXR_Source_VarjoOpenXRRuntimeSettings_Public_VarjoOpenXRRuntimeSettings_h_Statics::EnumInfo));
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
