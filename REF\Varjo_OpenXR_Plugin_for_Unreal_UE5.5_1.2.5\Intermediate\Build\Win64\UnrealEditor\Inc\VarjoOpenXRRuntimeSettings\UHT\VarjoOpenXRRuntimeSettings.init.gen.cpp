// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeVarjoOpenXRRuntimeSettings_init() {}
	static FPackageRegistrationInfo Z_Registration_Info_UPackage__Script_VarjoOpenXRRuntimeSettings;
	FORCENOINLINE UPackage* Z_Construct_UPackage__Script_VarjoOpenXRRuntimeSettings()
	{
		if (!Z_Registration_Info_UPackage__Script_VarjoOpenXRRuntimeSettings.OuterSingleton)
		{
			static const UECodeGen_Private::FPackageParams PackageParams = {
				"/Script/VarjoOpenXRRuntimeSettings",
				nullptr,
				0,
				PKG_CompiledIn | 0x00000000,
				0x5DFAB170,
				0x4DE5C281,
				METADATA_PARAMS(0, nullptr)
			};
			UECodeGen_Private::ConstructUPackage(Z_Registration_Info_UPackage__Script_VarjoOpenXRRuntimeSettings.OuterSingleton, PackageParams);
		}
		return Z_Registration_Info_UPackage__Script_VarjoOpenXRRuntimeSettings.OuterSingleton;
	}
	static FRegisterCompiledInInfo Z_CompiledInDeferPackage_UPackage__Script_VarjoOpenXRRuntimeSettings(Z_Construct_UPackage__Script_VarjoOpenXRRuntimeSettings, TEXT("/Script/VarjoOpenXRRuntimeSettings"), Z_Registration_Info_UPackage__Script_VarjoOpenXRRuntimeSettings, CONSTRUCT_RELOAD_VERSION_INFO(FPackageReloadVersionInfo, 0x5DFAB170, 0x4DE5C281));
PRAGMA_ENABLE_DEPRECATION_WARNINGS
