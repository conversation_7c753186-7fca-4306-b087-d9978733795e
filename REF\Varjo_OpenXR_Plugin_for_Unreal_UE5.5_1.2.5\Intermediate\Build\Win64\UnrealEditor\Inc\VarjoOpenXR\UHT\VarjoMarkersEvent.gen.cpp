// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "VarjoOpenXR/Public/VarjoMarkersEvent.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeVarjoMarkersEvent() {}

// Begin Cross Module References
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FRotator();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector2D();
ENGINE_API UClass* Z_Construct_UClass_UActorComponent();
UPackage* Z_Construct_UPackage__Script_VarjoOpenXR();
VARJOOPENXR_API UClass* Z_Construct_UClass_UVarjoMarkerDelegates();
VARJOOPENXR_API UClass* Z_Construct_UClass_UVarjoMarkerDelegates_NoRegister();
VARJOOPENXR_API UClass* Z_Construct_UClass_UVarjoMarkersEvent();
VARJOOPENXR_API UClass* Z_Construct_UClass_UVarjoMarkersEvent_NoRegister();
VARJOOPENXR_API UFunction* Z_Construct_UDelegateFunction_UVarjoMarkersEvent_NewVarjoMarkerDetected__DelegateSignature();
VARJOOPENXR_API UFunction* Z_Construct_UDelegateFunction_UVarjoMarkersEvent_VarjoMarkerLost__DelegateSignature();
VARJOOPENXR_API UFunction* Z_Construct_UDelegateFunction_UVarjoMarkersEvent_VarjoMarkerMoved__DelegateSignature();
// End Cross Module References

// Begin Class UVarjoMarkerDelegates
void UVarjoMarkerDelegates::StaticRegisterNativesUVarjoMarkerDelegates()
{
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(UVarjoMarkerDelegates);
UClass* Z_Construct_UClass_UVarjoMarkerDelegates_NoRegister()
{
	return UVarjoMarkerDelegates::StaticClass();
}
struct Z_Construct_UClass_UVarjoMarkerDelegates_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "ClassGroupNames", "OpenXR" },
		{ "Comment", "/**\n* Delegates for blueprints are implemented in UVarjoMarkersEvent(UActorComponent).\n* These are static delegate objects that feed data into the delegates in UVarjoMarkersEvent.\n* Made visible here so that C++ side can use them directly.\n*/" },
		{ "IncludePath", "VarjoMarkersEvent.h" },
		{ "ModuleRelativePath", "Public/VarjoMarkersEvent.h" },
		{ "ToolTip", "Delegates for blueprints are implemented in UVarjoMarkersEvent(UActorComponent).\nThese are static delegate objects that feed data into the delegates in UVarjoMarkersEvent.\nMade visible here so that C++ side can use them directly." },
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UVarjoMarkerDelegates>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_UVarjoMarkerDelegates_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_VarjoOpenXR,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UVarjoMarkerDelegates_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UVarjoMarkerDelegates_Statics::ClassParams = {
	&UVarjoMarkerDelegates::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	0,
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UVarjoMarkerDelegates_Statics::Class_MetaDataParams), Z_Construct_UClass_UVarjoMarkerDelegates_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UVarjoMarkerDelegates()
{
	if (!Z_Registration_Info_UClass_UVarjoMarkerDelegates.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UVarjoMarkerDelegates.OuterSingleton, Z_Construct_UClass_UVarjoMarkerDelegates_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UVarjoMarkerDelegates.OuterSingleton;
}
template<> VARJOOPENXR_API UClass* StaticClass<UVarjoMarkerDelegates>()
{
	return UVarjoMarkerDelegates::StaticClass();
}
UVarjoMarkerDelegates::UVarjoMarkerDelegates(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UVarjoMarkerDelegates);
UVarjoMarkerDelegates::~UVarjoMarkerDelegates() {}
// End Class UVarjoMarkerDelegates

// Begin Delegate FNewVarjoMarkerDetected
struct Z_Construct_UDelegateFunction_UVarjoMarkersEvent_NewVarjoMarkerDetected__DelegateSignature_Statics
{
	struct VarjoMarkersEvent_eventNewVarjoMarkerDetected_Parms
	{
		int32 MarkerId;
		FVector Position;
		FRotator Rotation;
		FVector2D Size;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/VarjoMarkersEvent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rotation_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Size_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_MarkerId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Rotation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Size;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_UVarjoMarkersEvent_NewVarjoMarkerDetected__DelegateSignature_Statics::NewProp_MarkerId = { "MarkerId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VarjoMarkersEvent_eventNewVarjoMarkerDetected_Parms, MarkerId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UVarjoMarkersEvent_NewVarjoMarkerDetected__DelegateSignature_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VarjoMarkersEvent_eventNewVarjoMarkerDetected_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UVarjoMarkersEvent_NewVarjoMarkerDetected__DelegateSignature_Statics::NewProp_Rotation = { "Rotation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VarjoMarkersEvent_eventNewVarjoMarkerDetected_Parms, Rotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rotation_MetaData), NewProp_Rotation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UVarjoMarkersEvent_NewVarjoMarkerDetected__DelegateSignature_Statics::NewProp_Size = { "Size", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VarjoMarkersEvent_eventNewVarjoMarkerDetected_Parms, Size), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Size_MetaData), NewProp_Size_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UVarjoMarkersEvent_NewVarjoMarkerDetected__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UVarjoMarkersEvent_NewVarjoMarkerDetected__DelegateSignature_Statics::NewProp_MarkerId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UVarjoMarkersEvent_NewVarjoMarkerDetected__DelegateSignature_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UVarjoMarkersEvent_NewVarjoMarkerDetected__DelegateSignature_Statics::NewProp_Rotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UVarjoMarkersEvent_NewVarjoMarkerDetected__DelegateSignature_Statics::NewProp_Size,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UVarjoMarkersEvent_NewVarjoMarkerDetected__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_UVarjoMarkersEvent_NewVarjoMarkerDetected__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UVarjoMarkersEvent, nullptr, "NewVarjoMarkerDetected__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_UVarjoMarkersEvent_NewVarjoMarkerDetected__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UVarjoMarkersEvent_NewVarjoMarkerDetected__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UVarjoMarkersEvent_NewVarjoMarkerDetected__DelegateSignature_Statics::VarjoMarkersEvent_eventNewVarjoMarkerDetected_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00D30000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UVarjoMarkersEvent_NewVarjoMarkerDetected__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UVarjoMarkersEvent_NewVarjoMarkerDetected__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_UVarjoMarkersEvent_NewVarjoMarkerDetected__DelegateSignature_Statics::VarjoMarkersEvent_eventNewVarjoMarkerDetected_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UVarjoMarkersEvent_NewVarjoMarkerDetected__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UVarjoMarkersEvent_NewVarjoMarkerDetected__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UVarjoMarkersEvent::FNewVarjoMarkerDetected_DelegateWrapper(const FMulticastScriptDelegate& NewVarjoMarkerDetected, int32 MarkerId, FVector const& Position, FRotator const& Rotation, FVector2D const& Size)
{
	struct VarjoMarkersEvent_eventNewVarjoMarkerDetected_Parms
	{
		int32 MarkerId;
		FVector Position;
		FRotator Rotation;
		FVector2D Size;
	};
	VarjoMarkersEvent_eventNewVarjoMarkerDetected_Parms Parms;
	Parms.MarkerId=MarkerId;
	Parms.Position=Position;
	Parms.Rotation=Rotation;
	Parms.Size=Size;
	NewVarjoMarkerDetected.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FNewVarjoMarkerDetected

// Begin Delegate FVarjoMarkerMoved
struct Z_Construct_UDelegateFunction_UVarjoMarkersEvent_VarjoMarkerMoved__DelegateSignature_Statics
{
	struct VarjoMarkersEvent_eventVarjoMarkerMoved_Parms
	{
		int32 MarkerId;
		FVector Position;
		FRotator Rotation;
		FVector2D Size;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/VarjoMarkersEvent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rotation_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Size_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_MarkerId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Rotation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Size;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_UVarjoMarkersEvent_VarjoMarkerMoved__DelegateSignature_Statics::NewProp_MarkerId = { "MarkerId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VarjoMarkersEvent_eventVarjoMarkerMoved_Parms, MarkerId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UVarjoMarkersEvent_VarjoMarkerMoved__DelegateSignature_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VarjoMarkersEvent_eventVarjoMarkerMoved_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UVarjoMarkersEvent_VarjoMarkerMoved__DelegateSignature_Statics::NewProp_Rotation = { "Rotation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VarjoMarkersEvent_eventVarjoMarkerMoved_Parms, Rotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rotation_MetaData), NewProp_Rotation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UVarjoMarkersEvent_VarjoMarkerMoved__DelegateSignature_Statics::NewProp_Size = { "Size", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VarjoMarkersEvent_eventVarjoMarkerMoved_Parms, Size), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Size_MetaData), NewProp_Size_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UVarjoMarkersEvent_VarjoMarkerMoved__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UVarjoMarkersEvent_VarjoMarkerMoved__DelegateSignature_Statics::NewProp_MarkerId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UVarjoMarkersEvent_VarjoMarkerMoved__DelegateSignature_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UVarjoMarkersEvent_VarjoMarkerMoved__DelegateSignature_Statics::NewProp_Rotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UVarjoMarkersEvent_VarjoMarkerMoved__DelegateSignature_Statics::NewProp_Size,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UVarjoMarkersEvent_VarjoMarkerMoved__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_UVarjoMarkersEvent_VarjoMarkerMoved__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UVarjoMarkersEvent, nullptr, "VarjoMarkerMoved__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_UVarjoMarkersEvent_VarjoMarkerMoved__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UVarjoMarkersEvent_VarjoMarkerMoved__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UVarjoMarkersEvent_VarjoMarkerMoved__DelegateSignature_Statics::VarjoMarkersEvent_eventVarjoMarkerMoved_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00D30000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UVarjoMarkersEvent_VarjoMarkerMoved__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UVarjoMarkersEvent_VarjoMarkerMoved__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_UVarjoMarkersEvent_VarjoMarkerMoved__DelegateSignature_Statics::VarjoMarkersEvent_eventVarjoMarkerMoved_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UVarjoMarkersEvent_VarjoMarkerMoved__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UVarjoMarkersEvent_VarjoMarkerMoved__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UVarjoMarkersEvent::FVarjoMarkerMoved_DelegateWrapper(const FMulticastScriptDelegate& VarjoMarkerMoved, int32 MarkerId, FVector const& Position, FRotator const& Rotation, FVector2D const& Size)
{
	struct VarjoMarkersEvent_eventVarjoMarkerMoved_Parms
	{
		int32 MarkerId;
		FVector Position;
		FRotator Rotation;
		FVector2D Size;
	};
	VarjoMarkersEvent_eventVarjoMarkerMoved_Parms Parms;
	Parms.MarkerId=MarkerId;
	Parms.Position=Position;
	Parms.Rotation=Rotation;
	Parms.Size=Size;
	VarjoMarkerMoved.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FVarjoMarkerMoved

// Begin Delegate FVarjoMarkerLost
struct Z_Construct_UDelegateFunction_UVarjoMarkersEvent_VarjoMarkerLost__DelegateSignature_Statics
{
	struct VarjoMarkersEvent_eventVarjoMarkerLost_Parms
	{
		int32 MarkerId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/VarjoMarkersEvent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_MarkerId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_UVarjoMarkersEvent_VarjoMarkerLost__DelegateSignature_Statics::NewProp_MarkerId = { "MarkerId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VarjoMarkersEvent_eventVarjoMarkerLost_Parms, MarkerId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UVarjoMarkersEvent_VarjoMarkerLost__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UVarjoMarkersEvent_VarjoMarkerLost__DelegateSignature_Statics::NewProp_MarkerId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UVarjoMarkersEvent_VarjoMarkerLost__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_UVarjoMarkersEvent_VarjoMarkerLost__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UVarjoMarkersEvent, nullptr, "VarjoMarkerLost__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_UVarjoMarkersEvent_VarjoMarkerLost__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UVarjoMarkersEvent_VarjoMarkerLost__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UVarjoMarkersEvent_VarjoMarkerLost__DelegateSignature_Statics::VarjoMarkersEvent_eventVarjoMarkerLost_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UVarjoMarkersEvent_VarjoMarkerLost__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UVarjoMarkersEvent_VarjoMarkerLost__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_UVarjoMarkersEvent_VarjoMarkerLost__DelegateSignature_Statics::VarjoMarkersEvent_eventVarjoMarkerLost_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UVarjoMarkersEvent_VarjoMarkerLost__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UVarjoMarkersEvent_VarjoMarkerLost__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UVarjoMarkersEvent::FVarjoMarkerLost_DelegateWrapper(const FMulticastScriptDelegate& VarjoMarkerLost, int32 MarkerId)
{
	struct VarjoMarkersEvent_eventVarjoMarkerLost_Parms
	{
		int32 MarkerId;
	};
	VarjoMarkersEvent_eventVarjoMarkerLost_Parms Parms;
	Parms.MarkerId=MarkerId;
	VarjoMarkerLost.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FVarjoMarkerLost

// Begin Class UVarjoMarkersEvent
void UVarjoMarkersEvent::StaticRegisterNativesUVarjoMarkersEvent()
{
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(UVarjoMarkersEvent);
UClass* Z_Construct_UClass_UVarjoMarkersEvent_NoRegister()
{
	return UVarjoMarkersEvent::StaticClass();
}
struct Z_Construct_UClass_UVarjoMarkersEvent_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "BlueprintType", "true" },
		{ "IncludePath", "VarjoMarkersEvent.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/VarjoMarkersEvent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewVarjoMarkerDetected_MetaData[] = {
		{ "ModuleRelativePath", "Public/VarjoMarkersEvent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VarjoMarkerMoved_MetaData[] = {
		{ "ModuleRelativePath", "Public/VarjoMarkersEvent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VarjoMarkerLost_MetaData[] = {
		{ "ModuleRelativePath", "Public/VarjoMarkersEvent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_NewVarjoMarkerDetected;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_VarjoMarkerMoved;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_VarjoMarkerLost;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UDelegateFunction_UVarjoMarkersEvent_NewVarjoMarkerDetected__DelegateSignature, "NewVarjoMarkerDetected__DelegateSignature" }, // 649208308
		{ &Z_Construct_UDelegateFunction_UVarjoMarkersEvent_VarjoMarkerLost__DelegateSignature, "VarjoMarkerLost__DelegateSignature" }, // 3842129804
		{ &Z_Construct_UDelegateFunction_UVarjoMarkersEvent_VarjoMarkerMoved__DelegateSignature, "VarjoMarkerMoved__DelegateSignature" }, // 2053522512
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UVarjoMarkersEvent>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UVarjoMarkersEvent_Statics::NewProp_NewVarjoMarkerDetected = { "NewVarjoMarkerDetected", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UVarjoMarkersEvent, NewVarjoMarkerDetected), Z_Construct_UDelegateFunction_UVarjoMarkersEvent_NewVarjoMarkerDetected__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewVarjoMarkerDetected_MetaData), NewProp_NewVarjoMarkerDetected_MetaData) }; // 649208308
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UVarjoMarkersEvent_Statics::NewProp_VarjoMarkerMoved = { "VarjoMarkerMoved", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UVarjoMarkersEvent, VarjoMarkerMoved), Z_Construct_UDelegateFunction_UVarjoMarkersEvent_VarjoMarkerMoved__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VarjoMarkerMoved_MetaData), NewProp_VarjoMarkerMoved_MetaData) }; // 2053522512
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UVarjoMarkersEvent_Statics::NewProp_VarjoMarkerLost = { "VarjoMarkerLost", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UVarjoMarkersEvent, VarjoMarkerLost), Z_Construct_UDelegateFunction_UVarjoMarkersEvent_VarjoMarkerLost__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VarjoMarkerLost_MetaData), NewProp_VarjoMarkerLost_MetaData) }; // 3842129804
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UVarjoMarkersEvent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UVarjoMarkersEvent_Statics::NewProp_NewVarjoMarkerDetected,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UVarjoMarkersEvent_Statics::NewProp_VarjoMarkerMoved,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UVarjoMarkersEvent_Statics::NewProp_VarjoMarkerLost,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UVarjoMarkersEvent_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UVarjoMarkersEvent_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UActorComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_VarjoOpenXR,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UVarjoMarkersEvent_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UVarjoMarkersEvent_Statics::ClassParams = {
	&UVarjoMarkersEvent::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UVarjoMarkersEvent_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UVarjoMarkersEvent_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UVarjoMarkersEvent_Statics::Class_MetaDataParams), Z_Construct_UClass_UVarjoMarkersEvent_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UVarjoMarkersEvent()
{
	if (!Z_Registration_Info_UClass_UVarjoMarkersEvent.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UVarjoMarkersEvent.OuterSingleton, Z_Construct_UClass_UVarjoMarkersEvent_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UVarjoMarkersEvent.OuterSingleton;
}
template<> VARJOOPENXR_API UClass* StaticClass<UVarjoMarkersEvent>()
{
	return UVarjoMarkersEvent::StaticClass();
}
UVarjoMarkersEvent::UVarjoMarkersEvent(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UVarjoMarkersEvent);
UVarjoMarkersEvent::~UVarjoMarkersEvent() {}
// End Class UVarjoMarkersEvent

// Begin Registration
struct Z_CompiledInDeferFile_FID_HostProject_Plugins_VarjoOpenXR_Source_VarjoOpenXR_Public_VarjoMarkersEvent_h_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UVarjoMarkerDelegates, UVarjoMarkerDelegates::StaticClass, TEXT("UVarjoMarkerDelegates"), &Z_Registration_Info_UClass_UVarjoMarkerDelegates, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UVarjoMarkerDelegates), 619751898U) },
		{ Z_Construct_UClass_UVarjoMarkersEvent, UVarjoMarkersEvent::StaticClass, TEXT("UVarjoMarkersEvent"), &Z_Registration_Info_UClass_UVarjoMarkersEvent, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UVarjoMarkersEvent), 2339267970U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_HostProject_Plugins_VarjoOpenXR_Source_VarjoOpenXR_Public_VarjoMarkersEvent_h_3157092284(TEXT("/Script/VarjoOpenXR"),
	Z_CompiledInDeferFile_FID_HostProject_Plugins_VarjoOpenXR_Source_VarjoOpenXR_Public_VarjoMarkersEvent_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_HostProject_Plugins_VarjoOpenXR_Source_VarjoOpenXR_Public_VarjoMarkersEvent_h_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
