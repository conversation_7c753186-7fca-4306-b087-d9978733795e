// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeVarjoOpenXR_init() {}
	VARJOOPENXR_API UFunction* Z_Construct_UDelegateFunction_UVarjoMarkersEvent_NewVarjoMarkerDetected__DelegateSignature();
	VARJOOPENXR_API UFunction* Z_Construct_UDelegateFunction_UVarjoMarkersEvent_VarjoMarkerLost__DelegateSignature();
	VARJOOPENXR_API UFunction* Z_Construct_UDelegateFunction_UVarjoMarkersEvent_VarjoMarkerMoved__DelegateSignature();
	static FPackageRegistrationInfo Z_Registration_Info_UPackage__Script_VarjoOpenXR;
	FORCENOINLINE UPackage* Z_Construct_UPackage__Script_VarjoOpenXR()
	{
		if (!Z_Registration_Info_UPackage__Script_VarjoOpenXR.OuterSingleton)
		{
			static UObject* (*const SingletonFuncArray[])() = {
				(UObject* (*)())Z_Construct_UDelegateFunction_UVarjoMarkersEvent_NewVarjoMarkerDetected__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UVarjoMarkersEvent_VarjoMarkerLost__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UVarjoMarkersEvent_VarjoMarkerMoved__DelegateSignature,
			};
			static const UECodeGen_Private::FPackageParams PackageParams = {
				"/Script/VarjoOpenXR",
				SingletonFuncArray,
				UE_ARRAY_COUNT(SingletonFuncArray),
				PKG_CompiledIn | 0x00000000,
				0xA13EFDDD,
				0x0AC2FF94,
				METADATA_PARAMS(0, nullptr)
			};
			UECodeGen_Private::ConstructUPackage(Z_Registration_Info_UPackage__Script_VarjoOpenXR.OuterSingleton, PackageParams);
		}
		return Z_Registration_Info_UPackage__Script_VarjoOpenXR.OuterSingleton;
	}
	static FRegisterCompiledInInfo Z_CompiledInDeferPackage_UPackage__Script_VarjoOpenXR(Z_Construct_UPackage__Script_VarjoOpenXR, TEXT("/Script/VarjoOpenXR"), Z_Registration_Info_UPackage__Script_VarjoOpenXR, CONSTRUCT_RELOAD_VERSION_INFO(FPackageReloadVersionInfo, 0xA13EFDDD, 0x0AC2FF94));
PRAGMA_ENABLE_DEPRECATION_WARNINGS
