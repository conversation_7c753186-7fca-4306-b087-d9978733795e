#ifndef OPENXR_REFLECTION_H_
#define OPENXR_REFLECTION_H_ 1

/*
** Copyright (c) 2017-2022, The Khronos Group Inc.
**
** SPDX-License-Identifier: Apache-2.0 OR MIT
*/

/*
** This header is generated from the Khronos OpenXR XML API Registry.
**
*/

#include "openxr.h"

/*
This file contains expansion macros (X Macros) for OpenXR enumerations and structures.
Example of how to use expansion macros to make an enum-to-string function:

#define XR_ENUM_CASE_STR(name, val) case name: return #name;
#define XR_ENUM_STR(enumType)                         \
    constexpr const char* XrEnumStr(enumType e) {     \
        switch (e) {                                  \
            XR_LIST_ENUM_##enumType(XR_ENUM_CASE_STR) \
            default: return "Unknown";                \
        }                                             \
    }                                                 \

XR_ENUM_STR(XrResult);
*/

#define XR_LIST_ENUM_XrResult(_) \
    _(XR_SUCCESS, 0) \
    _(XR_TIMEOUT_EXPIRED, 1) \
    _(XR_SESSION_LOSS_PENDING, 3) \
    _(XR_EVENT_UNAVAILABLE, 4) \
    _(XR_SPACE_BOUNDS_UNAVAILABLE, 7) \
    _(XR_SESSION_NOT_FOCUSED, 8) \
    _(XR_FRAME_DISCARDED, 9) \
    _(XR_ERROR_VALIDATION_FAILURE, -1) \
    _(XR_ERROR_RUNTIME_FAILURE, -2) \
    _(XR_ERROR_OUT_OF_MEMORY, -3) \
    _(XR_ERROR_API_VERSION_UNSUPPORTED, -4) \
    _(XR_ERROR_INITIALIZATION_FAILED, -6) \
    _(XR_ERROR_FUNCTION_UNSUPPORTED, -7) \
    _(XR_ERROR_FEATURE_UNSUPPORTED, -8) \
    _(XR_ERROR_EXTENSION_NOT_PRESENT, -9) \
    _(XR_ERROR_LIMIT_REACHED, -10) \
    _(XR_ERROR_SIZE_INSUFFICIENT, -11) \
    _(XR_ERROR_HANDLE_INVALID, -12) \
    _(XR_ERROR_INSTANCE_LOST, -13) \
    _(XR_ERROR_SESSION_RUNNING, -14) \
    _(XR_ERROR_SESSION_NOT_RUNNING, -16) \
    _(XR_ERROR_SESSION_LOST, -17) \
    _(XR_ERROR_SYSTEM_INVALID, -18) \
    _(XR_ERROR_PATH_INVALID, -19) \
    _(XR_ERROR_PATH_COUNT_EXCEEDED, -20) \
    _(XR_ERROR_PATH_FORMAT_INVALID, -21) \
    _(XR_ERROR_PATH_UNSUPPORTED, -22) \
    _(XR_ERROR_LAYER_INVALID, -23) \
    _(XR_ERROR_LAYER_LIMIT_EXCEEDED, -24) \
    _(XR_ERROR_SWAPCHAIN_RECT_INVALID, -25) \
    _(XR_ERROR_SWAPCHAIN_FORMAT_UNSUPPORTED, -26) \
    _(XR_ERROR_ACTION_TYPE_MISMATCH, -27) \
    _(XR_ERROR_SESSION_NOT_READY, -28) \
    _(XR_ERROR_SESSION_NOT_STOPPING, -29) \
    _(XR_ERROR_TIME_INVALID, -30) \
    _(XR_ERROR_REFERENCE_SPACE_UNSUPPORTED, -31) \
    _(XR_ERROR_FILE_ACCESS_ERROR, -32) \
    _(XR_ERROR_FILE_CONTENTS_INVALID, -33) \
    _(XR_ERROR_FORM_FACTOR_UNSUPPORTED, -34) \
    _(XR_ERROR_FORM_FACTOR_UNAVAILABLE, -35) \
    _(XR_ERROR_API_LAYER_NOT_PRESENT, -36) \
    _(XR_ERROR_CALL_ORDER_INVALID, -37) \
    _(XR_ERROR_GRAPHICS_DEVICE_INVALID, -38) \
    _(XR_ERROR_POSE_INVALID, -39) \
    _(XR_ERROR_INDEX_OUT_OF_RANGE, -40) \
    _(XR_ERROR_VIEW_CONFIGURATION_TYPE_UNSUPPORTED, -41) \
    _(XR_ERROR_ENVIRONMENT_BLEND_MODE_UNSUPPORTED, -42) \
    _(XR_ERROR_NAME_DUPLICATED, -44) \
    _(XR_ERROR_NAME_INVALID, -45) \
    _(XR_ERROR_ACTIONSET_NOT_ATTACHED, -46) \
    _(XR_ERROR_ACTIONSETS_ALREADY_ATTACHED, -47) \
    _(XR_ERROR_LOCALIZED_NAME_DUPLICATED, -48) \
    _(XR_ERROR_LOCALIZED_NAME_INVALID, -49) \
    _(XR_ERROR_GRAPHICS_REQUIREMENTS_CALL_MISSING, -50) \
    _(XR_ERROR_RUNTIME_UNAVAILABLE, -51) \
    _(XR_ERROR_ANDROID_THREAD_SETTINGS_ID_INVALID_KHR, -1000003000) \
    _(XR_ERROR_ANDROID_THREAD_SETTINGS_FAILURE_KHR, -1000003001) \
    _(XR_ERROR_CREATE_SPATIAL_ANCHOR_FAILED_MSFT, -1000039001) \
    _(XR_ERROR_SECONDARY_VIEW_CONFIGURATION_TYPE_NOT_ENABLED_MSFT, -1000053000) \
    _(XR_ERROR_CONTROLLER_MODEL_KEY_INVALID_MSFT, -1000055000) \
    _(XR_ERROR_REPROJECTION_MODE_UNSUPPORTED_MSFT, -1000066000) \
    _(XR_ERROR_COMPUTE_NEW_SCENE_NOT_COMPLETED_MSFT, -1000097000) \
    _(XR_ERROR_SCENE_COMPONENT_ID_INVALID_MSFT, -1000097001) \
    _(XR_ERROR_SCENE_COMPONENT_TYPE_MISMATCH_MSFT, -1000097002) \
    _(XR_ERROR_SCENE_MESH_BUFFER_ID_INVALID_MSFT, -1000097003) \
    _(XR_ERROR_SCENE_COMPUTE_FEATURE_INCOMPATIBLE_MSFT, -1000097004) \
    _(XR_ERROR_SCENE_COMPUTE_CONSISTENCY_MISMATCH_MSFT, -1000097005) \
    _(XR_ERROR_DISPLAY_REFRESH_RATE_UNSUPPORTED_FB, -1000101000) \
    _(XR_ERROR_COLOR_SPACE_UNSUPPORTED_FB, -1000108000) \
    _(XR_ERROR_SPACE_COMPONENT_NOT_SUPPORTED_FB, -1000113000) \
    _(XR_ERROR_SPACE_COMPONENT_NOT_ENABLED_FB, -1000113001) \
    _(XR_ERROR_SPACE_COMPONENT_STATUS_PENDING_FB, -1000113002) \
    _(XR_ERROR_SPACE_COMPONENT_STATUS_ALREADY_SET_FB, -1000113003) \
    _(XR_ERROR_UNEXPECTED_STATE_PASSTHROUGH_FB, -1000118000) \
    _(XR_ERROR_FEATURE_ALREADY_CREATED_PASSTHROUGH_FB, -1000118001) \
    _(XR_ERROR_FEATURE_REQUIRED_PASSTHROUGH_FB, -1000118002) \
    _(XR_ERROR_NOT_PERMITTED_PASSTHROUGH_FB, -1000118003) \
    _(XR_ERROR_INSUFFICIENT_RESOURCES_PASSTHROUGH_FB, -1000118004) \
    _(XR_ERROR_UNKNOWN_PASSTHROUGH_FB, -1000118050) \
    _(XR_ERROR_RENDER_MODEL_KEY_INVALID_FB, -1000119000) \
    _(XR_RENDER_MODEL_UNAVAILABLE_FB, 1000119020) \
    _(XR_ERROR_MARKER_NOT_TRACKED_VARJO, -1000124000) \
    _(XR_ERROR_MARKER_ID_INVALID_VARJO, -1000124001) \
    _(XR_ERROR_SPATIAL_ANCHOR_NAME_NOT_FOUND_MSFT, -1000142001) \
    _(XR_ERROR_SPATIAL_ANCHOR_NAME_INVALID_MSFT, -1000142002) \
    _(XR_RESULT_MAX_ENUM, 0x7FFFFFFF)

#define XR_LIST_ENUM_XrStructureType(_) \
    _(XR_TYPE_UNKNOWN, 0) \
    _(XR_TYPE_API_LAYER_PROPERTIES, 1) \
    _(XR_TYPE_EXTENSION_PROPERTIES, 2) \
    _(XR_TYPE_INSTANCE_CREATE_INFO, 3) \
    _(XR_TYPE_SYSTEM_GET_INFO, 4) \
    _(XR_TYPE_SYSTEM_PROPERTIES, 5) \
    _(XR_TYPE_VIEW_LOCATE_INFO, 6) \
    _(XR_TYPE_VIEW, 7) \
    _(XR_TYPE_SESSION_CREATE_INFO, 8) \
    _(XR_TYPE_SWAPCHAIN_CREATE_INFO, 9) \
    _(XR_TYPE_SESSION_BEGIN_INFO, 10) \
    _(XR_TYPE_VIEW_STATE, 11) \
    _(XR_TYPE_FRAME_END_INFO, 12) \
    _(XR_TYPE_HAPTIC_VIBRATION, 13) \
    _(XR_TYPE_EVENT_DATA_BUFFER, 16) \
    _(XR_TYPE_EVENT_DATA_INSTANCE_LOSS_PENDING, 17) \
    _(XR_TYPE_EVENT_DATA_SESSION_STATE_CHANGED, 18) \
    _(XR_TYPE_ACTION_STATE_BOOLEAN, 23) \
    _(XR_TYPE_ACTION_STATE_FLOAT, 24) \
    _(XR_TYPE_ACTION_STATE_VECTOR2F, 25) \
    _(XR_TYPE_ACTION_STATE_POSE, 27) \
    _(XR_TYPE_ACTION_SET_CREATE_INFO, 28) \
    _(XR_TYPE_ACTION_CREATE_INFO, 29) \
    _(XR_TYPE_INSTANCE_PROPERTIES, 32) \
    _(XR_TYPE_FRAME_WAIT_INFO, 33) \
    _(XR_TYPE_COMPOSITION_LAYER_PROJECTION, 35) \
    _(XR_TYPE_COMPOSITION_LAYER_QUAD, 36) \
    _(XR_TYPE_REFERENCE_SPACE_CREATE_INFO, 37) \
    _(XR_TYPE_ACTION_SPACE_CREATE_INFO, 38) \
    _(XR_TYPE_EVENT_DATA_REFERENCE_SPACE_CHANGE_PENDING, 40) \
    _(XR_TYPE_VIEW_CONFIGURATION_VIEW, 41) \
    _(XR_TYPE_SPACE_LOCATION, 42) \
    _(XR_TYPE_SPACE_VELOCITY, 43) \
    _(XR_TYPE_FRAME_STATE, 44) \
    _(XR_TYPE_VIEW_CONFIGURATION_PROPERTIES, 45) \
    _(XR_TYPE_FRAME_BEGIN_INFO, 46) \
    _(XR_TYPE_COMPOSITION_LAYER_PROJECTION_VIEW, 48) \
    _(XR_TYPE_EVENT_DATA_EVENTS_LOST, 49) \
    _(XR_TYPE_INTERACTION_PROFILE_SUGGESTED_BINDING, 51) \
    _(XR_TYPE_EVENT_DATA_INTERACTION_PROFILE_CHANGED, 52) \
    _(XR_TYPE_INTERACTION_PROFILE_STATE, 53) \
    _(XR_TYPE_SWAPCHAIN_IMAGE_ACQUIRE_INFO, 55) \
    _(XR_TYPE_SWAPCHAIN_IMAGE_WAIT_INFO, 56) \
    _(XR_TYPE_SWAPCHAIN_IMAGE_RELEASE_INFO, 57) \
    _(XR_TYPE_ACTION_STATE_GET_INFO, 58) \
    _(XR_TYPE_HAPTIC_ACTION_INFO, 59) \
    _(XR_TYPE_SESSION_ACTION_SETS_ATTACH_INFO, 60) \
    _(XR_TYPE_ACTIONS_SYNC_INFO, 61) \
    _(XR_TYPE_BOUND_SOURCES_FOR_ACTION_ENUMERATE_INFO, 62) \
    _(XR_TYPE_INPUT_SOURCE_LOCALIZED_NAME_GET_INFO, 63) \
    _(XR_TYPE_COMPOSITION_LAYER_CUBE_KHR, 1000006000) \
    _(XR_TYPE_INSTANCE_CREATE_INFO_ANDROID_KHR, 1000008000) \
    _(XR_TYPE_COMPOSITION_LAYER_DEPTH_INFO_KHR, 1000010000) \
    _(XR_TYPE_VULKAN_SWAPCHAIN_FORMAT_LIST_CREATE_INFO_KHR, 1000014000) \
    _(XR_TYPE_EVENT_DATA_PERF_SETTINGS_EXT, 1000015000) \
    _(XR_TYPE_COMPOSITION_LAYER_CYLINDER_KHR, 1000017000) \
    _(XR_TYPE_COMPOSITION_LAYER_EQUIRECT_KHR, 1000018000) \
    _(XR_TYPE_DEBUG_UTILS_OBJECT_NAME_INFO_EXT, 1000019000) \
    _(XR_TYPE_DEBUG_UTILS_MESSENGER_CALLBACK_DATA_EXT, 1000019001) \
    _(XR_TYPE_DEBUG_UTILS_MESSENGER_CREATE_INFO_EXT, 1000019002) \
    _(XR_TYPE_DEBUG_UTILS_LABEL_EXT, 1000019003) \
    _(XR_TYPE_GRAPHICS_BINDING_OPENGL_WIN32_KHR, 1000023000) \
    _(XR_TYPE_GRAPHICS_BINDING_OPENGL_XLIB_KHR, 1000023001) \
    _(XR_TYPE_GRAPHICS_BINDING_OPENGL_XCB_KHR, 1000023002) \
    _(XR_TYPE_GRAPHICS_BINDING_OPENGL_WAYLAND_KHR, 1000023003) \
    _(XR_TYPE_SWAPCHAIN_IMAGE_OPENGL_KHR, 1000023004) \
    _(XR_TYPE_GRAPHICS_REQUIREMENTS_OPENGL_KHR, 1000023005) \
    _(XR_TYPE_GRAPHICS_BINDING_OPENGL_ES_ANDROID_KHR, 1000024001) \
    _(XR_TYPE_SWAPCHAIN_IMAGE_OPENGL_ES_KHR, 1000024002) \
    _(XR_TYPE_GRAPHICS_REQUIREMENTS_OPENGL_ES_KHR, 1000024003) \
    _(XR_TYPE_GRAPHICS_BINDING_VULKAN_KHR, 1000025000) \
    _(XR_TYPE_SWAPCHAIN_IMAGE_VULKAN_KHR, 1000025001) \
    _(XR_TYPE_GRAPHICS_REQUIREMENTS_VULKAN_KHR, 1000025002) \
    _(XR_TYPE_GRAPHICS_BINDING_D3D11_KHR, 1000027000) \
    _(XR_TYPE_SWAPCHAIN_IMAGE_D3D11_KHR, 1000027001) \
    _(XR_TYPE_GRAPHICS_REQUIREMENTS_D3D11_KHR, 1000027002) \
    _(XR_TYPE_GRAPHICS_BINDING_D3D12_KHR, 1000028000) \
    _(XR_TYPE_SWAPCHAIN_IMAGE_D3D12_KHR, 1000028001) \
    _(XR_TYPE_GRAPHICS_REQUIREMENTS_D3D12_KHR, 1000028002) \
    _(XR_TYPE_SYSTEM_EYE_GAZE_INTERACTION_PROPERTIES_EXT, 1000030000) \
    _(XR_TYPE_EYE_GAZE_SAMPLE_TIME_EXT, 1000030001) \
    _(XR_TYPE_VISIBILITY_MASK_KHR, 1000031000) \
    _(XR_TYPE_EVENT_DATA_VISIBILITY_MASK_CHANGED_KHR, 1000031001) \
    _(XR_TYPE_SESSION_CREATE_INFO_OVERLAY_EXTX, 1000033000) \
    _(XR_TYPE_EVENT_DATA_MAIN_SESSION_VISIBILITY_CHANGED_EXTX, 1000033003) \
    _(XR_TYPE_COMPOSITION_LAYER_COLOR_SCALE_BIAS_KHR, 1000034000) \
    _(XR_TYPE_SPATIAL_ANCHOR_CREATE_INFO_MSFT, 1000039000) \
    _(XR_TYPE_SPATIAL_ANCHOR_SPACE_CREATE_INFO_MSFT, 1000039001) \
    _(XR_TYPE_COMPOSITION_LAYER_IMAGE_LAYOUT_FB, 1000040000) \
    _(XR_TYPE_COMPOSITION_LAYER_ALPHA_BLEND_FB, 1000041001) \
    _(XR_TYPE_VIEW_CONFIGURATION_DEPTH_RANGE_EXT, 1000046000) \
    _(XR_TYPE_GRAPHICS_BINDING_EGL_MNDX, 1000048004) \
    _(XR_TYPE_SPATIAL_GRAPH_NODE_SPACE_CREATE_INFO_MSFT, 1000049000) \
    _(XR_TYPE_SPATIAL_GRAPH_STATIC_NODE_BINDING_CREATE_INFO_MSFT, 1000049001) \
    _(XR_TYPE_SPATIAL_GRAPH_NODE_BINDING_PROPERTIES_GET_INFO_MSFT, 1000049002) \
    _(XR_TYPE_SPATIAL_GRAPH_NODE_BINDING_PROPERTIES_MSFT, 1000049003) \
    _(XR_TYPE_SYSTEM_HAND_TRACKING_PROPERTIES_EXT, 1000051000) \
    _(XR_TYPE_HAND_TRACKER_CREATE_INFO_EXT, 1000051001) \
    _(XR_TYPE_HAND_JOINTS_LOCATE_INFO_EXT, 1000051002) \
    _(XR_TYPE_HAND_JOINT_LOCATIONS_EXT, 1000051003) \
    _(XR_TYPE_HAND_JOINT_VELOCITIES_EXT, 1000051004) \
    _(XR_TYPE_SYSTEM_HAND_TRACKING_MESH_PROPERTIES_MSFT, 1000052000) \
    _(XR_TYPE_HAND_MESH_SPACE_CREATE_INFO_MSFT, 1000052001) \
    _(XR_TYPE_HAND_MESH_UPDATE_INFO_MSFT, 1000052002) \
    _(XR_TYPE_HAND_MESH_MSFT, 1000052003) \
    _(XR_TYPE_HAND_POSE_TYPE_INFO_MSFT, 1000052004) \
    _(XR_TYPE_SECONDARY_VIEW_CONFIGURATION_SESSION_BEGIN_INFO_MSFT, 1000053000) \
    _(XR_TYPE_SECONDARY_VIEW_CONFIGURATION_STATE_MSFT, 1000053001) \
    _(XR_TYPE_SECONDARY_VIEW_CONFIGURATION_FRAME_STATE_MSFT, 1000053002) \
    _(XR_TYPE_SECONDARY_VIEW_CONFIGURATION_FRAME_END_INFO_MSFT, 1000053003) \
    _(XR_TYPE_SECONDARY_VIEW_CONFIGURATION_LAYER_INFO_MSFT, 1000053004) \
    _(XR_TYPE_SECONDARY_VIEW_CONFIGURATION_SWAPCHAIN_CREATE_INFO_MSFT, 1000053005) \
    _(XR_TYPE_CONTROLLER_MODEL_KEY_STATE_MSFT, 1000055000) \
    _(XR_TYPE_CONTROLLER_MODEL_NODE_PROPERTIES_MSFT, 1000055001) \
    _(XR_TYPE_CONTROLLER_MODEL_PROPERTIES_MSFT, 1000055002) \
    _(XR_TYPE_CONTROLLER_MODEL_NODE_STATE_MSFT, 1000055003) \
    _(XR_TYPE_CONTROLLER_MODEL_STATE_MSFT, 1000055004) \
    _(XR_TYPE_VIEW_CONFIGURATION_VIEW_FOV_EPIC, 1000059000) \
    _(XR_TYPE_HOLOGRAPHIC_WINDOW_ATTACHMENT_MSFT, 1000063000) \
    _(XR_TYPE_COMPOSITION_LAYER_REPROJECTION_INFO_MSFT, 1000066000) \
    _(XR_TYPE_COMPOSITION_LAYER_REPROJECTION_PLANE_OVERRIDE_MSFT, 1000066001) \
    _(XR_TYPE_ANDROID_SURFACE_SWAPCHAIN_CREATE_INFO_FB, 1000070000) \
    _(XR_TYPE_COMPOSITION_LAYER_SECURE_CONTENT_FB, 1000072000) \
    _(XR_TYPE_INTERACTION_PROFILE_DPAD_BINDING_EXT, 1000078000) \
    _(XR_TYPE_INTERACTION_PROFILE_ANALOG_THRESHOLD_VALVE, 1000079000) \
    _(XR_TYPE_HAND_JOINTS_MOTION_RANGE_INFO_EXT, 1000080000) \
    _(XR_TYPE_LOADER_INIT_INFO_ANDROID_KHR, 1000089000) \
    _(XR_TYPE_VULKAN_INSTANCE_CREATE_INFO_KHR, 1000090000) \
    _(XR_TYPE_VULKAN_DEVICE_CREATE_INFO_KHR, 1000090001) \
    _(XR_TYPE_VULKAN_GRAPHICS_DEVICE_GET_INFO_KHR, 1000090003) \
    _(XR_TYPE_COMPOSITION_LAYER_EQUIRECT2_KHR, 1000091000) \
    _(XR_TYPE_SCENE_OBSERVER_CREATE_INFO_MSFT, 1000097000) \
    _(XR_TYPE_SCENE_CREATE_INFO_MSFT, 1000097001) \
    _(XR_TYPE_NEW_SCENE_COMPUTE_INFO_MSFT, 1000097002) \
    _(XR_TYPE_VISUAL_MESH_COMPUTE_LOD_INFO_MSFT, 1000097003) \
    _(XR_TYPE_SCENE_COMPONENTS_MSFT, 1000097004) \
    _(XR_TYPE_SCENE_COMPONENTS_GET_INFO_MSFT, 1000097005) \
    _(XR_TYPE_SCENE_COMPONENT_LOCATIONS_MSFT, 1000097006) \
    _(XR_TYPE_SCENE_COMPONENTS_LOCATE_INFO_MSFT, 1000097007) \
    _(XR_TYPE_SCENE_OBJECTS_MSFT, 1000097008) \
    _(XR_TYPE_SCENE_COMPONENT_PARENT_FILTER_INFO_MSFT, 1000097009) \
    _(XR_TYPE_SCENE_OBJECT_TYPES_FILTER_INFO_MSFT, 1000097010) \
    _(XR_TYPE_SCENE_PLANES_MSFT, 1000097011) \
    _(XR_TYPE_SCENE_PLANE_ALIGNMENT_FILTER_INFO_MSFT, 1000097012) \
    _(XR_TYPE_SCENE_MESHES_MSFT, 1000097013) \
    _(XR_TYPE_SCENE_MESH_BUFFERS_GET_INFO_MSFT, 1000097014) \
    _(XR_TYPE_SCENE_MESH_BUFFERS_MSFT, 1000097015) \
    _(XR_TYPE_SCENE_MESH_VERTEX_BUFFER_MSFT, 1000097016) \
    _(XR_TYPE_SCENE_MESH_INDICES_UINT32_MSFT, 1000097017) \
    _(XR_TYPE_SCENE_MESH_INDICES_UINT16_MSFT, 1000097018) \
    _(XR_TYPE_SERIALIZED_SCENE_FRAGMENT_DATA_GET_INFO_MSFT, 1000098000) \
    _(XR_TYPE_SCENE_DESERIALIZE_INFO_MSFT, 1000098001) \
    _(XR_TYPE_EVENT_DATA_DISPLAY_REFRESH_RATE_CHANGED_FB, 1000101000) \
    _(XR_TYPE_VIVE_TRACKER_PATHS_HTCX, 1000103000) \
    _(XR_TYPE_EVENT_DATA_VIVE_TRACKER_CONNECTED_HTCX, 1000103001) \
    _(XR_TYPE_SYSTEM_FACIAL_TRACKING_PROPERTIES_HTC, 1000104000) \
    _(XR_TYPE_FACIAL_TRACKER_CREATE_INFO_HTC, 1000104001) \
    _(XR_TYPE_FACIAL_EXPRESSIONS_HTC, 1000104002) \
    _(XR_TYPE_SYSTEM_COLOR_SPACE_PROPERTIES_FB, 1000108000) \
    _(XR_TYPE_HAND_TRACKING_MESH_FB, 1000110001) \
    _(XR_TYPE_HAND_TRACKING_SCALE_FB, 1000110003) \
    _(XR_TYPE_HAND_TRACKING_AIM_STATE_FB, 1000111001) \
    _(XR_TYPE_HAND_TRACKING_CAPSULES_STATE_FB, 1000112000) \
    _(XR_TYPE_SYSTEM_SPATIAL_ENTITY_PROPERTIES_FB, 1000113004) \
    _(XR_TYPE_SPATIAL_ANCHOR_CREATE_INFO_FB, 1000113003) \
    _(XR_TYPE_SPACE_COMPONENT_STATUS_SET_INFO_FB, 1000113007) \
    _(XR_TYPE_SPACE_COMPONENT_STATUS_FB, 1000113001) \
    _(XR_TYPE_EVENT_DATA_SPATIAL_ANCHOR_CREATE_COMPLETE_FB, 1000113005) \
    _(XR_TYPE_EVENT_DATA_SPACE_SET_STATUS_COMPLETE_FB, 1000113006) \
    _(XR_TYPE_FOVEATION_PROFILE_CREATE_INFO_FB, 1000114000) \
    _(XR_TYPE_SWAPCHAIN_CREATE_INFO_FOVEATION_FB, 1000114001) \
    _(XR_TYPE_SWAPCHAIN_STATE_FOVEATION_FB, 1000114002) \
    _(XR_TYPE_FOVEATION_LEVEL_PROFILE_CREATE_INFO_FB, 1000115000) \
    _(XR_TYPE_KEYBOARD_SPACE_CREATE_INFO_FB, 1000116009) \
    _(XR_TYPE_KEYBOARD_TRACKING_QUERY_FB, 1000116004) \
    _(XR_TYPE_SYSTEM_KEYBOARD_TRACKING_PROPERTIES_FB, 1000116002) \
    _(XR_TYPE_TRIANGLE_MESH_CREATE_INFO_FB, 1000117001) \
    _(XR_TYPE_SYSTEM_PASSTHROUGH_PROPERTIES_FB, 1000118000) \
    _(XR_TYPE_PASSTHROUGH_CREATE_INFO_FB, 1000118001) \
    _(XR_TYPE_PASSTHROUGH_LAYER_CREATE_INFO_FB, 1000118002) \
    _(XR_TYPE_COMPOSITION_LAYER_PASSTHROUGH_FB, 1000118003) \
    _(XR_TYPE_GEOMETRY_INSTANCE_CREATE_INFO_FB, 1000118004) \
    _(XR_TYPE_GEOMETRY_INSTANCE_TRANSFORM_FB, 1000118005) \
    _(XR_TYPE_PASSTHROUGH_STYLE_FB, 1000118020) \
    _(XR_TYPE_PASSTHROUGH_COLOR_MAP_MONO_TO_RGBA_FB, 1000118021) \
    _(XR_TYPE_PASSTHROUGH_COLOR_MAP_MONO_TO_MONO_FB, 1000118022) \
    _(XR_TYPE_PASSTHROUGH_BRIGHTNESS_CONTRAST_SATURATION_FB, 1000118023) \
    _(XR_TYPE_EVENT_DATA_PASSTHROUGH_STATE_CHANGED_FB, 1000118030) \
    _(XR_TYPE_RENDER_MODEL_PATH_INFO_FB, 1000119000) \
    _(XR_TYPE_RENDER_MODEL_PROPERTIES_FB, 1000119001) \
    _(XR_TYPE_RENDER_MODEL_BUFFER_FB, 1000119002) \
    _(XR_TYPE_RENDER_MODEL_LOAD_INFO_FB, 1000119003) \
    _(XR_TYPE_SYSTEM_RENDER_MODEL_PROPERTIES_FB, 1000119004) \
    _(XR_TYPE_RENDER_MODEL_CAPABILITIES_REQUEST_FB, 1000119005) \
    _(XR_TYPE_BINDING_MODIFICATIONS_KHR, 1000120000) \
    _(XR_TYPE_VIEW_LOCATE_FOVEATED_RENDERING_VARJO, 1000121000) \
    _(XR_TYPE_FOVEATED_VIEW_CONFIGURATION_VIEW_VARJO, 1000121001) \
    _(XR_TYPE_SYSTEM_FOVEATED_RENDERING_PROPERTIES_VARJO, 1000121002) \
    _(XR_TYPE_COMPOSITION_LAYER_DEPTH_TEST_VARJO, 1000122000) \
    _(XR_TYPE_SYSTEM_MARKER_TRACKING_PROPERTIES_VARJO, 1000124000) \
    _(XR_TYPE_EVENT_DATA_MARKER_TRACKING_UPDATE_VARJO, 1000124001) \
    _(XR_TYPE_MARKER_SPACE_CREATE_INFO_VARJO, 1000124002) \
    _(XR_TYPE_SPATIAL_ANCHOR_PERSISTENCE_INFO_MSFT, 1000142000) \
    _(XR_TYPE_SPATIAL_ANCHOR_FROM_PERSISTED_ANCHOR_CREATE_INFO_MSFT, 1000142001) \
    _(XR_TYPE_SPACE_QUERY_INFO_FB, 1000156001) \
    _(XR_TYPE_SPACE_QUERY_RESULTS_FB, 1000156002) \
    _(XR_TYPE_SPACE_STORAGE_LOCATION_FILTER_INFO_FB, 1000156003) \
    _(XR_TYPE_SPACE_UUID_FILTER_INFO_FB, 1000156054) \
    _(XR_TYPE_SPACE_COMPONENT_FILTER_INFO_FB, 1000156052) \
    _(XR_TYPE_EVENT_DATA_SPACE_QUERY_RESULTS_AVAILABLE_FB, 1000156103) \
    _(XR_TYPE_EVENT_DATA_SPACE_QUERY_COMPLETE_FB, 1000156104) \
    _(XR_TYPE_SPACE_SAVE_INFO_FB, 1000158000) \
    _(XR_TYPE_SPACE_ERASE_INFO_FB, 1000158001) \
    _(XR_TYPE_EVENT_DATA_SPACE_SAVE_COMPLETE_FB, 1000158106) \
    _(XR_TYPE_EVENT_DATA_SPACE_ERASE_COMPLETE_FB, 1000158107) \
    _(XR_TYPE_SWAPCHAIN_IMAGE_FOVEATION_VULKAN_FB, 1000160000) \
    _(XR_TYPE_SWAPCHAIN_STATE_ANDROID_SURFACE_DIMENSIONS_FB, 1000161000) \
    _(XR_TYPE_SWAPCHAIN_STATE_SAMPLER_OPENGL_ES_FB, 1000162000) \
    _(XR_TYPE_SWAPCHAIN_STATE_SAMPLER_VULKAN_FB, 1000163000) \
    _(XR_TYPE_COMPOSITION_LAYER_SPACE_WARP_INFO_FB, 1000171000) \
    _(XR_TYPE_SYSTEM_SPACE_WARP_PROPERTIES_FB, 1000171001) \
    _(XR_TYPE_DIGITAL_LENS_CONTROL_ALMALENCE, 1000196000) \
    _(XR_TYPE_SPACE_CONTAINER_FB, 1000199000) \
    _(XR_TYPE_PASSTHROUGH_KEYBOARD_HANDS_INTENSITY_FB, 1000203002) \
    _(XR_TYPE_COMPOSITION_LAYER_SETTINGS_FB, 1000204000) \
    _(XR_TYPE_VULKAN_SWAPCHAIN_CREATE_INFO_META, 1000227000) \
    _(XR_TYPE_PERFORMANCE_METRICS_STATE_META, 1000232001) \
    _(XR_TYPE_PERFORMANCE_METRICS_COUNTER_META, 1000232002) \
    _(XR_STRUCTURE_TYPE_MAX_ENUM, 0x7FFFFFFF)

#define XR_LIST_ENUM_XrFormFactor(_) \
    _(XR_FORM_FACTOR_HEAD_MOUNTED_DISPLAY, 1) \
    _(XR_FORM_FACTOR_HANDHELD_DISPLAY, 2) \
    _(XR_FORM_FACTOR_MAX_ENUM, 0x7FFFFFFF)

#define XR_LIST_ENUM_XrViewConfigurationType(_) \
    _(XR_VIEW_CONFIGURATION_TYPE_PRIMARY_MONO, 1) \
    _(XR_VIEW_CONFIGURATION_TYPE_PRIMARY_STEREO, 2) \
    _(XR_VIEW_CONFIGURATION_TYPE_PRIMARY_QUAD_VARJO, 1000037000) \
    _(XR_VIEW_CONFIGURATION_TYPE_SECONDARY_MONO_FIRST_PERSON_OBSERVER_MSFT, 1000054000) \
    _(XR_VIEW_CONFIGURATION_TYPE_MAX_ENUM, 0x7FFFFFFF)

#define XR_LIST_ENUM_XrEnvironmentBlendMode(_) \
    _(XR_ENVIRONMENT_BLEND_MODE_OPAQUE, 1) \
    _(XR_ENVIRONMENT_BLEND_MODE_ADDITIVE, 2) \
    _(XR_ENVIRONMENT_BLEND_MODE_ALPHA_BLEND, 3) \
    _(XR_ENVIRONMENT_BLEND_MODE_MAX_ENUM, 0x7FFFFFFF)

#define XR_LIST_ENUM_XrReferenceSpaceType(_) \
    _(XR_REFERENCE_SPACE_TYPE_VIEW, 1) \
    _(XR_REFERENCE_SPACE_TYPE_LOCAL, 2) \
    _(XR_REFERENCE_SPACE_TYPE_STAGE, 3) \
    _(XR_REFERENCE_SPACE_TYPE_UNBOUNDED_MSFT, 1000038000) \
    _(XR_REFERENCE_SPACE_TYPE_COMBINED_EYE_VARJO, 1000121000) \
    _(XR_REFERENCE_SPACE_TYPE_MAX_ENUM, 0x7FFFFFFF)

#define XR_LIST_ENUM_XrActionType(_) \
    _(XR_ACTION_TYPE_BOOLEAN_INPUT, 1) \
    _(XR_ACTION_TYPE_FLOAT_INPUT, 2) \
    _(XR_ACTION_TYPE_VECTOR2F_INPUT, 3) \
    _(XR_ACTION_TYPE_POSE_INPUT, 4) \
    _(XR_ACTION_TYPE_VIBRATION_OUTPUT, 100) \
    _(XR_ACTION_TYPE_MAX_ENUM, 0x7FFFFFFF)

#define XR_LIST_ENUM_XrEyeVisibility(_) \
    _(XR_EYE_VISIBILITY_BOTH, 0) \
    _(XR_EYE_VISIBILITY_LEFT, 1) \
    _(XR_EYE_VISIBILITY_RIGHT, 2) \
    _(XR_EYE_VISIBILITY_MAX_ENUM, 0x7FFFFFFF)

#define XR_LIST_ENUM_XrSessionState(_) \
    _(XR_SESSION_STATE_UNKNOWN, 0) \
    _(XR_SESSION_STATE_IDLE, 1) \
    _(XR_SESSION_STATE_READY, 2) \
    _(XR_SESSION_STATE_SYNCHRONIZED, 3) \
    _(XR_SESSION_STATE_VISIBLE, 4) \
    _(XR_SESSION_STATE_FOCUSED, 5) \
    _(XR_SESSION_STATE_STOPPING, 6) \
    _(XR_SESSION_STATE_LOSS_PENDING, 7) \
    _(XR_SESSION_STATE_EXITING, 8) \
    _(XR_SESSION_STATE_MAX_ENUM, 0x7FFFFFFF)

#define XR_LIST_ENUM_XrObjectType(_) \
    _(XR_OBJECT_TYPE_UNKNOWN, 0) \
    _(XR_OBJECT_TYPE_INSTANCE, 1) \
    _(XR_OBJECT_TYPE_SESSION, 2) \
    _(XR_OBJECT_TYPE_SWAPCHAIN, 3) \
    _(XR_OBJECT_TYPE_SPACE, 4) \
    _(XR_OBJECT_TYPE_ACTION_SET, 5) \
    _(XR_OBJECT_TYPE_ACTION, 6) \
    _(XR_OBJECT_TYPE_DEBUG_UTILS_MESSENGER_EXT, 1000019000) \
    _(XR_OBJECT_TYPE_SPATIAL_ANCHOR_MSFT, 1000039000) \
    _(XR_OBJECT_TYPE_SPATIAL_GRAPH_NODE_BINDING_MSFT, 1000049000) \
    _(XR_OBJECT_TYPE_HAND_TRACKER_EXT, 1000051000) \
    _(XR_OBJECT_TYPE_SCENE_OBSERVER_MSFT, 1000097000) \
    _(XR_OBJECT_TYPE_SCENE_MSFT, 1000097001) \
    _(XR_OBJECT_TYPE_FACIAL_TRACKER_HTC, 1000104000) \
    _(XR_OBJECT_TYPE_FOVEATION_PROFILE_FB, 1000114000) \
    _(XR_OBJECT_TYPE_TRIANGLE_MESH_FB, 1000117000) \
    _(XR_OBJECT_TYPE_PASSTHROUGH_FB, 1000118000) \
    _(XR_OBJECT_TYPE_PASSTHROUGH_LAYER_FB, 1000118002) \
    _(XR_OBJECT_TYPE_GEOMETRY_INSTANCE_FB, 1000118004) \
    _(XR_OBJECT_TYPE_SPATIAL_ANCHOR_STORE_CONNECTION_MSFT, 1000142000) \
    _(XR_OBJECT_TYPE_MAX_ENUM, 0x7FFFFFFF)

#define XR_LIST_ENUM_XrAndroidThreadTypeKHR(_) \
    _(XR_ANDROID_THREAD_TYPE_APPLICATION_MAIN_KHR, 1) \
    _(XR_ANDROID_THREAD_TYPE_APPLICATION_WORKER_KHR, 2) \
    _(XR_ANDROID_THREAD_TYPE_RENDERER_MAIN_KHR, 3) \
    _(XR_ANDROID_THREAD_TYPE_RENDERER_WORKER_KHR, 4) \
    _(XR_ANDROID_THREAD_TYPE_MAX_ENUM_KHR, 0x7FFFFFFF)

#define XR_LIST_ENUM_XrVisibilityMaskTypeKHR(_) \
    _(XR_VISIBILITY_MASK_TYPE_HIDDEN_TRIANGLE_MESH_KHR, 1) \
    _(XR_VISIBILITY_MASK_TYPE_VISIBLE_TRIANGLE_MESH_KHR, 2) \
    _(XR_VISIBILITY_MASK_TYPE_LINE_LOOP_KHR, 3) \
    _(XR_VISIBILITY_MASK_TYPE_MAX_ENUM_KHR, 0x7FFFFFFF)

#define XR_LIST_ENUM_XrPerfSettingsDomainEXT(_) \
    _(XR_PERF_SETTINGS_DOMAIN_CPU_EXT, 1) \
    _(XR_PERF_SETTINGS_DOMAIN_GPU_EXT, 2) \
    _(XR_PERF_SETTINGS_DOMAIN_MAX_ENUM_EXT, 0x7FFFFFFF)

#define XR_LIST_ENUM_XrPerfSettingsSubDomainEXT(_) \
    _(XR_PERF_SETTINGS_SUB_DOMAIN_COMPOSITING_EXT, 1) \
    _(XR_PERF_SETTINGS_SUB_DOMAIN_RENDERING_EXT, 2) \
    _(XR_PERF_SETTINGS_SUB_DOMAIN_THERMAL_EXT, 3) \
    _(XR_PERF_SETTINGS_SUB_DOMAIN_MAX_ENUM_EXT, 0x7FFFFFFF)

#define XR_LIST_ENUM_XrPerfSettingsLevelEXT(_) \
    _(XR_PERF_SETTINGS_LEVEL_POWER_SAVINGS_EXT, 0) \
    _(XR_PERF_SETTINGS_LEVEL_SUSTAINED_LOW_EXT, 25) \
    _(XR_PERF_SETTINGS_LEVEL_SUSTAINED_HIGH_EXT, 50) \
    _(XR_PERF_SETTINGS_LEVEL_BOOST_EXT, 75) \
    _(XR_PERF_SETTINGS_LEVEL_MAX_ENUM_EXT, 0x7FFFFFFF)

#define XR_LIST_ENUM_XrPerfSettingsNotificationLevelEXT(_) \
    _(XR_PERF_SETTINGS_NOTIF_LEVEL_NORMAL_EXT, 0) \
    _(XR_PERF_SETTINGS_NOTIF_LEVEL_WARNING_EXT, 25) \
    _(XR_PERF_SETTINGS_NOTIF_LEVEL_IMPAIRED_EXT, 75) \
    _(XR_PERF_SETTINGS_NOTIFICATION_LEVEL_MAX_ENUM_EXT, 0x7FFFFFFF)

#define XR_LIST_ENUM_XrBlendFactorFB(_) \
    _(XR_BLEND_FACTOR_ZERO_FB, 0) \
    _(XR_BLEND_FACTOR_ONE_FB, 1) \
    _(XR_BLEND_FACTOR_SRC_ALPHA_FB, 2) \
    _(XR_BLEND_FACTOR_ONE_MINUS_SRC_ALPHA_FB, 3) \
    _(XR_BLEND_FACTOR_DST_ALPHA_FB, 4) \
    _(XR_BLEND_FACTOR_ONE_MINUS_DST_ALPHA_FB, 5) \
    _(XR_BLEND_FACTOR_MAX_ENUM_FB, 0x7FFFFFFF)

#define XR_LIST_ENUM_XrSpatialGraphNodeTypeMSFT(_) \
    _(XR_SPATIAL_GRAPH_NODE_TYPE_STATIC_MSFT, 1) \
    _(XR_SPATIAL_GRAPH_NODE_TYPE_DYNAMIC_MSFT, 2) \
    _(XR_SPATIAL_GRAPH_NODE_TYPE_MAX_ENUM_MSFT, 0x7FFFFFFF)

#define XR_LIST_ENUM_XrHandEXT(_) \
    _(XR_HAND_LEFT_EXT, 1) \
    _(XR_HAND_RIGHT_EXT, 2) \
    _(XR_HAND_MAX_ENUM_EXT, 0x7FFFFFFF)

#define XR_LIST_ENUM_XrHandJointEXT(_) \
    _(XR_HAND_JOINT_PALM_EXT, 0) \
    _(XR_HAND_JOINT_WRIST_EXT, 1) \
    _(XR_HAND_JOINT_THUMB_METACARPAL_EXT, 2) \
    _(XR_HAND_JOINT_THUMB_PROXIMAL_EXT, 3) \
    _(XR_HAND_JOINT_THUMB_DISTAL_EXT, 4) \
    _(XR_HAND_JOINT_THUMB_TIP_EXT, 5) \
    _(XR_HAND_JOINT_INDEX_METACARPAL_EXT, 6) \
    _(XR_HAND_JOINT_INDEX_PROXIMAL_EXT, 7) \
    _(XR_HAND_JOINT_INDEX_INTERMEDIATE_EXT, 8) \
    _(XR_HAND_JOINT_INDEX_DISTAL_EXT, 9) \
    _(XR_HAND_JOINT_INDEX_TIP_EXT, 10) \
    _(XR_HAND_JOINT_MIDDLE_METACARPAL_EXT, 11) \
    _(XR_HAND_JOINT_MIDDLE_PROXIMAL_EXT, 12) \
    _(XR_HAND_JOINT_MIDDLE_INTERMEDIATE_EXT, 13) \
    _(XR_HAND_JOINT_MIDDLE_DISTAL_EXT, 14) \
    _(XR_HAND_JOINT_MIDDLE_TIP_EXT, 15) \
    _(XR_HAND_JOINT_RING_METACARPAL_EXT, 16) \
    _(XR_HAND_JOINT_RING_PROXIMAL_EXT, 17) \
    _(XR_HAND_JOINT_RING_INTERMEDIATE_EXT, 18) \
    _(XR_HAND_JOINT_RING_DISTAL_EXT, 19) \
    _(XR_HAND_JOINT_RING_TIP_EXT, 20) \
    _(XR_HAND_JOINT_LITTLE_METACARPAL_EXT, 21) \
    _(XR_HAND_JOINT_LITTLE_PROXIMAL_EXT, 22) \
    _(XR_HAND_JOINT_LITTLE_INTERMEDIATE_EXT, 23) \
    _(XR_HAND_JOINT_LITTLE_DISTAL_EXT, 24) \
    _(XR_HAND_JOINT_LITTLE_TIP_EXT, 25) \
    _(XR_HAND_JOINT_MAX_ENUM_EXT, 0x7FFFFFFF)

#define XR_LIST_ENUM_XrHandJointSetEXT(_) \
    _(XR_HAND_JOINT_SET_DEFAULT_EXT, 0) \
    _(XR_HAND_JOINT_SET_HAND_WITH_FOREARM_ULTRALEAP, 1000149000) \
    _(XR_HAND_JOINT_SET_MAX_ENUM_EXT, 0x7FFFFFFF)

#define XR_LIST_ENUM_XrHandPoseTypeMSFT(_) \
    _(XR_HAND_POSE_TYPE_TRACKED_MSFT, 0) \
    _(XR_HAND_POSE_TYPE_REFERENCE_OPEN_PALM_MSFT, 1) \
    _(XR_HAND_POSE_TYPE_MAX_ENUM_MSFT, 0x7FFFFFFF)

#define XR_LIST_ENUM_XrReprojectionModeMSFT(_) \
    _(XR_REPROJECTION_MODE_DEPTH_MSFT, 1) \
    _(XR_REPROJECTION_MODE_PLANAR_FROM_DEPTH_MSFT, 2) \
    _(XR_REPROJECTION_MODE_PLANAR_MANUAL_MSFT, 3) \
    _(XR_REPROJECTION_MODE_ORIENTATION_ONLY_MSFT, 4) \
    _(XR_REPROJECTION_MODE_MAX_ENUM_MSFT, 0x7FFFFFFF)

#define XR_LIST_ENUM_XrHandJointsMotionRangeEXT(_) \
    _(XR_HAND_JOINTS_MOTION_RANGE_UNOBSTRUCTED_EXT, 1) \
    _(XR_HAND_JOINTS_MOTION_RANGE_CONFORMING_TO_CONTROLLER_EXT, 2) \
    _(XR_HAND_JOINTS_MOTION_RANGE_MAX_ENUM_EXT, 0x7FFFFFFF)

#define XR_LIST_ENUM_XrSceneComputeFeatureMSFT(_) \
    _(XR_SCENE_COMPUTE_FEATURE_PLANE_MSFT, 1) \
    _(XR_SCENE_COMPUTE_FEATURE_PLANE_MESH_MSFT, 2) \
    _(XR_SCENE_COMPUTE_FEATURE_VISUAL_MESH_MSFT, 3) \
    _(XR_SCENE_COMPUTE_FEATURE_COLLIDER_MESH_MSFT, 4) \
    _(XR_SCENE_COMPUTE_FEATURE_SERIALIZE_SCENE_MSFT, 1000098000) \
    _(XR_SCENE_COMPUTE_FEATURE_MAX_ENUM_MSFT, 0x7FFFFFFF)

#define XR_LIST_ENUM_XrSceneComputeConsistencyMSFT(_) \
    _(XR_SCENE_COMPUTE_CONSISTENCY_SNAPSHOT_COMPLETE_MSFT, 1) \
    _(XR_SCENE_COMPUTE_CONSISTENCY_SNAPSHOT_INCOMPLETE_FAST_MSFT, 2) \
    _(XR_SCENE_COMPUTE_CONSISTENCY_OCCLUSION_OPTIMIZED_MSFT, 3) \
    _(XR_SCENE_COMPUTE_CONSISTENCY_MAX_ENUM_MSFT, 0x7FFFFFFF)

#define XR_LIST_ENUM_XrMeshComputeLodMSFT(_) \
    _(XR_MESH_COMPUTE_LOD_COARSE_MSFT, 1) \
    _(XR_MESH_COMPUTE_LOD_MEDIUM_MSFT, 2) \
    _(XR_MESH_COMPUTE_LOD_FINE_MSFT, 3) \
    _(XR_MESH_COMPUTE_LOD_UNLIMITED_MSFT, 4) \
    _(XR_MESH_COMPUTE_LOD_MAX_ENUM_MSFT, 0x7FFFFFFF)

#define XR_LIST_ENUM_XrSceneComponentTypeMSFT(_) \
    _(XR_SCENE_COMPONENT_TYPE_INVALID_MSFT, -1) \
    _(XR_SCENE_COMPONENT_TYPE_OBJECT_MSFT, 1) \
    _(XR_SCENE_COMPONENT_TYPE_PLANE_MSFT, 2) \
    _(XR_SCENE_COMPONENT_TYPE_VISUAL_MESH_MSFT, 3) \
    _(XR_SCENE_COMPONENT_TYPE_COLLIDER_MESH_MSFT, 4) \
    _(XR_SCENE_COMPONENT_TYPE_SERIALIZED_SCENE_FRAGMENT_MSFT, 1000098000) \
    _(XR_SCENE_COMPONENT_TYPE_MAX_ENUM_MSFT, 0x7FFFFFFF)

#define XR_LIST_ENUM_XrSceneObjectTypeMSFT(_) \
    _(XR_SCENE_OBJECT_TYPE_UNCATEGORIZED_MSFT, -1) \
    _(XR_SCENE_OBJECT_TYPE_BACKGROUND_MSFT, 1) \
    _(XR_SCENE_OBJECT_TYPE_WALL_MSFT, 2) \
    _(XR_SCENE_OBJECT_TYPE_FLOOR_MSFT, 3) \
    _(XR_SCENE_OBJECT_TYPE_CEILING_MSFT, 4) \
    _(XR_SCENE_OBJECT_TYPE_PLATFORM_MSFT, 5) \
    _(XR_SCENE_OBJECT_TYPE_INFERRED_MSFT, 6) \
    _(XR_SCENE_OBJECT_TYPE_MAX_ENUM_MSFT, 0x7FFFFFFF)

#define XR_LIST_ENUM_XrScenePlaneAlignmentTypeMSFT(_) \
    _(XR_SCENE_PLANE_ALIGNMENT_TYPE_NON_ORTHOGONAL_MSFT, 0) \
    _(XR_SCENE_PLANE_ALIGNMENT_TYPE_HORIZONTAL_MSFT, 1) \
    _(XR_SCENE_PLANE_ALIGNMENT_TYPE_VERTICAL_MSFT, 2) \
    _(XR_SCENE_PLANE_ALIGNMENT_TYPE_MAX_ENUM_MSFT, 0x7FFFFFFF)

#define XR_LIST_ENUM_XrSceneComputeStateMSFT(_) \
    _(XR_SCENE_COMPUTE_STATE_NONE_MSFT, 0) \
    _(XR_SCENE_COMPUTE_STATE_UPDATING_MSFT, 1) \
    _(XR_SCENE_COMPUTE_STATE_COMPLETED_MSFT, 2) \
    _(XR_SCENE_COMPUTE_STATE_COMPLETED_WITH_ERROR_MSFT, 3) \
    _(XR_SCENE_COMPUTE_STATE_MAX_ENUM_MSFT, 0x7FFFFFFF)

#define XR_LIST_ENUM_XrEyeExpressionHTC(_) \
    _(XR_EYE_EXPRESSION_LEFT_BLINK_HTC, 0) \
    _(XR_EYE_EXPRESSION_LEFT_WIDE_HTC, 1) \
    _(XR_EYE_EXPRESSION_RIGHT_BLINK_HTC, 2) \
    _(XR_EYE_EXPRESSION_RIGHT_WIDE_HTC, 3) \
    _(XR_EYE_EXPRESSION_LEFT_SQUEEZE_HTC, 4) \
    _(XR_EYE_EXPRESSION_RIGHT_SQUEEZE_HTC, 5) \
    _(XR_EYE_EXPRESSION_LEFT_DOWN_HTC, 6) \
    _(XR_EYE_EXPRESSION_RIGHT_DOWN_HTC, 7) \
    _(XR_EYE_EXPRESSION_LEFT_OUT_HTC, 8) \
    _(XR_EYE_EXPRESSION_RIGHT_IN_HTC, 9) \
    _(XR_EYE_EXPRESSION_LEFT_IN_HTC, 10) \
    _(XR_EYE_EXPRESSION_RIGHT_OUT_HTC, 11) \
    _(XR_EYE_EXPRESSION_LEFT_UP_HTC, 12) \
    _(XR_EYE_EXPRESSION_RIGHT_UP_HTC, 13) \
    _(XR_EYE_EXPRESSION_MAX_ENUM_HTC, 0x7FFFFFFF)

#define XR_LIST_ENUM_XrLipExpressionHTC(_) \
    _(XR_LIP_EXPRESSION_JAW_RIGHT_HTC, 0) \
    _(XR_LIP_EXPRESSION_JAW_LEFT_HTC, 1) \
    _(XR_LIP_EXPRESSION_JAW_FORWARD_HTC, 2) \
    _(XR_LIP_EXPRESSION_JAW_OPEN_HTC, 3) \
    _(XR_LIP_EXPRESSION_MOUTH_APE_SHAPE_HTC, 4) \
    _(XR_LIP_EXPRESSION_MOUTH_UPPER_RIGHT_HTC, 5) \
    _(XR_LIP_EXPRESSION_MOUTH_UPPER_LEFT_HTC, 6) \
    _(XR_LIP_EXPRESSION_MOUTH_LOWER_RIGHT_HTC, 7) \
    _(XR_LIP_EXPRESSION_MOUTH_LOWER_LEFT_HTC, 8) \
    _(XR_LIP_EXPRESSION_MOUTH_UPPER_OVERTURN_HTC, 9) \
    _(XR_LIP_EXPRESSION_MOUTH_LOWER_OVERTURN_HTC, 10) \
    _(XR_LIP_EXPRESSION_MOUTH_POUT_HTC, 11) \
    _(XR_LIP_EXPRESSION_MOUTH_SMILE_RIGHT_HTC, 12) \
    _(XR_LIP_EXPRESSION_MOUTH_SMILE_LEFT_HTC, 13) \
    _(XR_LIP_EXPRESSION_MOUTH_SAD_RIGHT_HTC, 14) \
    _(XR_LIP_EXPRESSION_MOUTH_SAD_LEFT_HTC, 15) \
    _(XR_LIP_EXPRESSION_CHEEK_PUFF_RIGHT_HTC, 16) \
    _(XR_LIP_EXPRESSION_CHEEK_PUFF_LEFT_HTC, 17) \
    _(XR_LIP_EXPRESSION_CHEEK_SUCK_HTC, 18) \
    _(XR_LIP_EXPRESSION_MOUTH_UPPER_UPRIGHT_HTC, 19) \
    _(XR_LIP_EXPRESSION_MOUTH_UPPER_UPLEFT_HTC, 20) \
    _(XR_LIP_EXPRESSION_MOUTH_LOWER_DOWNRIGHT_HTC, 21) \
    _(XR_LIP_EXPRESSION_MOUTH_LOWER_DOWNLEFT_HTC, 22) \
    _(XR_LIP_EXPRESSION_MOUTH_UPPER_INSIDE_HTC, 23) \
    _(XR_LIP_EXPRESSION_MOUTH_LOWER_INSIDE_HTC, 24) \
    _(XR_LIP_EXPRESSION_MOUTH_LOWER_OVERLAY_HTC, 25) \
    _(XR_LIP_EXPRESSION_TONGUE_LONGSTEP1_HTC, 26) \
    _(XR_LIP_EXPRESSION_TONGUE_LEFT_HTC, 27) \
    _(XR_LIP_EXPRESSION_TONGUE_RIGHT_HTC, 28) \
    _(XR_LIP_EXPRESSION_TONGUE_UP_HTC, 29) \
    _(XR_LIP_EXPRESSION_TONGUE_DOWN_HTC, 30) \
    _(XR_LIP_EXPRESSION_TONGUE_ROLL_HTC, 31) \
    _(XR_LIP_EXPRESSION_TONGUE_LONGSTEP2_HTC, 32) \
    _(XR_LIP_EXPRESSION_TONGUE_UPRIGHT_MORPH_HTC, 33) \
    _(XR_LIP_EXPRESSION_TONGUE_UPLEFT_MORPH_HTC, 34) \
    _(XR_LIP_EXPRESSION_TONGUE_DOWNRIGHT_MORPH_HTC, 35) \
    _(XR_LIP_EXPRESSION_TONGUE_DOWNLEFT_MORPH_HTC, 36) \
    _(XR_LIP_EXPRESSION_MAX_ENUM_HTC, 0x7FFFFFFF)

#define XR_LIST_ENUM_XrFacialTrackingTypeHTC(_) \
    _(XR_FACIAL_TRACKING_TYPE_EYE_DEFAULT_HTC, 1) \
    _(XR_FACIAL_TRACKING_TYPE_LIP_DEFAULT_HTC, 2) \
    _(XR_FACIAL_TRACKING_TYPE_MAX_ENUM_HTC, 0x7FFFFFFF)

#define XR_LIST_ENUM_XrColorSpaceFB(_) \
    _(XR_COLOR_SPACE_UNMANAGED_FB, 0) \
    _(XR_COLOR_SPACE_REC2020_FB, 1) \
    _(XR_COLOR_SPACE_REC709_FB, 2) \
    _(XR_COLOR_SPACE_RIFT_CV1_FB, 3) \
    _(XR_COLOR_SPACE_RIFT_S_FB, 4) \
    _(XR_COLOR_SPACE_QUEST_FB, 5) \
    _(XR_COLOR_SPACE_P3_FB, 6) \
    _(XR_COLOR_SPACE_ADOBE_RGB_FB, 7) \
    _(XR_COLOR_SPACE_MAX_ENUM_FB, 0x7FFFFFFF)

#define XR_LIST_ENUM_XrSpaceComponentTypeFB(_) \
    _(XR_SPACE_COMPONENT_TYPE_LOCATABLE_FB, 0) \
    _(XR_SPACE_COMPONENT_TYPE_STORABLE_FB, 1) \
    _(XR_SPACE_COMPONENT_TYPE_SPACE_CONTAINER_FB, 7) \
    _(XR_SPACE_COMPONENT_TYPE_MAX_ENUM_FB, 0x7FFFFFFF)

#define XR_LIST_ENUM_XrFoveationLevelFB(_) \
    _(XR_FOVEATION_LEVEL_NONE_FB, 0) \
    _(XR_FOVEATION_LEVEL_LOW_FB, 1) \
    _(XR_FOVEATION_LEVEL_MEDIUM_FB, 2) \
    _(XR_FOVEATION_LEVEL_HIGH_FB, 3) \
    _(XR_FOVEATION_LEVEL_MAX_ENUM_FB, 0x7FFFFFFF)

#define XR_LIST_ENUM_XrFoveationDynamicFB(_) \
    _(XR_FOVEATION_DYNAMIC_DISABLED_FB, 0) \
    _(XR_FOVEATION_DYNAMIC_LEVEL_ENABLED_FB, 1) \
    _(XR_FOVEATION_DYNAMIC_MAX_ENUM_FB, 0x7FFFFFFF)

#define XR_LIST_ENUM_XrWindingOrderFB(_) \
    _(XR_WINDING_ORDER_UNKNOWN_FB, 0) \
    _(XR_WINDING_ORDER_CW_FB, 1) \
    _(XR_WINDING_ORDER_CCW_FB, 2) \
    _(XR_WINDING_ORDER_MAX_ENUM_FB, 0x7FFFFFFF)

#define XR_LIST_ENUM_XrPassthroughLayerPurposeFB(_) \
    _(XR_PASSTHROUGH_LAYER_PURPOSE_RECONSTRUCTION_FB, 0) \
    _(XR_PASSTHROUGH_LAYER_PURPOSE_PROJECTED_FB, 1) \
    _(XR_PASSTHROUGH_LAYER_PURPOSE_TRACKED_KEYBOARD_HANDS_FB, 1000203001) \
    _(XR_PASSTHROUGH_LAYER_PURPOSE_TRACKED_KEYBOARD_MASKED_HANDS_FB, 1000203002) \
    _(XR_PASSTHROUGH_LAYER_PURPOSE_MAX_ENUM_FB, 0x7FFFFFFF)

#define XR_LIST_ENUM_XrHandForearmJointULTRALEAP(_) \
    _(XR_HAND_FOREARM_JOINT_PALM_ULTRALEAP, 0) \
    _(XR_HAND_FOREARM_JOINT_WRIST_ULTRALEAP, 1) \
    _(XR_HAND_FOREARM_JOINT_THUMB_METACARPAL_ULTRALEAP, 2) \
    _(XR_HAND_FOREARM_JOINT_THUMB_PROXIMAL_ULTRALEAP, 3) \
    _(XR_HAND_FOREARM_JOINT_THUMB_DISTAL_ULTRALEAP, 4) \
    _(XR_HAND_FOREARM_JOINT_THUMB_TIP_ULTRALEAP, 5) \
    _(XR_HAND_FOREARM_JOINT_INDEX_METACARPAL_ULTRALEAP, 6) \
    _(XR_HAND_FOREARM_JOINT_INDEX_PROXIMAL_ULTRALEAP, 7) \
    _(XR_HAND_FOREARM_JOINT_INDEX_INTERMEDIATE_ULTRALEAP, 8) \
    _(XR_HAND_FOREARM_JOINT_INDEX_DISTAL_ULTRALEAP, 9) \
    _(XR_HAND_FOREARM_JOINT_INDEX_TIP_ULTRALEAP, 10) \
    _(XR_HAND_FOREARM_JOINT_MIDDLE_METACARPAL_ULTRALEAP, 11) \
    _(XR_HAND_FOREARM_JOINT_MIDDLE_PROXIMAL_ULTRALEAP, 12) \
    _(XR_HAND_FOREARM_JOINT_MIDDLE_INTERMEDIATE_ULTRALEAP, 13) \
    _(XR_HAND_FOREARM_JOINT_MIDDLE_DISTAL_ULTRALEAP, 14) \
    _(XR_HAND_FOREARM_JOINT_MIDDLE_TIP_ULTRALEAP, 15) \
    _(XR_HAND_FOREARM_JOINT_RING_METACARPAL_ULTRALEAP, 16) \
    _(XR_HAND_FOREARM_JOINT_RING_PROXIMAL_ULTRALEAP, 17) \
    _(XR_HAND_FOREARM_JOINT_RING_INTERMEDIATE_ULTRALEAP, 18) \
    _(XR_HAND_FOREARM_JOINT_RING_DISTAL_ULTRALEAP, 19) \
    _(XR_HAND_FOREARM_JOINT_RING_TIP_ULTRALEAP, 20) \
    _(XR_HAND_FOREARM_JOINT_LITTLE_METACARPAL_ULTRALEAP, 21) \
    _(XR_HAND_FOREARM_JOINT_LITTLE_PROXIMAL_ULTRALEAP, 22) \
    _(XR_HAND_FOREARM_JOINT_LITTLE_INTERMEDIATE_ULTRALEAP, 23) \
    _(XR_HAND_FOREARM_JOINT_LITTLE_DISTAL_ULTRALEAP, 24) \
    _(XR_HAND_FOREARM_JOINT_LITTLE_TIP_ULTRALEAP, 25) \
    _(XR_HAND_FOREARM_JOINT_ELBOW_ULTRALEAP, 26) \
    _(XR_HAND_FOREARM_JOINT_MAX_ENUM_ULTRALEAP, 0x7FFFFFFF)

#define XR_LIST_ENUM_XrSpaceQueryActionFB(_) \
    _(XR_SPACE_QUERY_ACTION_LOAD_FB, 0) \
    _(XR_SPACE_QUERY_ACTION_MAX_ENUM_FB, 0x7FFFFFFF)

#define XR_LIST_ENUM_XrSpaceStorageLocationFB(_) \
    _(XR_SPACE_STORAGE_LOCATION_INVALID_FB, 0) \
    _(XR_SPACE_STORAGE_LOCATION_LOCAL_FB, 1) \
    _(XR_SPACE_STORAGE_LOCATION_MAX_ENUM_FB, 0x7FFFFFFF)

#define XR_LIST_ENUM_XrSpacePersistenceModeFB(_) \
    _(XR_SPACE_PERSISTENCE_MODE_INVALID_FB, 0) \
    _(XR_SPACE_PERSISTENCE_MODE_INDEFINITE_FB, 1) \
    _(XR_SPACE_PERSISTENCE_MODE_MAX_ENUM_FB, 0x7FFFFFFF)

#define XR_LIST_ENUM_XrPerformanceMetricsCounterUnitMETA(_) \
    _(XR_PERFORMANCE_METRICS_COUNTER_UNIT_GENERIC_META, 0) \
    _(XR_PERFORMANCE_METRICS_COUNTER_UNIT_PERCENTAGE_META, 1) \
    _(XR_PERFORMANCE_METRICS_COUNTER_UNIT_MILLISECONDS_META, 2) \
    _(XR_PERFORMANCE_METRICS_COUNTER_UNIT_BYTES_META, 3) \
    _(XR_PERFORMANCE_METRICS_COUNTER_UNIT_HERTZ_META, 4) \
    _(XR_PERFORMANCE_METRICS_COUNTER_UNIT_MAX_ENUM_META, 0x7FFFFFFF)

#define XR_LIST_BITS_XrInstanceCreateFlags(_)

#define XR_LIST_BITS_XrSessionCreateFlags(_)

#define XR_LIST_BITS_XrSpaceVelocityFlags(_) \
    _(XR_SPACE_VELOCITY_LINEAR_VALID_BIT, 0x00000001) \
    _(XR_SPACE_VELOCITY_ANGULAR_VALID_BIT, 0x00000002) \

#define XR_LIST_BITS_XrSpaceLocationFlags(_) \
    _(XR_SPACE_LOCATION_ORIENTATION_VALID_BIT, 0x00000001) \
    _(XR_SPACE_LOCATION_POSITION_VALID_BIT, 0x00000002) \
    _(XR_SPACE_LOCATION_ORIENTATION_TRACKED_BIT, 0x00000004) \
    _(XR_SPACE_LOCATION_POSITION_TRACKED_BIT, 0x00000008) \

#define XR_LIST_BITS_XrSwapchainCreateFlags(_) \
    _(XR_SWAPCHAIN_CREATE_PROTECTED_CONTENT_BIT, 0x00000001) \
    _(XR_SWAPCHAIN_CREATE_STATIC_IMAGE_BIT, 0x00000002) \

#define XR_LIST_BITS_XrSwapchainUsageFlags(_) \
    _(XR_SWAPCHAIN_USAGE_COLOR_ATTACHMENT_BIT, 0x00000001) \
    _(XR_SWAPCHAIN_USAGE_DEPTH_STENCIL_ATTACHMENT_BIT, 0x00000002) \
    _(XR_SWAPCHAIN_USAGE_UNORDERED_ACCESS_BIT, 0x00000004) \
    _(XR_SWAPCHAIN_USAGE_TRANSFER_SRC_BIT, 0x00000008) \
    _(XR_SWAPCHAIN_USAGE_TRANSFER_DST_BIT, 0x00000010) \
    _(XR_SWAPCHAIN_USAGE_SAMPLED_BIT, 0x00000020) \
    _(XR_SWAPCHAIN_USAGE_MUTABLE_FORMAT_BIT, 0x00000040) \
    _(XR_SWAPCHAIN_USAGE_INPUT_ATTACHMENT_BIT_MND, 0x00000080) \
    _(XR_SWAPCHAIN_USAGE_INPUT_ATTACHMENT_BIT_KHR, XR_SWAPCHAIN_USAGE_INPUT_ATTACHMENT_BIT_MND) \

#define XR_LIST_BITS_XrCompositionLayerFlags(_) \
    _(XR_COMPOSITION_LAYER_CORRECT_CHROMATIC_ABERRATION_BIT, 0x00000001) \
    _(XR_COMPOSITION_LAYER_BLEND_TEXTURE_SOURCE_ALPHA_BIT, 0x00000002) \
    _(XR_COMPOSITION_LAYER_UNPREMULTIPLIED_ALPHA_BIT, 0x00000004) \

#define XR_LIST_BITS_XrViewStateFlags(_) \
    _(XR_VIEW_STATE_ORIENTATION_VALID_BIT, 0x00000001) \
    _(XR_VIEW_STATE_POSITION_VALID_BIT, 0x00000002) \
    _(XR_VIEW_STATE_ORIENTATION_TRACKED_BIT, 0x00000004) \
    _(XR_VIEW_STATE_POSITION_TRACKED_BIT, 0x00000008) \

#define XR_LIST_BITS_XrInputSourceLocalizedNameFlags(_) \
    _(XR_INPUT_SOURCE_LOCALIZED_NAME_USER_PATH_BIT, 0x00000001) \
    _(XR_INPUT_SOURCE_LOCALIZED_NAME_INTERACTION_PROFILE_BIT, 0x00000002) \
    _(XR_INPUT_SOURCE_LOCALIZED_NAME_COMPONENT_BIT, 0x00000004) \

#define XR_LIST_BITS_XrVulkanInstanceCreateFlagsKHR(_)

#define XR_LIST_BITS_XrVulkanDeviceCreateFlagsKHR(_)

#define XR_LIST_BITS_XrDebugUtilsMessageSeverityFlagsEXT(_) \
    _(XR_DEBUG_UTILS_MESSAGE_SEVERITY_VERBOSE_BIT_EXT, 0x00000001) \
    _(XR_DEBUG_UTILS_MESSAGE_SEVERITY_INFO_BIT_EXT, 0x00000010) \
    _(XR_DEBUG_UTILS_MESSAGE_SEVERITY_WARNING_BIT_EXT, 0x00000100) \
    _(XR_DEBUG_UTILS_MESSAGE_SEVERITY_ERROR_BIT_EXT, 0x00001000) \

#define XR_LIST_BITS_XrDebugUtilsMessageTypeFlagsEXT(_) \
    _(XR_DEBUG_UTILS_MESSAGE_TYPE_GENERAL_BIT_EXT, 0x00000001) \
    _(XR_DEBUG_UTILS_MESSAGE_TYPE_VALIDATION_BIT_EXT, 0x00000002) \
    _(XR_DEBUG_UTILS_MESSAGE_TYPE_PERFORMANCE_BIT_EXT, 0x00000004) \
    _(XR_DEBUG_UTILS_MESSAGE_TYPE_CONFORMANCE_BIT_EXT, 0x00000008) \

#define XR_LIST_BITS_XrOverlaySessionCreateFlagsEXTX(_)

#define XR_LIST_BITS_XrOverlayMainSessionFlagsEXTX(_) \
    _(XR_OVERLAY_MAIN_SESSION_ENABLED_COMPOSITION_LAYER_INFO_DEPTH_BIT_EXTX, 0x00000001) \

#define XR_LIST_BITS_XrCompositionLayerImageLayoutFlagsFB(_) \
    _(XR_COMPOSITION_LAYER_IMAGE_LAYOUT_VERTICAL_FLIP_BIT_FB, 0x00000001) \

#define XR_LIST_BITS_XrAndroidSurfaceSwapchainFlagsFB(_) \
    _(XR_ANDROID_SURFACE_SWAPCHAIN_SYNCHRONOUS_BIT_FB, 0x00000001) \
    _(XR_ANDROID_SURFACE_SWAPCHAIN_USE_TIMESTAMPS_BIT_FB, 0x00000002) \

#define XR_LIST_BITS_XrCompositionLayerSecureContentFlagsFB(_) \
    _(XR_COMPOSITION_LAYER_SECURE_CONTENT_EXCLUDE_LAYER_BIT_FB, 0x00000001) \
    _(XR_COMPOSITION_LAYER_SECURE_CONTENT_REPLACE_LAYER_BIT_FB, 0x00000002) \

#define XR_LIST_BITS_XrHandTrackingAimFlagsFB(_) \
    _(XR_HAND_TRACKING_AIM_COMPUTED_BIT_FB, 0x00000001) \
    _(XR_HAND_TRACKING_AIM_VALID_BIT_FB, 0x00000002) \
    _(XR_HAND_TRACKING_AIM_INDEX_PINCHING_BIT_FB, 0x00000004) \
    _(XR_HAND_TRACKING_AIM_MIDDLE_PINCHING_BIT_FB, 0x00000008) \
    _(XR_HAND_TRACKING_AIM_RING_PINCHING_BIT_FB, 0x00000010) \
    _(XR_HAND_TRACKING_AIM_LITTLE_PINCHING_BIT_FB, 0x00000020) \
    _(XR_HAND_TRACKING_AIM_SYSTEM_GESTURE_BIT_FB, 0x00000040) \
    _(XR_HAND_TRACKING_AIM_DOMINANT_HAND_BIT_FB, 0x00000080) \
    _(XR_HAND_TRACKING_AIM_MENU_PRESSED_BIT_FB, 0x00000100) \

#define XR_LIST_BITS_XrSwapchainCreateFoveationFlagsFB(_) \
    _(XR_SWAPCHAIN_CREATE_FOVEATION_SCALED_BIN_BIT_FB, 0x00000001) \
    _(XR_SWAPCHAIN_CREATE_FOVEATION_FRAGMENT_DENSITY_MAP_BIT_FB, 0x00000002) \

#define XR_LIST_BITS_XrSwapchainStateFoveationFlagsFB(_)

#define XR_LIST_BITS_XrKeyboardTrackingFlagsFB(_) \
    _(XR_KEYBOARD_TRACKING_EXISTS_BIT_FB, 0x00000001) \
    _(XR_KEYBOARD_TRACKING_LOCAL_BIT_FB, 0x00000002) \
    _(XR_KEYBOARD_TRACKING_REMOTE_BIT_FB, 0x00000004) \
    _(XR_KEYBOARD_TRACKING_CONNECTED_BIT_FB, 0x00000008) \

#define XR_LIST_BITS_XrKeyboardTrackingQueryFlagsFB(_) \
    _(XR_KEYBOARD_TRACKING_QUERY_LOCAL_BIT_FB, 0x00000002) \
    _(XR_KEYBOARD_TRACKING_QUERY_REMOTE_BIT_FB, 0x00000004) \

#define XR_LIST_BITS_XrTriangleMeshFlagsFB(_) \
    _(XR_TRIANGLE_MESH_MUTABLE_BIT_FB, 0x00000001) \

#define XR_LIST_BITS_XrPassthroughFlagsFB(_) \
    _(XR_PASSTHROUGH_IS_RUNNING_AT_CREATION_BIT_FB, 0x00000001) \

#define XR_LIST_BITS_XrPassthroughStateChangedFlagsFB(_) \
    _(XR_PASSTHROUGH_STATE_CHANGED_REINIT_REQUIRED_BIT_FB, 0x00000001) \
    _(XR_PASSTHROUGH_STATE_CHANGED_NON_RECOVERABLE_ERROR_BIT_FB, 0x00000002) \
    _(XR_PASSTHROUGH_STATE_CHANGED_RECOVERABLE_ERROR_BIT_FB, 0x00000004) \
    _(XR_PASSTHROUGH_STATE_CHANGED_RESTORED_ERROR_BIT_FB, 0x00000008) \

#define XR_LIST_BITS_XrRenderModelFlagsFB(_) \
    _(XR_RENDER_MODEL_SUPPORTS_GLTF_2_0_SUBSET_1_BIT_FB, 0x00000001) \
    _(XR_RENDER_MODEL_SUPPORTS_GLTF_2_0_SUBSET_2_BIT_FB, 0x00000002) \

#define XR_LIST_BITS_XrCompositionLayerSpaceWarpInfoFlagsFB(_) \
    _(XR_COMPOSITION_LAYER_SPACE_WARP_INFO_FRAME_SKIP_BIT_FB, 0x00000001) \

#define XR_LIST_BITS_XrDigitalLensControlFlagsALMALENCE(_) \
    _(XR_DIGITAL_LENS_CONTROL_PROCESSING_DISABLE_BIT_ALMALENCE, 0x00000001) \

#define XR_LIST_BITS_XrCompositionLayerSettingsFlagsFB(_) \
    _(XR_COMPOSITION_LAYER_SETTINGS_NORMAL_SUPER_SAMPLING_BIT_FB, 0x00000001) \
    _(XR_COMPOSITION_LAYER_SETTINGS_QUALITY_SUPER_SAMPLING_BIT_FB, 0x00000002) \
    _(XR_COMPOSITION_LAYER_SETTINGS_NORMAL_SHARPENING_BIT_FB, 0x00000004) \
    _(XR_COMPOSITION_LAYER_SETTINGS_QUALITY_SHARPENING_BIT_FB, 0x00000008) \

#define XR_LIST_BITS_XrPerformanceMetricsCounterFlagsMETA(_) \
    _(XR_PERFORMANCE_METRICS_COUNTER_ANY_VALUE_VALID_BIT_META, 0x00000001) \
    _(XR_PERFORMANCE_METRICS_COUNTER_UINT_VALUE_VALID_BIT_META, 0x00000002) \
    _(XR_PERFORMANCE_METRICS_COUNTER_FLOAT_VALUE_VALID_BIT_META, 0x00000004) \

#define XR_LIST_STRUCT_XrApiLayerProperties(_) \
    _(type) \
    _(next) \
    _(layerName) \
    _(specVersion) \
    _(layerVersion) \
    _(description) \

#define XR_LIST_STRUCT_XrExtensionProperties(_) \
    _(type) \
    _(next) \
    _(extensionName) \
    _(extensionVersion) \

#define XR_LIST_STRUCT_XrApplicationInfo(_) \
    _(applicationName) \
    _(applicationVersion) \
    _(engineName) \
    _(engineVersion) \
    _(apiVersion) \

#define XR_LIST_STRUCT_XrInstanceCreateInfo(_) \
    _(type) \
    _(next) \
    _(createFlags) \
    _(applicationInfo) \
    _(enabledApiLayerCount) \
    _(enabledApiLayerNames) \
    _(enabledExtensionCount) \
    _(enabledExtensionNames) \

#define XR_LIST_STRUCT_XrInstanceProperties(_) \
    _(type) \
    _(next) \
    _(runtimeVersion) \
    _(runtimeName) \

#define XR_LIST_STRUCT_XrEventDataBuffer(_) \
    _(type) \
    _(next) \
    _(varying) \

#define XR_LIST_STRUCT_XrSystemGetInfo(_) \
    _(type) \
    _(next) \
    _(formFactor) \

#define XR_LIST_STRUCT_XrSystemGraphicsProperties(_) \
    _(maxSwapchainImageHeight) \
    _(maxSwapchainImageWidth) \
    _(maxLayerCount) \

#define XR_LIST_STRUCT_XrSystemTrackingProperties(_) \
    _(orientationTracking) \
    _(positionTracking) \

#define XR_LIST_STRUCT_XrSystemProperties(_) \
    _(type) \
    _(next) \
    _(systemId) \
    _(vendorId) \
    _(systemName) \
    _(graphicsProperties) \
    _(trackingProperties) \

#define XR_LIST_STRUCT_XrSessionCreateInfo(_) \
    _(type) \
    _(next) \
    _(createFlags) \
    _(systemId) \

#define XR_LIST_STRUCT_XrVector3f(_) \
    _(x) \
    _(y) \
    _(z) \

#define XR_LIST_STRUCT_XrSpaceVelocity(_) \
    _(type) \
    _(next) \
    _(velocityFlags) \
    _(linearVelocity) \
    _(angularVelocity) \

#define XR_LIST_STRUCT_XrQuaternionf(_) \
    _(x) \
    _(y) \
    _(z) \
    _(w) \

#define XR_LIST_STRUCT_XrPosef(_) \
    _(orientation) \
    _(position) \

#define XR_LIST_STRUCT_XrReferenceSpaceCreateInfo(_) \
    _(type) \
    _(next) \
    _(referenceSpaceType) \
    _(poseInReferenceSpace) \

#define XR_LIST_STRUCT_XrExtent2Df(_) \
    _(width) \
    _(height) \

#define XR_LIST_STRUCT_XrActionSpaceCreateInfo(_) \
    _(type) \
    _(next) \
    _(action) \
    _(subactionPath) \
    _(poseInActionSpace) \

#define XR_LIST_STRUCT_XrSpaceLocation(_) \
    _(type) \
    _(next) \
    _(locationFlags) \
    _(pose) \

#define XR_LIST_STRUCT_XrViewConfigurationProperties(_) \
    _(type) \
    _(next) \
    _(viewConfigurationType) \
    _(fovMutable) \

#define XR_LIST_STRUCT_XrViewConfigurationView(_) \
    _(type) \
    _(next) \
    _(recommendedImageRectWidth) \
    _(maxImageRectWidth) \
    _(recommendedImageRectHeight) \
    _(maxImageRectHeight) \
    _(recommendedSwapchainSampleCount) \
    _(maxSwapchainSampleCount) \

#define XR_LIST_STRUCT_XrSwapchainCreateInfo(_) \
    _(type) \
    _(next) \
    _(createFlags) \
    _(usageFlags) \
    _(format) \
    _(sampleCount) \
    _(width) \
    _(height) \
    _(faceCount) \
    _(arraySize) \
    _(mipCount) \

#define XR_LIST_STRUCT_XrSwapchainImageBaseHeader(_) \
    _(type) \
    _(next) \

#define XR_LIST_STRUCT_XrSwapchainImageAcquireInfo(_) \
    _(type) \
    _(next) \

#define XR_LIST_STRUCT_XrSwapchainImageWaitInfo(_) \
    _(type) \
    _(next) \
    _(timeout) \

#define XR_LIST_STRUCT_XrSwapchainImageReleaseInfo(_) \
    _(type) \
    _(next) \

#define XR_LIST_STRUCT_XrSessionBeginInfo(_) \
    _(type) \
    _(next) \
    _(primaryViewConfigurationType) \

#define XR_LIST_STRUCT_XrFrameWaitInfo(_) \
    _(type) \
    _(next) \

#define XR_LIST_STRUCT_XrFrameState(_) \
    _(type) \
    _(next) \
    _(predictedDisplayTime) \
    _(predictedDisplayPeriod) \
    _(shouldRender) \

#define XR_LIST_STRUCT_XrFrameBeginInfo(_) \
    _(type) \
    _(next) \

#define XR_LIST_STRUCT_XrCompositionLayerBaseHeader(_) \
    _(type) \
    _(next) \
    _(layerFlags) \
    _(space) \

#define XR_LIST_STRUCT_XrFrameEndInfo(_) \
    _(type) \
    _(next) \
    _(displayTime) \
    _(environmentBlendMode) \
    _(layerCount) \
    _(layers) \

#define XR_LIST_STRUCT_XrViewLocateInfo(_) \
    _(type) \
    _(next) \
    _(viewConfigurationType) \
    _(displayTime) \
    _(space) \

#define XR_LIST_STRUCT_XrViewState(_) \
    _(type) \
    _(next) \
    _(viewStateFlags) \

#define XR_LIST_STRUCT_XrFovf(_) \
    _(angleLeft) \
    _(angleRight) \
    _(angleUp) \
    _(angleDown) \

#define XR_LIST_STRUCT_XrView(_) \
    _(type) \
    _(next) \
    _(pose) \
    _(fov) \

#define XR_LIST_STRUCT_XrActionSetCreateInfo(_) \
    _(type) \
    _(next) \
    _(actionSetName) \
    _(localizedActionSetName) \
    _(priority) \

#define XR_LIST_STRUCT_XrActionCreateInfo(_) \
    _(type) \
    _(next) \
    _(actionName) \
    _(actionType) \
    _(countSubactionPaths) \
    _(subactionPaths) \
    _(localizedActionName) \

#define XR_LIST_STRUCT_XrActionSuggestedBinding(_) \
    _(action) \
    _(binding) \

#define XR_LIST_STRUCT_XrInteractionProfileSuggestedBinding(_) \
    _(type) \
    _(next) \
    _(interactionProfile) \
    _(countSuggestedBindings) \
    _(suggestedBindings) \

#define XR_LIST_STRUCT_XrSessionActionSetsAttachInfo(_) \
    _(type) \
    _(next) \
    _(countActionSets) \
    _(actionSets) \

#define XR_LIST_STRUCT_XrInteractionProfileState(_) \
    _(type) \
    _(next) \
    _(interactionProfile) \

#define XR_LIST_STRUCT_XrActionStateGetInfo(_) \
    _(type) \
    _(next) \
    _(action) \
    _(subactionPath) \

#define XR_LIST_STRUCT_XrActionStateBoolean(_) \
    _(type) \
    _(next) \
    _(currentState) \
    _(changedSinceLastSync) \
    _(lastChangeTime) \
    _(isActive) \

#define XR_LIST_STRUCT_XrActionStateFloat(_) \
    _(type) \
    _(next) \
    _(currentState) \
    _(changedSinceLastSync) \
    _(lastChangeTime) \
    _(isActive) \

#define XR_LIST_STRUCT_XrVector2f(_) \
    _(x) \
    _(y) \

#define XR_LIST_STRUCT_XrActionStateVector2f(_) \
    _(type) \
    _(next) \
    _(currentState) \
    _(changedSinceLastSync) \
    _(lastChangeTime) \
    _(isActive) \

#define XR_LIST_STRUCT_XrActionStatePose(_) \
    _(type) \
    _(next) \
    _(isActive) \

#define XR_LIST_STRUCT_XrActiveActionSet(_) \
    _(actionSet) \
    _(subactionPath) \

#define XR_LIST_STRUCT_XrActionsSyncInfo(_) \
    _(type) \
    _(next) \
    _(countActiveActionSets) \
    _(activeActionSets) \

#define XR_LIST_STRUCT_XrBoundSourcesForActionEnumerateInfo(_) \
    _(type) \
    _(next) \
    _(action) \

#define XR_LIST_STRUCT_XrInputSourceLocalizedNameGetInfo(_) \
    _(type) \
    _(next) \
    _(sourcePath) \
    _(whichComponents) \

#define XR_LIST_STRUCT_XrHapticActionInfo(_) \
    _(type) \
    _(next) \
    _(action) \
    _(subactionPath) \

#define XR_LIST_STRUCT_XrHapticBaseHeader(_) \
    _(type) \
    _(next) \

#define XR_LIST_STRUCT_XrBaseInStructure(_) \
    _(type) \
    _(next) \

#define XR_LIST_STRUCT_XrBaseOutStructure(_) \
    _(type) \
    _(next) \

#define XR_LIST_STRUCT_XrOffset2Di(_) \
    _(x) \
    _(y) \

#define XR_LIST_STRUCT_XrExtent2Di(_) \
    _(width) \
    _(height) \

#define XR_LIST_STRUCT_XrRect2Di(_) \
    _(offset) \
    _(extent) \

#define XR_LIST_STRUCT_XrSwapchainSubImage(_) \
    _(swapchain) \
    _(imageRect) \
    _(imageArrayIndex) \

#define XR_LIST_STRUCT_XrCompositionLayerProjectionView(_) \
    _(type) \
    _(next) \
    _(pose) \
    _(fov) \
    _(subImage) \

#define XR_LIST_STRUCT_XrCompositionLayerProjection(_) \
    _(type) \
    _(next) \
    _(layerFlags) \
    _(space) \
    _(viewCount) \
    _(views) \

#define XR_LIST_STRUCT_XrCompositionLayerQuad(_) \
    _(type) \
    _(next) \
    _(layerFlags) \
    _(space) \
    _(eyeVisibility) \
    _(subImage) \
    _(pose) \
    _(size) \

#define XR_LIST_STRUCT_XrEventDataBaseHeader(_) \
    _(type) \
    _(next) \

#define XR_LIST_STRUCT_XrEventDataEventsLost(_) \
    _(type) \
    _(next) \
    _(lostEventCount) \

#define XR_LIST_STRUCT_XrEventDataInstanceLossPending(_) \
    _(type) \
    _(next) \
    _(lossTime) \

#define XR_LIST_STRUCT_XrEventDataSessionStateChanged(_) \
    _(type) \
    _(next) \
    _(session) \
    _(state) \
    _(time) \

#define XR_LIST_STRUCT_XrEventDataReferenceSpaceChangePending(_) \
    _(type) \
    _(next) \
    _(session) \
    _(referenceSpaceType) \
    _(changeTime) \
    _(poseValid) \
    _(poseInPreviousSpace) \

#define XR_LIST_STRUCT_XrEventDataInteractionProfileChanged(_) \
    _(type) \
    _(next) \
    _(session) \

#define XR_LIST_STRUCT_XrHapticVibration(_) \
    _(type) \
    _(next) \
    _(duration) \
    _(frequency) \
    _(amplitude) \

#define XR_LIST_STRUCT_XrOffset2Df(_) \
    _(x) \
    _(y) \

#define XR_LIST_STRUCT_XrRect2Df(_) \
    _(offset) \
    _(extent) \

#define XR_LIST_STRUCT_XrVector4f(_) \
    _(x) \
    _(y) \
    _(z) \
    _(w) \

#define XR_LIST_STRUCT_XrColor4f(_) \
    _(r) \
    _(g) \
    _(b) \
    _(a) \

#define XR_LIST_STRUCT_XrCompositionLayerCubeKHR(_) \
    _(type) \
    _(next) \
    _(layerFlags) \
    _(space) \
    _(eyeVisibility) \
    _(swapchain) \
    _(imageArrayIndex) \
    _(orientation) \

#define XR_LIST_STRUCT_XrInstanceCreateInfoAndroidKHR(_) \
    _(type) \
    _(next) \
    _(applicationVM) \
    _(applicationActivity) \

#define XR_LIST_STRUCT_XrCompositionLayerDepthInfoKHR(_) \
    _(type) \
    _(next) \
    _(subImage) \
    _(minDepth) \
    _(maxDepth) \
    _(nearZ) \
    _(farZ) \

#define XR_LIST_STRUCT_XrVulkanSwapchainFormatListCreateInfoKHR(_) \
    _(type) \
    _(next) \
    _(viewFormatCount) \
    _(viewFormats) \

#define XR_LIST_STRUCT_XrCompositionLayerCylinderKHR(_) \
    _(type) \
    _(next) \
    _(layerFlags) \
    _(space) \
    _(eyeVisibility) \
    _(subImage) \
    _(pose) \
    _(radius) \
    _(centralAngle) \
    _(aspectRatio) \

#define XR_LIST_STRUCT_XrCompositionLayerEquirectKHR(_) \
    _(type) \
    _(next) \
    _(layerFlags) \
    _(space) \
    _(eyeVisibility) \
    _(subImage) \
    _(pose) \
    _(radius) \
    _(scale) \
    _(bias) \

#define XR_LIST_STRUCT_XrGraphicsBindingOpenGLWin32KHR(_) \
    _(type) \
    _(next) \
    _(hDC) \
    _(hGLRC) \

#define XR_LIST_STRUCT_XrGraphicsBindingOpenGLXlibKHR(_) \
    _(type) \
    _(next) \
    _(xDisplay) \
    _(visualid) \
    _(glxFBConfig) \
    _(glxDrawable) \
    _(glxContext) \

#define XR_LIST_STRUCT_XrGraphicsBindingOpenGLXcbKHR(_) \
    _(type) \
    _(next) \
    _(connection) \
    _(screenNumber) \
    _(fbconfigid) \
    _(visualid) \
    _(glxDrawable) \
    _(glxContext) \

#define XR_LIST_STRUCT_XrGraphicsBindingOpenGLWaylandKHR(_) \
    _(type) \
    _(next) \
    _(display) \

#define XR_LIST_STRUCT_XrSwapchainImageOpenGLKHR(_) \
    _(type) \
    _(next) \
    _(image) \

#define XR_LIST_STRUCT_XrGraphicsRequirementsOpenGLKHR(_) \
    _(type) \
    _(next) \
    _(minApiVersionSupported) \
    _(maxApiVersionSupported) \

#define XR_LIST_STRUCT_XrGraphicsBindingOpenGLESAndroidKHR(_) \
    _(type) \
    _(next) \
    _(display) \
    _(config) \
    _(context) \

#define XR_LIST_STRUCT_XrSwapchainImageOpenGLESKHR(_) \
    _(type) \
    _(next) \
    _(image) \

#define XR_LIST_STRUCT_XrGraphicsRequirementsOpenGLESKHR(_) \
    _(type) \
    _(next) \
    _(minApiVersionSupported) \
    _(maxApiVersionSupported) \

#define XR_LIST_STRUCT_XrGraphicsBindingVulkanKHR(_) \
    _(type) \
    _(next) \
    _(instance) \
    _(physicalDevice) \
    _(device) \
    _(queueFamilyIndex) \
    _(queueIndex) \

#define XR_LIST_STRUCT_XrSwapchainImageVulkanKHR(_) \
    _(type) \
    _(next) \
    _(image) \

#define XR_LIST_STRUCT_XrGraphicsRequirementsVulkanKHR(_) \
    _(type) \
    _(next) \
    _(minApiVersionSupported) \
    _(maxApiVersionSupported) \

#define XR_LIST_STRUCT_XrGraphicsBindingD3D11KHR(_) \
    _(type) \
    _(next) \
    _(device) \

#define XR_LIST_STRUCT_XrSwapchainImageD3D11KHR(_) \
    _(type) \
    _(next) \
    _(texture) \

#define XR_LIST_STRUCT_XrGraphicsRequirementsD3D11KHR(_) \
    _(type) \
    _(next) \
    _(adapterLuid) \
    _(minFeatureLevel) \

#define XR_LIST_STRUCT_XrGraphicsBindingD3D12KHR(_) \
    _(type) \
    _(next) \
    _(device) \
    _(queue) \

#define XR_LIST_STRUCT_XrSwapchainImageD3D12KHR(_) \
    _(type) \
    _(next) \
    _(texture) \

#define XR_LIST_STRUCT_XrGraphicsRequirementsD3D12KHR(_) \
    _(type) \
    _(next) \
    _(adapterLuid) \
    _(minFeatureLevel) \

#define XR_LIST_STRUCT_XrVisibilityMaskKHR(_) \
    _(type) \
    _(next) \
    _(vertexCapacityInput) \
    _(vertexCountOutput) \
    _(vertices) \
    _(indexCapacityInput) \
    _(indexCountOutput) \
    _(indices) \

#define XR_LIST_STRUCT_XrEventDataVisibilityMaskChangedKHR(_) \
    _(type) \
    _(next) \
    _(session) \
    _(viewConfigurationType) \
    _(viewIndex) \

#define XR_LIST_STRUCT_XrCompositionLayerColorScaleBiasKHR(_) \
    _(type) \
    _(next) \
    _(colorScale) \
    _(colorBias) \

#define XR_LIST_STRUCT_XrLoaderInitInfoBaseHeaderKHR(_) \
    _(type) \
    _(next) \

#define XR_LIST_STRUCT_XrLoaderInitInfoAndroidKHR(_) \
    _(type) \
    _(next) \
    _(applicationVM) \
    _(applicationContext) \

#define XR_LIST_STRUCT_XrVulkanInstanceCreateInfoKHR(_) \
    _(type) \
    _(next) \
    _(systemId) \
    _(createFlags) \
    _(pfnGetInstanceProcAddr) \
    _(vulkanCreateInfo) \
    _(vulkanAllocator) \

#define XR_LIST_STRUCT_XrVulkanDeviceCreateInfoKHR(_) \
    _(type) \
    _(next) \
    _(systemId) \
    _(createFlags) \
    _(pfnGetInstanceProcAddr) \
    _(vulkanPhysicalDevice) \
    _(vulkanCreateInfo) \
    _(vulkanAllocator) \

#define XR_LIST_STRUCT_XrVulkanGraphicsDeviceGetInfoKHR(_) \
    _(type) \
    _(next) \
    _(systemId) \
    _(vulkanInstance) \

#define XR_LIST_STRUCT_XrCompositionLayerEquirect2KHR(_) \
    _(type) \
    _(next) \
    _(layerFlags) \
    _(space) \
    _(eyeVisibility) \
    _(subImage) \
    _(pose) \
    _(radius) \
    _(centralHorizontalAngle) \
    _(upperVerticalAngle) \
    _(lowerVerticalAngle) \

#define XR_LIST_STRUCT_XrBindingModificationBaseHeaderKHR(_) \
    _(type) \
    _(next) \

#define XR_LIST_STRUCT_XrBindingModificationsKHR(_) \
    _(type) \
    _(next) \
    _(bindingModificationCount) \
    _(bindingModifications) \

#define XR_LIST_STRUCT_XrEventDataPerfSettingsEXT(_) \
    _(type) \
    _(next) \
    _(domain) \
    _(subDomain) \
    _(fromLevel) \
    _(toLevel) \

#define XR_LIST_STRUCT_XrDebugUtilsObjectNameInfoEXT(_) \
    _(type) \
    _(next) \
    _(objectType) \
    _(objectHandle) \
    _(objectName) \

#define XR_LIST_STRUCT_XrDebugUtilsLabelEXT(_) \
    _(type) \
    _(next) \
    _(labelName) \

#define XR_LIST_STRUCT_XrDebugUtilsMessengerCallbackDataEXT(_) \
    _(type) \
    _(next) \
    _(messageId) \
    _(functionName) \
    _(message) \
    _(objectCount) \
    _(objects) \
    _(sessionLabelCount) \
    _(sessionLabels) \

#define XR_LIST_STRUCT_XrDebugUtilsMessengerCreateInfoEXT(_) \
    _(type) \
    _(next) \
    _(messageSeverities) \
    _(messageTypes) \
    _(userCallback) \
    _(userData) \

#define XR_LIST_STRUCT_XrSystemEyeGazeInteractionPropertiesEXT(_) \
    _(type) \
    _(next) \
    _(supportsEyeGazeInteraction) \

#define XR_LIST_STRUCT_XrEyeGazeSampleTimeEXT(_) \
    _(type) \
    _(next) \
    _(time) \

#define XR_LIST_STRUCT_XrSessionCreateInfoOverlayEXTX(_) \
    _(type) \
    _(next) \
    _(createFlags) \
    _(sessionLayersPlacement) \

#define XR_LIST_STRUCT_XrEventDataMainSessionVisibilityChangedEXTX(_) \
    _(type) \
    _(next) \
    _(visible) \
    _(flags) \

#define XR_LIST_STRUCT_XrSpatialAnchorCreateInfoMSFT(_) \
    _(type) \
    _(next) \
    _(space) \
    _(pose) \
    _(time) \

#define XR_LIST_STRUCT_XrSpatialAnchorSpaceCreateInfoMSFT(_) \
    _(type) \
    _(next) \
    _(anchor) \
    _(poseInAnchorSpace) \

#define XR_LIST_STRUCT_XrCompositionLayerImageLayoutFB(_) \
    _(type) \
    _(next) \
    _(flags) \

#define XR_LIST_STRUCT_XrCompositionLayerAlphaBlendFB(_) \
    _(type) \
    _(next) \
    _(srcFactorColor) \
    _(dstFactorColor) \
    _(srcFactorAlpha) \
    _(dstFactorAlpha) \

#define XR_LIST_STRUCT_XrViewConfigurationDepthRangeEXT(_) \
    _(type) \
    _(next) \
    _(recommendedNearZ) \
    _(minNearZ) \
    _(recommendedFarZ) \
    _(maxFarZ) \

#define XR_LIST_STRUCT_XrGraphicsBindingEGLMNDX(_) \
    _(type) \
    _(next) \
    _(getProcAddress) \
    _(display) \
    _(config) \
    _(context) \

#define XR_LIST_STRUCT_XrSpatialGraphNodeSpaceCreateInfoMSFT(_) \
    _(type) \
    _(next) \
    _(nodeType) \
    _(nodeId) \
    _(pose) \

#define XR_LIST_STRUCT_XrSpatialGraphStaticNodeBindingCreateInfoMSFT(_) \
    _(type) \
    _(next) \
    _(space) \
    _(poseInSpace) \
    _(time) \

#define XR_LIST_STRUCT_XrSpatialGraphNodeBindingPropertiesGetInfoMSFT(_) \
    _(type) \
    _(next) \

#define XR_LIST_STRUCT_XrSpatialGraphNodeBindingPropertiesMSFT(_) \
    _(type) \
    _(next) \
    _(nodeId) \
    _(poseInNodeSpace) \

#define XR_LIST_STRUCT_XrSystemHandTrackingPropertiesEXT(_) \
    _(type) \
    _(next) \
    _(supportsHandTracking) \

#define XR_LIST_STRUCT_XrHandTrackerCreateInfoEXT(_) \
    _(type) \
    _(next) \
    _(hand) \
    _(handJointSet) \

#define XR_LIST_STRUCT_XrHandJointsLocateInfoEXT(_) \
    _(type) \
    _(next) \
    _(baseSpace) \
    _(time) \

#define XR_LIST_STRUCT_XrHandJointLocationEXT(_) \
    _(locationFlags) \
    _(pose) \
    _(radius) \

#define XR_LIST_STRUCT_XrHandJointVelocityEXT(_) \
    _(velocityFlags) \
    _(linearVelocity) \
    _(angularVelocity) \

#define XR_LIST_STRUCT_XrHandJointLocationsEXT(_) \
    _(type) \
    _(next) \
    _(isActive) \
    _(jointCount) \
    _(jointLocations) \

#define XR_LIST_STRUCT_XrHandJointVelocitiesEXT(_) \
    _(type) \
    _(next) \
    _(jointCount) \
    _(jointVelocities) \

#define XR_LIST_STRUCT_XrSystemHandTrackingMeshPropertiesMSFT(_) \
    _(type) \
    _(next) \
    _(supportsHandTrackingMesh) \
    _(maxHandMeshIndexCount) \
    _(maxHandMeshVertexCount) \

#define XR_LIST_STRUCT_XrHandMeshSpaceCreateInfoMSFT(_) \
    _(type) \
    _(next) \
    _(handPoseType) \
    _(poseInHandMeshSpace) \

#define XR_LIST_STRUCT_XrHandMeshUpdateInfoMSFT(_) \
    _(type) \
    _(next) \
    _(time) \
    _(handPoseType) \

#define XR_LIST_STRUCT_XrHandMeshIndexBufferMSFT(_) \
    _(indexBufferKey) \
    _(indexCapacityInput) \
    _(indexCountOutput) \
    _(indices) \

#define XR_LIST_STRUCT_XrHandMeshVertexMSFT(_) \
    _(position) \
    _(normal) \

#define XR_LIST_STRUCT_XrHandMeshVertexBufferMSFT(_) \
    _(vertexUpdateTime) \
    _(vertexCapacityInput) \
    _(vertexCountOutput) \
    _(vertices) \

#define XR_LIST_STRUCT_XrHandMeshMSFT(_) \
    _(type) \
    _(next) \
    _(isActive) \
    _(indexBufferChanged) \
    _(vertexBufferChanged) \
    _(indexBuffer) \
    _(vertexBuffer) \

#define XR_LIST_STRUCT_XrHandPoseTypeInfoMSFT(_) \
    _(type) \
    _(next) \
    _(handPoseType) \

#define XR_LIST_STRUCT_XrSecondaryViewConfigurationSessionBeginInfoMSFT(_) \
    _(type) \
    _(next) \
    _(viewConfigurationCount) \
    _(enabledViewConfigurationTypes) \

#define XR_LIST_STRUCT_XrSecondaryViewConfigurationStateMSFT(_) \
    _(type) \
    _(next) \
    _(viewConfigurationType) \
    _(active) \

#define XR_LIST_STRUCT_XrSecondaryViewConfigurationFrameStateMSFT(_) \
    _(type) \
    _(next) \
    _(viewConfigurationCount) \
    _(viewConfigurationStates) \

#define XR_LIST_STRUCT_XrSecondaryViewConfigurationLayerInfoMSFT(_) \
    _(type) \
    _(next) \
    _(viewConfigurationType) \
    _(environmentBlendMode) \
    _(layerCount) \
    _(layers) \

#define XR_LIST_STRUCT_XrSecondaryViewConfigurationFrameEndInfoMSFT(_) \
    _(type) \
    _(next) \
    _(viewConfigurationCount) \
    _(viewConfigurationLayersInfo) \

#define XR_LIST_STRUCT_XrSecondaryViewConfigurationSwapchainCreateInfoMSFT(_) \
    _(type) \
    _(next) \
    _(viewConfigurationType) \

#define XR_LIST_STRUCT_XrControllerModelKeyStateMSFT(_) \
    _(type) \
    _(next) \
    _(modelKey) \

#define XR_LIST_STRUCT_XrControllerModelNodePropertiesMSFT(_) \
    _(type) \
    _(next) \
    _(parentNodeName) \
    _(nodeName) \

#define XR_LIST_STRUCT_XrControllerModelPropertiesMSFT(_) \
    _(type) \
    _(next) \
    _(nodeCapacityInput) \
    _(nodeCountOutput) \
    _(nodeProperties) \

#define XR_LIST_STRUCT_XrControllerModelNodeStateMSFT(_) \
    _(type) \
    _(next) \
    _(nodePose) \

#define XR_LIST_STRUCT_XrControllerModelStateMSFT(_) \
    _(type) \
    _(next) \
    _(nodeCapacityInput) \
    _(nodeCountOutput) \
    _(nodeStates) \

#define XR_LIST_STRUCT_XrViewConfigurationViewFovEPIC(_) \
    _(type) \
    _(next) \
    _(recommendedFov) \
    _(maxMutableFov) \

#define XR_LIST_STRUCT_XrHolographicWindowAttachmentMSFT(_) \
    _(type) \
    _(next) \
    _(holographicSpace) \
    _(coreWindow) \

#define XR_LIST_STRUCT_XrCompositionLayerReprojectionInfoMSFT(_) \
    _(type) \
    _(next) \
    _(reprojectionMode) \

#define XR_LIST_STRUCT_XrCompositionLayerReprojectionPlaneOverrideMSFT(_) \
    _(type) \
    _(next) \
    _(position) \
    _(normal) \
    _(velocity) \

#define XR_LIST_STRUCT_XrAndroidSurfaceSwapchainCreateInfoFB(_) \
    _(type) \
    _(next) \
    _(createFlags) \

#define XR_LIST_STRUCT_XrSwapchainStateBaseHeaderFB(_) \
    _(type) \
    _(next) \

#define XR_LIST_STRUCT_XrCompositionLayerSecureContentFB(_) \
    _(type) \
    _(next) \
    _(flags) \

#define XR_LIST_STRUCT_XrInteractionProfileDpadBindingEXT(_) \
    _(type) \
    _(next) \
    _(binding) \
    _(actionSet) \
    _(forceThreshold) \
    _(forceThresholdReleased) \
    _(centerRegion) \
    _(wedgeAngle) \
    _(isSticky) \
    _(onHaptic) \
    _(offHaptic) \

#define XR_LIST_STRUCT_XrInteractionProfileAnalogThresholdVALVE(_) \
    _(type) \
    _(next) \
    _(action) \
    _(binding) \
    _(onThreshold) \
    _(offThreshold) \
    _(onHaptic) \
    _(offHaptic) \

#define XR_LIST_STRUCT_XrHandJointsMotionRangeInfoEXT(_) \
    _(type) \
    _(next) \
    _(handJointsMotionRange) \

#define XR_LIST_STRUCT_XrUuidMSFT(_) \
    _(bytes) \

#define XR_LIST_STRUCT_XrSceneObserverCreateInfoMSFT(_) \
    _(type) \
    _(next) \

#define XR_LIST_STRUCT_XrSceneCreateInfoMSFT(_) \
    _(type) \
    _(next) \

#define XR_LIST_STRUCT_XrSceneSphereBoundMSFT(_) \
    _(center) \
    _(radius) \

#define XR_LIST_STRUCT_XrSceneOrientedBoxBoundMSFT(_) \
    _(pose) \
    _(extents) \

#define XR_LIST_STRUCT_XrSceneFrustumBoundMSFT(_) \
    _(pose) \
    _(fov) \
    _(farDistance) \

#define XR_LIST_STRUCT_XrSceneBoundsMSFT(_) \
    _(space) \
    _(time) \
    _(sphereCount) \
    _(spheres) \
    _(boxCount) \
    _(boxes) \
    _(frustumCount) \
    _(frustums) \

#define XR_LIST_STRUCT_XrNewSceneComputeInfoMSFT(_) \
    _(type) \
    _(next) \
    _(requestedFeatureCount) \
    _(requestedFeatures) \
    _(consistency) \
    _(bounds) \

#define XR_LIST_STRUCT_XrVisualMeshComputeLodInfoMSFT(_) \
    _(type) \
    _(next) \
    _(lod) \

#define XR_LIST_STRUCT_XrSceneComponentMSFT(_) \
    _(componentType) \
    _(id) \
    _(parentId) \
    _(updateTime) \

#define XR_LIST_STRUCT_XrSceneComponentsMSFT(_) \
    _(type) \
    _(next) \
    _(componentCapacityInput) \
    _(componentCountOutput) \
    _(components) \

#define XR_LIST_STRUCT_XrSceneComponentsGetInfoMSFT(_) \
    _(type) \
    _(next) \
    _(componentType) \

#define XR_LIST_STRUCT_XrSceneComponentLocationMSFT(_) \
    _(flags) \
    _(pose) \

#define XR_LIST_STRUCT_XrSceneComponentLocationsMSFT(_) \
    _(type) \
    _(next) \
    _(locationCount) \
    _(locations) \

#define XR_LIST_STRUCT_XrSceneComponentsLocateInfoMSFT(_) \
    _(type) \
    _(next) \
    _(baseSpace) \
    _(time) \
    _(componentIdCount) \
    _(componentIds) \

#define XR_LIST_STRUCT_XrSceneObjectMSFT(_) \
    _(objectType) \

#define XR_LIST_STRUCT_XrSceneObjectsMSFT(_) \
    _(type) \
    _(next) \
    _(sceneObjectCount) \
    _(sceneObjects) \

#define XR_LIST_STRUCT_XrSceneComponentParentFilterInfoMSFT(_) \
    _(type) \
    _(next) \
    _(parentId) \

#define XR_LIST_STRUCT_XrSceneObjectTypesFilterInfoMSFT(_) \
    _(type) \
    _(next) \
    _(objectTypeCount) \
    _(objectTypes) \

#define XR_LIST_STRUCT_XrScenePlaneMSFT(_) \
    _(alignment) \
    _(size) \
    _(meshBufferId) \
    _(supportsIndicesUint16) \

#define XR_LIST_STRUCT_XrScenePlanesMSFT(_) \
    _(type) \
    _(next) \
    _(scenePlaneCount) \
    _(scenePlanes) \

#define XR_LIST_STRUCT_XrScenePlaneAlignmentFilterInfoMSFT(_) \
    _(type) \
    _(next) \
    _(alignmentCount) \
    _(alignments) \

#define XR_LIST_STRUCT_XrSceneMeshMSFT(_) \
    _(meshBufferId) \
    _(supportsIndicesUint16) \

#define XR_LIST_STRUCT_XrSceneMeshesMSFT(_) \
    _(type) \
    _(next) \
    _(sceneMeshCount) \
    _(sceneMeshes) \

#define XR_LIST_STRUCT_XrSceneMeshBuffersGetInfoMSFT(_) \
    _(type) \
    _(next) \
    _(meshBufferId) \

#define XR_LIST_STRUCT_XrSceneMeshBuffersMSFT(_) \
    _(type) \
    _(next) \

#define XR_LIST_STRUCT_XrSceneMeshVertexBufferMSFT(_) \
    _(type) \
    _(next) \
    _(vertexCapacityInput) \
    _(vertexCountOutput) \
    _(vertices) \

#define XR_LIST_STRUCT_XrSceneMeshIndicesUint32MSFT(_) \
    _(type) \
    _(next) \
    _(indexCapacityInput) \
    _(indexCountOutput) \
    _(indices) \

#define XR_LIST_STRUCT_XrSceneMeshIndicesUint16MSFT(_) \
    _(type) \
    _(next) \
    _(indexCapacityInput) \
    _(indexCountOutput) \
    _(indices) \

#define XR_LIST_STRUCT_XrSerializedSceneFragmentDataGetInfoMSFT(_) \
    _(type) \
    _(next) \
    _(sceneFragmentId) \

#define XR_LIST_STRUCT_XrDeserializeSceneFragmentMSFT(_) \
    _(bufferSize) \
    _(buffer) \

#define XR_LIST_STRUCT_XrSceneDeserializeInfoMSFT(_) \
    _(type) \
    _(next) \
    _(fragmentCount) \
    _(fragments) \

#define XR_LIST_STRUCT_XrEventDataDisplayRefreshRateChangedFB(_) \
    _(type) \
    _(next) \
    _(fromDisplayRefreshRate) \
    _(toDisplayRefreshRate) \

#define XR_LIST_STRUCT_XrViveTrackerPathsHTCX(_) \
    _(type) \
    _(next) \
    _(persistentPath) \
    _(rolePath) \

#define XR_LIST_STRUCT_XrEventDataViveTrackerConnectedHTCX(_) \
    _(type) \
    _(next) \
    _(paths) \

#define XR_LIST_STRUCT_XrSystemFacialTrackingPropertiesHTC(_) \
    _(type) \
    _(next) \
    _(supportEyeFacialTracking) \
    _(supportLipFacialTracking) \

#define XR_LIST_STRUCT_XrFacialExpressionsHTC(_) \
    _(type) \
    _(next) \
    _(isActive) \
    _(sampleTime) \
    _(expressionCount) \
    _(expressionWeightings) \

#define XR_LIST_STRUCT_XrFacialTrackerCreateInfoHTC(_) \
    _(type) \
    _(next) \
    _(facialTrackingType) \

#define XR_LIST_STRUCT_XrSystemColorSpacePropertiesFB(_) \
    _(type) \
    _(next) \
    _(colorSpace) \

#define XR_LIST_STRUCT_XrVector4sFB(_) \
    _(x) \
    _(y) \
    _(z) \
    _(w) \

#define XR_LIST_STRUCT_XrHandTrackingMeshFB(_) \
    _(type) \
    _(next) \
    _(jointCapacityInput) \
    _(jointCountOutput) \
    _(jointBindPoses) \
    _(jointRadii) \
    _(jointParents) \
    _(vertexCapacityInput) \
    _(vertexCountOutput) \
    _(vertexPositions) \
    _(vertexNormals) \
    _(vertexUVs) \
    _(vertexBlendIndices) \
    _(vertexBlendWeights) \
    _(indexCapacityInput) \
    _(indexCountOutput) \
    _(indices) \

#define XR_LIST_STRUCT_XrHandTrackingScaleFB(_) \
    _(type) \
    _(next) \
    _(sensorOutput) \
    _(currentOutput) \
    _(overrideHandScale) \
    _(overrideValueInput) \

#define XR_LIST_STRUCT_XrHandTrackingAimStateFB(_) \
    _(type) \
    _(next) \
    _(status) \
    _(aimPose) \
    _(pinchStrengthIndex) \
    _(pinchStrengthMiddle) \
    _(pinchStrengthRing) \
    _(pinchStrengthLittle) \

#define XR_LIST_STRUCT_XrHandCapsuleFB(_) \
    _(points) \
    _(radius) \
    _(joint) \

#define XR_LIST_STRUCT_XrHandTrackingCapsulesStateFB(_) \
    _(type) \
    _(next) \
    _(capsules) \

#define XR_LIST_STRUCT_XrSystemSpatialEntityPropertiesFB(_) \
    _(type) \
    _(next) \
    _(supportsSpatialEntity) \

#define XR_LIST_STRUCT_XrSpatialAnchorCreateInfoFB(_) \
    _(type) \
    _(next) \
    _(space) \
    _(poseInSpace) \
    _(time) \

#define XR_LIST_STRUCT_XrSpaceComponentStatusSetInfoFB(_) \
    _(type) \
    _(next) \
    _(componentType) \
    _(enabled) \
    _(timeout) \

#define XR_LIST_STRUCT_XrSpaceComponentStatusFB(_) \
    _(type) \
    _(next) \
    _(enabled) \
    _(changePending) \

#define XR_LIST_STRUCT_XrUuidEXT(_) \
    _(data) \

#define XR_LIST_STRUCT_XrEventDataSpatialAnchorCreateCompleteFB(_) \
    _(type) \
    _(next) \
    _(requestId) \
    _(result) \
    _(space) \
    _(uuid) \

#define XR_LIST_STRUCT_XrEventDataSpaceSetStatusCompleteFB(_) \
    _(type) \
    _(next) \
    _(requestId) \
    _(result) \
    _(space) \
    _(uuid) \
    _(componentType) \
    _(enabled) \

#define XR_LIST_STRUCT_XrFoveationProfileCreateInfoFB(_) \
    _(type) \
    _(next) \

#define XR_LIST_STRUCT_XrSwapchainCreateInfoFoveationFB(_) \
    _(type) \
    _(next) \
    _(flags) \

#define XR_LIST_STRUCT_XrSwapchainStateFoveationFB(_) \
    _(type) \
    _(next) \
    _(flags) \
    _(profile) \

#define XR_LIST_STRUCT_XrFoveationLevelProfileCreateInfoFB(_) \
    _(type) \
    _(next) \
    _(level) \
    _(verticalOffset) \
    _(dynamic) \

#define XR_LIST_STRUCT_XrSystemKeyboardTrackingPropertiesFB(_) \
    _(type) \
    _(next) \
    _(supportsKeyboardTracking) \

#define XR_LIST_STRUCT_XrKeyboardTrackingDescriptionFB(_) \
    _(trackedKeyboardId) \
    _(size) \
    _(flags) \
    _(name) \

#define XR_LIST_STRUCT_XrKeyboardSpaceCreateInfoFB(_) \
    _(type) \
    _(next) \
    _(trackedKeyboardId) \

#define XR_LIST_STRUCT_XrKeyboardTrackingQueryFB(_) \
    _(type) \
    _(next) \
    _(flags) \

#define XR_LIST_STRUCT_XrTriangleMeshCreateInfoFB(_) \
    _(type) \
    _(next) \
    _(flags) \
    _(windingOrder) \
    _(vertexCount) \
    _(vertexBuffer) \
    _(triangleCount) \
    _(indexBuffer) \

#define XR_LIST_STRUCT_XrSystemPassthroughPropertiesFB(_) \
    _(type) \
    _(next) \
    _(supportsPassthrough) \

#define XR_LIST_STRUCT_XrPassthroughCreateInfoFB(_) \
    _(type) \
    _(next) \
    _(flags) \

#define XR_LIST_STRUCT_XrPassthroughLayerCreateInfoFB(_) \
    _(type) \
    _(next) \
    _(passthrough) \
    _(flags) \
    _(purpose) \

#define XR_LIST_STRUCT_XrCompositionLayerPassthroughFB(_) \
    _(type) \
    _(next) \
    _(flags) \
    _(space) \
    _(layerHandle) \

#define XR_LIST_STRUCT_XrGeometryInstanceCreateInfoFB(_) \
    _(type) \
    _(next) \
    _(layer) \
    _(mesh) \
    _(baseSpace) \
    _(pose) \
    _(scale) \

#define XR_LIST_STRUCT_XrGeometryInstanceTransformFB(_) \
    _(type) \
    _(next) \
    _(baseSpace) \
    _(time) \
    _(pose) \
    _(scale) \

#define XR_LIST_STRUCT_XrPassthroughStyleFB(_) \
    _(type) \
    _(next) \
    _(textureOpacityFactor) \
    _(edgeColor) \

#define XR_LIST_STRUCT_XrPassthroughColorMapMonoToRgbaFB(_) \
    _(type) \
    _(next) \
    _(textureColorMap) \

#define XR_LIST_STRUCT_XrPassthroughColorMapMonoToMonoFB(_) \
    _(type) \
    _(next) \
    _(textureColorMap) \

#define XR_LIST_STRUCT_XrPassthroughBrightnessContrastSaturationFB(_) \
    _(type) \
    _(next) \
    _(brightness) \
    _(contrast) \
    _(saturation) \

#define XR_LIST_STRUCT_XrEventDataPassthroughStateChangedFB(_) \
    _(type) \
    _(next) \
    _(flags) \

#define XR_LIST_STRUCT_XrRenderModelPathInfoFB(_) \
    _(type) \
    _(next) \
    _(path) \

#define XR_LIST_STRUCT_XrRenderModelPropertiesFB(_) \
    _(type) \
    _(next) \
    _(vendorId) \
    _(modelName) \
    _(modelKey) \
    _(modelVersion) \
    _(flags) \

#define XR_LIST_STRUCT_XrRenderModelBufferFB(_) \
    _(type) \
    _(next) \
    _(bufferCapacityInput) \
    _(bufferCountOutput) \
    _(buffer) \

#define XR_LIST_STRUCT_XrRenderModelLoadInfoFB(_) \
    _(type) \
    _(next) \
    _(modelKey) \

#define XR_LIST_STRUCT_XrSystemRenderModelPropertiesFB(_) \
    _(type) \
    _(next) \
    _(supportsRenderModelLoading) \

#define XR_LIST_STRUCT_XrRenderModelCapabilitiesRequestFB(_) \
    _(type) \
    _(next) \
    _(flags) \

#define XR_LIST_STRUCT_XrViewLocateFoveatedRenderingVARJO(_) \
    _(type) \
    _(next) \
    _(foveatedRenderingActive) \

#define XR_LIST_STRUCT_XrFoveatedViewConfigurationViewVARJO(_) \
    _(type) \
    _(next) \
    _(foveatedRenderingActive) \

#define XR_LIST_STRUCT_XrSystemFoveatedRenderingPropertiesVARJO(_) \
    _(type) \
    _(next) \
    _(supportsFoveatedRendering) \

#define XR_LIST_STRUCT_XrCompositionLayerDepthTestVARJO(_) \
    _(type) \
    _(next) \
    _(depthTestRangeNearZ) \
    _(depthTestRangeFarZ) \

#define XR_LIST_STRUCT_XrSystemMarkerTrackingPropertiesVARJO(_) \
    _(type) \
    _(next) \
    _(supportsMarkerTracking) \

#define XR_LIST_STRUCT_XrEventDataMarkerTrackingUpdateVARJO(_) \
    _(type) \
    _(next) \
    _(markerId) \
    _(isActive) \
    _(isPredicted) \
    _(time) \

#define XR_LIST_STRUCT_XrMarkerSpaceCreateInfoVARJO(_) \
    _(type) \
    _(next) \
    _(markerId) \
    _(poseInMarkerSpace) \

#define XR_LIST_STRUCT_XrSpatialAnchorPersistenceNameMSFT(_) \
    _(name) \

#define XR_LIST_STRUCT_XrSpatialAnchorPersistenceInfoMSFT(_) \
    _(type) \
    _(next) \
    _(spatialAnchorPersistenceName) \
    _(spatialAnchor) \

#define XR_LIST_STRUCT_XrSpatialAnchorFromPersistedAnchorCreateInfoMSFT(_) \
    _(type) \
    _(next) \
    _(spatialAnchorStore) \
    _(spatialAnchorPersistenceName) \

#define XR_LIST_STRUCT_XrSpaceQueryInfoBaseHeaderFB(_) \
    _(type) \
    _(next) \

#define XR_LIST_STRUCT_XrSpaceFilterInfoBaseHeaderFB(_) \
    _(type) \
    _(next) \

#define XR_LIST_STRUCT_XrSpaceQueryInfoFB(_) \
    _(type) \
    _(next) \
    _(queryAction) \
    _(maxResultCount) \
    _(timeout) \
    _(filter) \
    _(excludeFilter) \

#define XR_LIST_STRUCT_XrSpaceStorageLocationFilterInfoFB(_) \
    _(type) \
    _(next) \
    _(location) \

#define XR_LIST_STRUCT_XrSpaceUuidFilterInfoFB(_) \
    _(type) \
    _(next) \
    _(uuidCount) \
    _(uuids) \

#define XR_LIST_STRUCT_XrSpaceComponentFilterInfoFB(_) \
    _(type) \
    _(next) \
    _(componentType) \

#define XR_LIST_STRUCT_XrSpaceQueryResultFB(_) \
    _(space) \
    _(uuid) \

#define XR_LIST_STRUCT_XrSpaceQueryResultsFB(_) \
    _(type) \
    _(next) \
    _(resultCapacityInput) \
    _(resultCountOutput) \
    _(results) \

#define XR_LIST_STRUCT_XrEventDataSpaceQueryResultsAvailableFB(_) \
    _(type) \
    _(next) \
    _(requestId) \

#define XR_LIST_STRUCT_XrEventDataSpaceQueryCompleteFB(_) \
    _(type) \
    _(next) \
    _(requestId) \
    _(result) \

#define XR_LIST_STRUCT_XrSpaceSaveInfoFB(_) \
    _(type) \
    _(next) \
    _(space) \
    _(location) \
    _(persistenceMode) \

#define XR_LIST_STRUCT_XrSpaceEraseInfoFB(_) \
    _(type) \
    _(next) \
    _(space) \
    _(location) \

#define XR_LIST_STRUCT_XrEventDataSpaceSaveCompleteFB(_) \
    _(type) \
    _(next) \
    _(requestId) \
    _(result) \
    _(space) \
    _(uuid) \
    _(location) \

#define XR_LIST_STRUCT_XrEventDataSpaceEraseCompleteFB(_) \
    _(type) \
    _(next) \
    _(requestId) \
    _(result) \
    _(space) \
    _(uuid) \
    _(location) \

#define XR_LIST_STRUCT_XrSwapchainImageFoveationVulkanFB(_) \
    _(type) \
    _(next) \
    _(image) \
    _(width) \
    _(height) \

#define XR_LIST_STRUCT_XrSwapchainStateAndroidSurfaceDimensionsFB(_) \
    _(type) \
    _(next) \
    _(width) \
    _(height) \

#define XR_LIST_STRUCT_XrSwapchainStateSamplerOpenGLESFB(_) \
    _(type) \
    _(next) \
    _(minFilter) \
    _(magFilter) \
    _(wrapModeS) \
    _(wrapModeT) \
    _(swizzleRed) \
    _(swizzleGreen) \
    _(swizzleBlue) \
    _(swizzleAlpha) \
    _(maxAnisotropy) \
    _(borderColor) \

#define XR_LIST_STRUCT_XrSwapchainStateSamplerVulkanFB(_) \
    _(type) \
    _(next) \
    _(minFilter) \
    _(magFilter) \
    _(mipmapMode) \
    _(wrapModeS) \
    _(wrapModeT) \
    _(swizzleRed) \
    _(swizzleGreen) \
    _(swizzleBlue) \
    _(swizzleAlpha) \
    _(maxAnisotropy) \
    _(borderColor) \

#define XR_LIST_STRUCT_XrCompositionLayerSpaceWarpInfoFB(_) \
    _(type) \
    _(next) \
    _(layerFlags) \
    _(motionVectorSubImage) \
    _(appSpaceDeltaPose) \
    _(depthSubImage) \
    _(minDepth) \
    _(maxDepth) \
    _(nearZ) \
    _(farZ) \

#define XR_LIST_STRUCT_XrSystemSpaceWarpPropertiesFB(_) \
    _(type) \
    _(next) \
    _(recommendedMotionVectorImageRectWidth) \
    _(recommendedMotionVectorImageRectHeight) \

#define XR_LIST_STRUCT_XrDigitalLensControlALMALENCE(_) \
    _(type) \
    _(next) \
    _(flags) \

#define XR_LIST_STRUCT_XrSpaceContainerFB(_) \
    _(type) \
    _(next) \
    _(uuidCapacityInput) \
    _(uuidCountOutput) \
    _(uuids) \

#define XR_LIST_STRUCT_XrPassthroughKeyboardHandsIntensityFB(_) \
    _(type) \
    _(next) \
    _(leftHandIntensity) \
    _(rightHandIntensity) \

#define XR_LIST_STRUCT_XrCompositionLayerSettingsFB(_) \
    _(type) \
    _(next) \
    _(layerFlags) \

#define XR_LIST_STRUCT_XrVulkanSwapchainCreateInfoMETA(_) \
    _(type) \
    _(next) \
    _(additionalCreateFlags) \
    _(additionalUsageFlags) \

#define XR_LIST_STRUCT_XrPerformanceMetricsStateMETA(_) \
    _(type) \
    _(next) \
    _(enabled) \

#define XR_LIST_STRUCT_XrPerformanceMetricsCounterMETA(_) \
    _(type) \
    _(next) \
    _(counterFlags) \
    _(counterUnit) \
    _(uintValue) \
    _(floatValue) \



#define XR_LIST_STRUCTURE_TYPES_CORE(_) \
    _(XrApiLayerProperties, XR_TYPE_API_LAYER_PROPERTIES) \
    _(XrExtensionProperties, XR_TYPE_EXTENSION_PROPERTIES) \
    _(XrInstanceCreateInfo, XR_TYPE_INSTANCE_CREATE_INFO) \
    _(XrInstanceProperties, XR_TYPE_INSTANCE_PROPERTIES) \
    _(XrEventDataBuffer, XR_TYPE_EVENT_DATA_BUFFER) \
    _(XrSystemGetInfo, XR_TYPE_SYSTEM_GET_INFO) \
    _(XrSystemProperties, XR_TYPE_SYSTEM_PROPERTIES) \
    _(XrSessionCreateInfo, XR_TYPE_SESSION_CREATE_INFO) \
    _(XrSpaceVelocity, XR_TYPE_SPACE_VELOCITY) \
    _(XrReferenceSpaceCreateInfo, XR_TYPE_REFERENCE_SPACE_CREATE_INFO) \
    _(XrActionSpaceCreateInfo, XR_TYPE_ACTION_SPACE_CREATE_INFO) \
    _(XrSpaceLocation, XR_TYPE_SPACE_LOCATION) \
    _(XrViewConfigurationProperties, XR_TYPE_VIEW_CONFIGURATION_PROPERTIES) \
    _(XrViewConfigurationView, XR_TYPE_VIEW_CONFIGURATION_VIEW) \
    _(XrSwapchainCreateInfo, XR_TYPE_SWAPCHAIN_CREATE_INFO) \
    _(XrSwapchainImageAcquireInfo, XR_TYPE_SWAPCHAIN_IMAGE_ACQUIRE_INFO) \
    _(XrSwapchainImageWaitInfo, XR_TYPE_SWAPCHAIN_IMAGE_WAIT_INFO) \
    _(XrSwapchainImageReleaseInfo, XR_TYPE_SWAPCHAIN_IMAGE_RELEASE_INFO) \
    _(XrSessionBeginInfo, XR_TYPE_SESSION_BEGIN_INFO) \
    _(XrFrameWaitInfo, XR_TYPE_FRAME_WAIT_INFO) \
    _(XrFrameState, XR_TYPE_FRAME_STATE) \
    _(XrFrameBeginInfo, XR_TYPE_FRAME_BEGIN_INFO) \
    _(XrFrameEndInfo, XR_TYPE_FRAME_END_INFO) \
    _(XrViewLocateInfo, XR_TYPE_VIEW_LOCATE_INFO) \
    _(XrViewState, XR_TYPE_VIEW_STATE) \
    _(XrView, XR_TYPE_VIEW) \
    _(XrActionSetCreateInfo, XR_TYPE_ACTION_SET_CREATE_INFO) \
    _(XrActionCreateInfo, XR_TYPE_ACTION_CREATE_INFO) \
    _(XrInteractionProfileSuggestedBinding, XR_TYPE_INTERACTION_PROFILE_SUGGESTED_BINDING) \
    _(XrSessionActionSetsAttachInfo, XR_TYPE_SESSION_ACTION_SETS_ATTACH_INFO) \
    _(XrInteractionProfileState, XR_TYPE_INTERACTION_PROFILE_STATE) \
    _(XrActionStateGetInfo, XR_TYPE_ACTION_STATE_GET_INFO) \
    _(XrActionStateBoolean, XR_TYPE_ACTION_STATE_BOOLEAN) \
    _(XrActionStateFloat, XR_TYPE_ACTION_STATE_FLOAT) \
    _(XrActionStateVector2f, XR_TYPE_ACTION_STATE_VECTOR2F) \
    _(XrActionStatePose, XR_TYPE_ACTION_STATE_POSE) \
    _(XrActionsSyncInfo, XR_TYPE_ACTIONS_SYNC_INFO) \
    _(XrBoundSourcesForActionEnumerateInfo, XR_TYPE_BOUND_SOURCES_FOR_ACTION_ENUMERATE_INFO) \
    _(XrInputSourceLocalizedNameGetInfo, XR_TYPE_INPUT_SOURCE_LOCALIZED_NAME_GET_INFO) \
    _(XrHapticActionInfo, XR_TYPE_HAPTIC_ACTION_INFO) \
    _(XrCompositionLayerProjectionView, XR_TYPE_COMPOSITION_LAYER_PROJECTION_VIEW) \
    _(XrCompositionLayerProjection, XR_TYPE_COMPOSITION_LAYER_PROJECTION) \
    _(XrCompositionLayerQuad, XR_TYPE_COMPOSITION_LAYER_QUAD) \
    _(XrEventDataEventsLost, XR_TYPE_EVENT_DATA_EVENTS_LOST) \
    _(XrEventDataInstanceLossPending, XR_TYPE_EVENT_DATA_INSTANCE_LOSS_PENDING) \
    _(XrEventDataSessionStateChanged, XR_TYPE_EVENT_DATA_SESSION_STATE_CHANGED) \
    _(XrEventDataReferenceSpaceChangePending, XR_TYPE_EVENT_DATA_REFERENCE_SPACE_CHANGE_PENDING) \
    _(XrEventDataInteractionProfileChanged, XR_TYPE_EVENT_DATA_INTERACTION_PROFILE_CHANGED) \
    _(XrHapticVibration, XR_TYPE_HAPTIC_VIBRATION) \
    _(XrCompositionLayerCubeKHR, XR_TYPE_COMPOSITION_LAYER_CUBE_KHR) \
    _(XrCompositionLayerDepthInfoKHR, XR_TYPE_COMPOSITION_LAYER_DEPTH_INFO_KHR) \
    _(XrCompositionLayerCylinderKHR, XR_TYPE_COMPOSITION_LAYER_CYLINDER_KHR) \
    _(XrCompositionLayerEquirectKHR, XR_TYPE_COMPOSITION_LAYER_EQUIRECT_KHR) \
    _(XrVisibilityMaskKHR, XR_TYPE_VISIBILITY_MASK_KHR) \
    _(XrEventDataVisibilityMaskChangedKHR, XR_TYPE_EVENT_DATA_VISIBILITY_MASK_CHANGED_KHR) \
    _(XrCompositionLayerColorScaleBiasKHR, XR_TYPE_COMPOSITION_LAYER_COLOR_SCALE_BIAS_KHR) \
    _(XrCompositionLayerEquirect2KHR, XR_TYPE_COMPOSITION_LAYER_EQUIRECT2_KHR) \
    _(XrBindingModificationsKHR, XR_TYPE_BINDING_MODIFICATIONS_KHR) \
    _(XrEventDataPerfSettingsEXT, XR_TYPE_EVENT_DATA_PERF_SETTINGS_EXT) \
    _(XrDebugUtilsObjectNameInfoEXT, XR_TYPE_DEBUG_UTILS_OBJECT_NAME_INFO_EXT) \
    _(XrDebugUtilsLabelEXT, XR_TYPE_DEBUG_UTILS_LABEL_EXT) \
    _(XrDebugUtilsMessengerCallbackDataEXT, XR_TYPE_DEBUG_UTILS_MESSENGER_CALLBACK_DATA_EXT) \
    _(XrDebugUtilsMessengerCreateInfoEXT, XR_TYPE_DEBUG_UTILS_MESSENGER_CREATE_INFO_EXT) \
    _(XrSystemEyeGazeInteractionPropertiesEXT, XR_TYPE_SYSTEM_EYE_GAZE_INTERACTION_PROPERTIES_EXT) \
    _(XrEyeGazeSampleTimeEXT, XR_TYPE_EYE_GAZE_SAMPLE_TIME_EXT) \
    _(XrSessionCreateInfoOverlayEXTX, XR_TYPE_SESSION_CREATE_INFO_OVERLAY_EXTX) \
    _(XrEventDataMainSessionVisibilityChangedEXTX, XR_TYPE_EVENT_DATA_MAIN_SESSION_VISIBILITY_CHANGED_EXTX) \
    _(XrSpatialAnchorCreateInfoMSFT, XR_TYPE_SPATIAL_ANCHOR_CREATE_INFO_MSFT) \
    _(XrSpatialAnchorSpaceCreateInfoMSFT, XR_TYPE_SPATIAL_ANCHOR_SPACE_CREATE_INFO_MSFT) \
    _(XrCompositionLayerImageLayoutFB, XR_TYPE_COMPOSITION_LAYER_IMAGE_LAYOUT_FB) \
    _(XrCompositionLayerAlphaBlendFB, XR_TYPE_COMPOSITION_LAYER_ALPHA_BLEND_FB) \
    _(XrViewConfigurationDepthRangeEXT, XR_TYPE_VIEW_CONFIGURATION_DEPTH_RANGE_EXT) \
    _(XrSpatialGraphNodeSpaceCreateInfoMSFT, XR_TYPE_SPATIAL_GRAPH_NODE_SPACE_CREATE_INFO_MSFT) \
    _(XrSpatialGraphStaticNodeBindingCreateInfoMSFT, XR_TYPE_SPATIAL_GRAPH_STATIC_NODE_BINDING_CREATE_INFO_MSFT) \
    _(XrSpatialGraphNodeBindingPropertiesGetInfoMSFT, XR_TYPE_SPATIAL_GRAPH_NODE_BINDING_PROPERTIES_GET_INFO_MSFT) \
    _(XrSpatialGraphNodeBindingPropertiesMSFT, XR_TYPE_SPATIAL_GRAPH_NODE_BINDING_PROPERTIES_MSFT) \
    _(XrSystemHandTrackingPropertiesEXT, XR_TYPE_SYSTEM_HAND_TRACKING_PROPERTIES_EXT) \
    _(XrHandTrackerCreateInfoEXT, XR_TYPE_HAND_TRACKER_CREATE_INFO_EXT) \
    _(XrHandJointsLocateInfoEXT, XR_TYPE_HAND_JOINTS_LOCATE_INFO_EXT) \
    _(XrHandJointLocationsEXT, XR_TYPE_HAND_JOINT_LOCATIONS_EXT) \
    _(XrHandJointVelocitiesEXT, XR_TYPE_HAND_JOINT_VELOCITIES_EXT) \
    _(XrSystemHandTrackingMeshPropertiesMSFT, XR_TYPE_SYSTEM_HAND_TRACKING_MESH_PROPERTIES_MSFT) \
    _(XrHandMeshSpaceCreateInfoMSFT, XR_TYPE_HAND_MESH_SPACE_CREATE_INFO_MSFT) \
    _(XrHandMeshUpdateInfoMSFT, XR_TYPE_HAND_MESH_UPDATE_INFO_MSFT) \
    _(XrHandMeshMSFT, XR_TYPE_HAND_MESH_MSFT) \
    _(XrHandPoseTypeInfoMSFT, XR_TYPE_HAND_POSE_TYPE_INFO_MSFT) \
    _(XrSecondaryViewConfigurationSessionBeginInfoMSFT, XR_TYPE_SECONDARY_VIEW_CONFIGURATION_SESSION_BEGIN_INFO_MSFT) \
    _(XrSecondaryViewConfigurationStateMSFT, XR_TYPE_SECONDARY_VIEW_CONFIGURATION_STATE_MSFT) \
    _(XrSecondaryViewConfigurationFrameStateMSFT, XR_TYPE_SECONDARY_VIEW_CONFIGURATION_FRAME_STATE_MSFT) \
    _(XrSecondaryViewConfigurationLayerInfoMSFT, XR_TYPE_SECONDARY_VIEW_CONFIGURATION_LAYER_INFO_MSFT) \
    _(XrSecondaryViewConfigurationFrameEndInfoMSFT, XR_TYPE_SECONDARY_VIEW_CONFIGURATION_FRAME_END_INFO_MSFT) \
    _(XrSecondaryViewConfigurationSwapchainCreateInfoMSFT, XR_TYPE_SECONDARY_VIEW_CONFIGURATION_SWAPCHAIN_CREATE_INFO_MSFT) \
    _(XrControllerModelKeyStateMSFT, XR_TYPE_CONTROLLER_MODEL_KEY_STATE_MSFT) \
    _(XrControllerModelNodePropertiesMSFT, XR_TYPE_CONTROLLER_MODEL_NODE_PROPERTIES_MSFT) \
    _(XrControllerModelPropertiesMSFT, XR_TYPE_CONTROLLER_MODEL_PROPERTIES_MSFT) \
    _(XrControllerModelNodeStateMSFT, XR_TYPE_CONTROLLER_MODEL_NODE_STATE_MSFT) \
    _(XrControllerModelStateMSFT, XR_TYPE_CONTROLLER_MODEL_STATE_MSFT) \
    _(XrViewConfigurationViewFovEPIC, XR_TYPE_VIEW_CONFIGURATION_VIEW_FOV_EPIC) \
    _(XrCompositionLayerReprojectionInfoMSFT, XR_TYPE_COMPOSITION_LAYER_REPROJECTION_INFO_MSFT) \
    _(XrCompositionLayerReprojectionPlaneOverrideMSFT, XR_TYPE_COMPOSITION_LAYER_REPROJECTION_PLANE_OVERRIDE_MSFT) \
    _(XrCompositionLayerSecureContentFB, XR_TYPE_COMPOSITION_LAYER_SECURE_CONTENT_FB) \
    _(XrInteractionProfileDpadBindingEXT, XR_TYPE_INTERACTION_PROFILE_DPAD_BINDING_EXT) \
    _(XrInteractionProfileAnalogThresholdVALVE, XR_TYPE_INTERACTION_PROFILE_ANALOG_THRESHOLD_VALVE) \
    _(XrHandJointsMotionRangeInfoEXT, XR_TYPE_HAND_JOINTS_MOTION_RANGE_INFO_EXT) \
    _(XrSceneObserverCreateInfoMSFT, XR_TYPE_SCENE_OBSERVER_CREATE_INFO_MSFT) \
    _(XrSceneCreateInfoMSFT, XR_TYPE_SCENE_CREATE_INFO_MSFT) \
    _(XrNewSceneComputeInfoMSFT, XR_TYPE_NEW_SCENE_COMPUTE_INFO_MSFT) \
    _(XrVisualMeshComputeLodInfoMSFT, XR_TYPE_VISUAL_MESH_COMPUTE_LOD_INFO_MSFT) \
    _(XrSceneComponentsMSFT, XR_TYPE_SCENE_COMPONENTS_MSFT) \
    _(XrSceneComponentsGetInfoMSFT, XR_TYPE_SCENE_COMPONENTS_GET_INFO_MSFT) \
    _(XrSceneComponentLocationsMSFT, XR_TYPE_SCENE_COMPONENT_LOCATIONS_MSFT) \
    _(XrSceneComponentsLocateInfoMSFT, XR_TYPE_SCENE_COMPONENTS_LOCATE_INFO_MSFT) \
    _(XrSceneObjectsMSFT, XR_TYPE_SCENE_OBJECTS_MSFT) \
    _(XrSceneComponentParentFilterInfoMSFT, XR_TYPE_SCENE_COMPONENT_PARENT_FILTER_INFO_MSFT) \
    _(XrSceneObjectTypesFilterInfoMSFT, XR_TYPE_SCENE_OBJECT_TYPES_FILTER_INFO_MSFT) \
    _(XrScenePlanesMSFT, XR_TYPE_SCENE_PLANES_MSFT) \
    _(XrScenePlaneAlignmentFilterInfoMSFT, XR_TYPE_SCENE_PLANE_ALIGNMENT_FILTER_INFO_MSFT) \
    _(XrSceneMeshesMSFT, XR_TYPE_SCENE_MESHES_MSFT) \
    _(XrSceneMeshBuffersGetInfoMSFT, XR_TYPE_SCENE_MESH_BUFFERS_GET_INFO_MSFT) \
    _(XrSceneMeshBuffersMSFT, XR_TYPE_SCENE_MESH_BUFFERS_MSFT) \
    _(XrSceneMeshVertexBufferMSFT, XR_TYPE_SCENE_MESH_VERTEX_BUFFER_MSFT) \
    _(XrSceneMeshIndicesUint32MSFT, XR_TYPE_SCENE_MESH_INDICES_UINT32_MSFT) \
    _(XrSceneMeshIndicesUint16MSFT, XR_TYPE_SCENE_MESH_INDICES_UINT16_MSFT) \
    _(XrSerializedSceneFragmentDataGetInfoMSFT, XR_TYPE_SERIALIZED_SCENE_FRAGMENT_DATA_GET_INFO_MSFT) \
    _(XrSceneDeserializeInfoMSFT, XR_TYPE_SCENE_DESERIALIZE_INFO_MSFT) \
    _(XrEventDataDisplayRefreshRateChangedFB, XR_TYPE_EVENT_DATA_DISPLAY_REFRESH_RATE_CHANGED_FB) \
    _(XrViveTrackerPathsHTCX, XR_TYPE_VIVE_TRACKER_PATHS_HTCX) \
    _(XrEventDataViveTrackerConnectedHTCX, XR_TYPE_EVENT_DATA_VIVE_TRACKER_CONNECTED_HTCX) \
    _(XrSystemFacialTrackingPropertiesHTC, XR_TYPE_SYSTEM_FACIAL_TRACKING_PROPERTIES_HTC) \
    _(XrFacialExpressionsHTC, XR_TYPE_FACIAL_EXPRESSIONS_HTC) \
    _(XrFacialTrackerCreateInfoHTC, XR_TYPE_FACIAL_TRACKER_CREATE_INFO_HTC) \
    _(XrSystemColorSpacePropertiesFB, XR_TYPE_SYSTEM_COLOR_SPACE_PROPERTIES_FB) \
    _(XrHandTrackingMeshFB, XR_TYPE_HAND_TRACKING_MESH_FB) \
    _(XrHandTrackingScaleFB, XR_TYPE_HAND_TRACKING_SCALE_FB) \
    _(XrHandTrackingAimStateFB, XR_TYPE_HAND_TRACKING_AIM_STATE_FB) \
    _(XrHandTrackingCapsulesStateFB, XR_TYPE_HAND_TRACKING_CAPSULES_STATE_FB) \
    _(XrSystemSpatialEntityPropertiesFB, XR_TYPE_SYSTEM_SPATIAL_ENTITY_PROPERTIES_FB) \
    _(XrSpatialAnchorCreateInfoFB, XR_TYPE_SPATIAL_ANCHOR_CREATE_INFO_FB) \
    _(XrSpaceComponentStatusSetInfoFB, XR_TYPE_SPACE_COMPONENT_STATUS_SET_INFO_FB) \
    _(XrSpaceComponentStatusFB, XR_TYPE_SPACE_COMPONENT_STATUS_FB) \
    _(XrEventDataSpatialAnchorCreateCompleteFB, XR_TYPE_EVENT_DATA_SPATIAL_ANCHOR_CREATE_COMPLETE_FB) \
    _(XrEventDataSpaceSetStatusCompleteFB, XR_TYPE_EVENT_DATA_SPACE_SET_STATUS_COMPLETE_FB) \
    _(XrFoveationProfileCreateInfoFB, XR_TYPE_FOVEATION_PROFILE_CREATE_INFO_FB) \
    _(XrSwapchainCreateInfoFoveationFB, XR_TYPE_SWAPCHAIN_CREATE_INFO_FOVEATION_FB) \
    _(XrSwapchainStateFoveationFB, XR_TYPE_SWAPCHAIN_STATE_FOVEATION_FB) \
    _(XrFoveationLevelProfileCreateInfoFB, XR_TYPE_FOVEATION_LEVEL_PROFILE_CREATE_INFO_FB) \
    _(XrSystemKeyboardTrackingPropertiesFB, XR_TYPE_SYSTEM_KEYBOARD_TRACKING_PROPERTIES_FB) \
    _(XrKeyboardSpaceCreateInfoFB, XR_TYPE_KEYBOARD_SPACE_CREATE_INFO_FB) \
    _(XrKeyboardTrackingQueryFB, XR_TYPE_KEYBOARD_TRACKING_QUERY_FB) \
    _(XrTriangleMeshCreateInfoFB, XR_TYPE_TRIANGLE_MESH_CREATE_INFO_FB) \
    _(XrSystemPassthroughPropertiesFB, XR_TYPE_SYSTEM_PASSTHROUGH_PROPERTIES_FB) \
    _(XrPassthroughCreateInfoFB, XR_TYPE_PASSTHROUGH_CREATE_INFO_FB) \
    _(XrPassthroughLayerCreateInfoFB, XR_TYPE_PASSTHROUGH_LAYER_CREATE_INFO_FB) \
    _(XrCompositionLayerPassthroughFB, XR_TYPE_COMPOSITION_LAYER_PASSTHROUGH_FB) \
    _(XrGeometryInstanceCreateInfoFB, XR_TYPE_GEOMETRY_INSTANCE_CREATE_INFO_FB) \
    _(XrGeometryInstanceTransformFB, XR_TYPE_GEOMETRY_INSTANCE_TRANSFORM_FB) \
    _(XrPassthroughStyleFB, XR_TYPE_PASSTHROUGH_STYLE_FB) \
    _(XrPassthroughColorMapMonoToRgbaFB, XR_TYPE_PASSTHROUGH_COLOR_MAP_MONO_TO_RGBA_FB) \
    _(XrPassthroughColorMapMonoToMonoFB, XR_TYPE_PASSTHROUGH_COLOR_MAP_MONO_TO_MONO_FB) \
    _(XrPassthroughBrightnessContrastSaturationFB, XR_TYPE_PASSTHROUGH_BRIGHTNESS_CONTRAST_SATURATION_FB) \
    _(XrEventDataPassthroughStateChangedFB, XR_TYPE_EVENT_DATA_PASSTHROUGH_STATE_CHANGED_FB) \
    _(XrRenderModelPathInfoFB, XR_TYPE_RENDER_MODEL_PATH_INFO_FB) \
    _(XrRenderModelPropertiesFB, XR_TYPE_RENDER_MODEL_PROPERTIES_FB) \
    _(XrRenderModelBufferFB, XR_TYPE_RENDER_MODEL_BUFFER_FB) \
    _(XrRenderModelLoadInfoFB, XR_TYPE_RENDER_MODEL_LOAD_INFO_FB) \
    _(XrSystemRenderModelPropertiesFB, XR_TYPE_SYSTEM_RENDER_MODEL_PROPERTIES_FB) \
    _(XrRenderModelCapabilitiesRequestFB, XR_TYPE_RENDER_MODEL_CAPABILITIES_REQUEST_FB) \
    _(XrViewLocateFoveatedRenderingVARJO, XR_TYPE_VIEW_LOCATE_FOVEATED_RENDERING_VARJO) \
    _(XrFoveatedViewConfigurationViewVARJO, XR_TYPE_FOVEATED_VIEW_CONFIGURATION_VIEW_VARJO) \
    _(XrSystemFoveatedRenderingPropertiesVARJO, XR_TYPE_SYSTEM_FOVEATED_RENDERING_PROPERTIES_VARJO) \
    _(XrCompositionLayerDepthTestVARJO, XR_TYPE_COMPOSITION_LAYER_DEPTH_TEST_VARJO) \
    _(XrSystemMarkerTrackingPropertiesVARJO, XR_TYPE_SYSTEM_MARKER_TRACKING_PROPERTIES_VARJO) \
    _(XrEventDataMarkerTrackingUpdateVARJO, XR_TYPE_EVENT_DATA_MARKER_TRACKING_UPDATE_VARJO) \
    _(XrMarkerSpaceCreateInfoVARJO, XR_TYPE_MARKER_SPACE_CREATE_INFO_VARJO) \
    _(XrSpatialAnchorPersistenceInfoMSFT, XR_TYPE_SPATIAL_ANCHOR_PERSISTENCE_INFO_MSFT) \
    _(XrSpatialAnchorFromPersistedAnchorCreateInfoMSFT, XR_TYPE_SPATIAL_ANCHOR_FROM_PERSISTED_ANCHOR_CREATE_INFO_MSFT) \
    _(XrSpaceQueryInfoFB, XR_TYPE_SPACE_QUERY_INFO_FB) \
    _(XrSpaceStorageLocationFilterInfoFB, XR_TYPE_SPACE_STORAGE_LOCATION_FILTER_INFO_FB) \
    _(XrSpaceUuidFilterInfoFB, XR_TYPE_SPACE_UUID_FILTER_INFO_FB) \
    _(XrSpaceComponentFilterInfoFB, XR_TYPE_SPACE_COMPONENT_FILTER_INFO_FB) \
    _(XrSpaceQueryResultsFB, XR_TYPE_SPACE_QUERY_RESULTS_FB) \
    _(XrEventDataSpaceQueryResultsAvailableFB, XR_TYPE_EVENT_DATA_SPACE_QUERY_RESULTS_AVAILABLE_FB) \
    _(XrEventDataSpaceQueryCompleteFB, XR_TYPE_EVENT_DATA_SPACE_QUERY_COMPLETE_FB) \
    _(XrSpaceSaveInfoFB, XR_TYPE_SPACE_SAVE_INFO_FB) \
    _(XrSpaceEraseInfoFB, XR_TYPE_SPACE_ERASE_INFO_FB) \
    _(XrEventDataSpaceSaveCompleteFB, XR_TYPE_EVENT_DATA_SPACE_SAVE_COMPLETE_FB) \
    _(XrEventDataSpaceEraseCompleteFB, XR_TYPE_EVENT_DATA_SPACE_ERASE_COMPLETE_FB) \
    _(XrCompositionLayerSpaceWarpInfoFB, XR_TYPE_COMPOSITION_LAYER_SPACE_WARP_INFO_FB) \
    _(XrSystemSpaceWarpPropertiesFB, XR_TYPE_SYSTEM_SPACE_WARP_PROPERTIES_FB) \
    _(XrDigitalLensControlALMALENCE, XR_TYPE_DIGITAL_LENS_CONTROL_ALMALENCE) \
    _(XrSpaceContainerFB, XR_TYPE_SPACE_CONTAINER_FB) \
    _(XrPassthroughKeyboardHandsIntensityFB, XR_TYPE_PASSTHROUGH_KEYBOARD_HANDS_INTENSITY_FB) \
    _(XrCompositionLayerSettingsFB, XR_TYPE_COMPOSITION_LAYER_SETTINGS_FB) \
    _(XrPerformanceMetricsStateMETA, XR_TYPE_PERFORMANCE_METRICS_STATE_META) \
    _(XrPerformanceMetricsCounterMETA, XR_TYPE_PERFORMANCE_METRICS_COUNTER_META) \




#if defined(XR_USE_GRAPHICS_API_D3D11)
#define XR_LIST_STRUCTURE_TYPES_XR_USE_GRAPHICS_API_D3D11(_) \
    _(XrGraphicsBindingD3D11KHR, XR_TYPE_GRAPHICS_BINDING_D3D11_KHR) \
    _(XrSwapchainImageD3D11KHR, XR_TYPE_SWAPCHAIN_IMAGE_D3D11_KHR) \
    _(XrGraphicsRequirementsD3D11KHR, XR_TYPE_GRAPHICS_REQUIREMENTS_D3D11_KHR) \


#else
#define XR_LIST_STRUCTURE_TYPES_XR_USE_GRAPHICS_API_D3D11(_)
#endif

#if defined(XR_USE_GRAPHICS_API_D3D12)
#define XR_LIST_STRUCTURE_TYPES_XR_USE_GRAPHICS_API_D3D12(_) \
    _(XrGraphicsBindingD3D12KHR, XR_TYPE_GRAPHICS_BINDING_D3D12_KHR) \
    _(XrSwapchainImageD3D12KHR, XR_TYPE_SWAPCHAIN_IMAGE_D3D12_KHR) \
    _(XrGraphicsRequirementsD3D12KHR, XR_TYPE_GRAPHICS_REQUIREMENTS_D3D12_KHR) \


#else
#define XR_LIST_STRUCTURE_TYPES_XR_USE_GRAPHICS_API_D3D12(_)
#endif

#if defined(XR_USE_GRAPHICS_API_OPENGL)
#define XR_LIST_STRUCTURE_TYPES_XR_USE_GRAPHICS_API_OPENGL(_) \
    _(XrSwapchainImageOpenGLKHR, XR_TYPE_SWAPCHAIN_IMAGE_OPENGL_KHR) \
    _(XrGraphicsRequirementsOpenGLKHR, XR_TYPE_GRAPHICS_REQUIREMENTS_OPENGL_KHR) \


#else
#define XR_LIST_STRUCTURE_TYPES_XR_USE_GRAPHICS_API_OPENGL(_)
#endif

#if defined(XR_USE_GRAPHICS_API_OPENGL) && defined(XR_USE_PLATFORM_WAYLAND)
#define XR_LIST_STRUCTURE_TYPES_XR_USE_GRAPHICS_API_OPENGL_XR_USE_PLATFORM_WAYLAND(_) \
    _(XrGraphicsBindingOpenGLWaylandKHR, XR_TYPE_GRAPHICS_BINDING_OPENGL_WAYLAND_KHR) \


#else
#define XR_LIST_STRUCTURE_TYPES_XR_USE_GRAPHICS_API_OPENGL_XR_USE_PLATFORM_WAYLAND(_)
#endif

#if defined(XR_USE_GRAPHICS_API_OPENGL) && defined(XR_USE_PLATFORM_WIN32)
#define XR_LIST_STRUCTURE_TYPES_XR_USE_GRAPHICS_API_OPENGL_XR_USE_PLATFORM_WIN32(_) \
    _(XrGraphicsBindingOpenGLWin32KHR, XR_TYPE_GRAPHICS_BINDING_OPENGL_WIN32_KHR) \


#else
#define XR_LIST_STRUCTURE_TYPES_XR_USE_GRAPHICS_API_OPENGL_XR_USE_PLATFORM_WIN32(_)
#endif

#if defined(XR_USE_GRAPHICS_API_OPENGL) && defined(XR_USE_PLATFORM_XCB)
#define XR_LIST_STRUCTURE_TYPES_XR_USE_GRAPHICS_API_OPENGL_XR_USE_PLATFORM_XCB(_) \
    _(XrGraphicsBindingOpenGLXcbKHR, XR_TYPE_GRAPHICS_BINDING_OPENGL_XCB_KHR) \


#else
#define XR_LIST_STRUCTURE_TYPES_XR_USE_GRAPHICS_API_OPENGL_XR_USE_PLATFORM_XCB(_)
#endif

#if defined(XR_USE_GRAPHICS_API_OPENGL) && defined(XR_USE_PLATFORM_XLIB)
#define XR_LIST_STRUCTURE_TYPES_XR_USE_GRAPHICS_API_OPENGL_XR_USE_PLATFORM_XLIB(_) \
    _(XrGraphicsBindingOpenGLXlibKHR, XR_TYPE_GRAPHICS_BINDING_OPENGL_XLIB_KHR) \


#else
#define XR_LIST_STRUCTURE_TYPES_XR_USE_GRAPHICS_API_OPENGL_XR_USE_PLATFORM_XLIB(_)
#endif

#if defined(XR_USE_GRAPHICS_API_OPENGL_ES)
#define XR_LIST_STRUCTURE_TYPES_XR_USE_GRAPHICS_API_OPENGL_ES(_) \
    _(XrSwapchainImageOpenGLESKHR, XR_TYPE_SWAPCHAIN_IMAGE_OPENGL_ES_KHR) \
    _(XrGraphicsRequirementsOpenGLESKHR, XR_TYPE_GRAPHICS_REQUIREMENTS_OPENGL_ES_KHR) \
    _(XrSwapchainStateSamplerOpenGLESFB, XR_TYPE_SWAPCHAIN_STATE_SAMPLER_OPENGL_ES_FB) \


#else
#define XR_LIST_STRUCTURE_TYPES_XR_USE_GRAPHICS_API_OPENGL_ES(_)
#endif

#if defined(XR_USE_GRAPHICS_API_OPENGL_ES) && defined(XR_USE_PLATFORM_ANDROID)
#define XR_LIST_STRUCTURE_TYPES_XR_USE_GRAPHICS_API_OPENGL_ES_XR_USE_PLATFORM_ANDROID(_) \
    _(XrGraphicsBindingOpenGLESAndroidKHR, XR_TYPE_GRAPHICS_BINDING_OPENGL_ES_ANDROID_KHR) \


#else
#define XR_LIST_STRUCTURE_TYPES_XR_USE_GRAPHICS_API_OPENGL_ES_XR_USE_PLATFORM_ANDROID(_)
#endif

#if defined(XR_USE_GRAPHICS_API_VULKAN)
#define XR_LIST_STRUCTURE_TYPES_XR_USE_GRAPHICS_API_VULKAN(_) \
    _(XrVulkanSwapchainFormatListCreateInfoKHR, XR_TYPE_VULKAN_SWAPCHAIN_FORMAT_LIST_CREATE_INFO_KHR) \
    _(XrGraphicsBindingVulkanKHR, XR_TYPE_GRAPHICS_BINDING_VULKAN_KHR) \
    _(XrSwapchainImageVulkanKHR, XR_TYPE_SWAPCHAIN_IMAGE_VULKAN_KHR) \
    _(XrGraphicsRequirementsVulkanKHR, XR_TYPE_GRAPHICS_REQUIREMENTS_VULKAN_KHR) \
    _(XrVulkanInstanceCreateInfoKHR, XR_TYPE_VULKAN_INSTANCE_CREATE_INFO_KHR) \
    _(XrVulkanDeviceCreateInfoKHR, XR_TYPE_VULKAN_DEVICE_CREATE_INFO_KHR) \
    _(XrVulkanGraphicsDeviceGetInfoKHR, XR_TYPE_VULKAN_GRAPHICS_DEVICE_GET_INFO_KHR) \
    _(XrSwapchainImageFoveationVulkanFB, XR_TYPE_SWAPCHAIN_IMAGE_FOVEATION_VULKAN_FB) \
    _(XrSwapchainStateSamplerVulkanFB, XR_TYPE_SWAPCHAIN_STATE_SAMPLER_VULKAN_FB) \
    _(XrVulkanSwapchainCreateInfoMETA, XR_TYPE_VULKAN_SWAPCHAIN_CREATE_INFO_META) \


#else
#define XR_LIST_STRUCTURE_TYPES_XR_USE_GRAPHICS_API_VULKAN(_)
#endif

#if defined(XR_USE_PLATFORM_ANDROID)
#define XR_LIST_STRUCTURE_TYPES_XR_USE_PLATFORM_ANDROID(_) \
    _(XrInstanceCreateInfoAndroidKHR, XR_TYPE_INSTANCE_CREATE_INFO_ANDROID_KHR) \
    _(XrLoaderInitInfoAndroidKHR, XR_TYPE_LOADER_INIT_INFO_ANDROID_KHR) \
    _(XrAndroidSurfaceSwapchainCreateInfoFB, XR_TYPE_ANDROID_SURFACE_SWAPCHAIN_CREATE_INFO_FB) \
    _(XrSwapchainStateAndroidSurfaceDimensionsFB, XR_TYPE_SWAPCHAIN_STATE_ANDROID_SURFACE_DIMENSIONS_FB) \


#else
#define XR_LIST_STRUCTURE_TYPES_XR_USE_PLATFORM_ANDROID(_)
#endif

#if defined(XR_USE_PLATFORM_EGL)
#define XR_LIST_STRUCTURE_TYPES_XR_USE_PLATFORM_EGL(_) \
    _(XrGraphicsBindingEGLMNDX, XR_TYPE_GRAPHICS_BINDING_EGL_MNDX) \


#else
#define XR_LIST_STRUCTURE_TYPES_XR_USE_PLATFORM_EGL(_)
#endif

#if defined(XR_USE_PLATFORM_WIN32)
#define XR_LIST_STRUCTURE_TYPES_XR_USE_PLATFORM_WIN32(_) \
    _(XrHolographicWindowAttachmentMSFT, XR_TYPE_HOLOGRAPHIC_WINDOW_ATTACHMENT_MSFT) \


#else
#define XR_LIST_STRUCTURE_TYPES_XR_USE_PLATFORM_WIN32(_)
#endif

#define XR_LIST_STRUCTURE_TYPES(_) \
    XR_LIST_STRUCTURE_TYPES_CORE(_) \
    XR_LIST_STRUCTURE_TYPES_XR_USE_GRAPHICS_API_D3D11(_) \
    XR_LIST_STRUCTURE_TYPES_XR_USE_GRAPHICS_API_D3D12(_) \
    XR_LIST_STRUCTURE_TYPES_XR_USE_GRAPHICS_API_OPENGL(_) \
    XR_LIST_STRUCTURE_TYPES_XR_USE_GRAPHICS_API_OPENGL_XR_USE_PLATFORM_WAYLAND(_) \
    XR_LIST_STRUCTURE_TYPES_XR_USE_GRAPHICS_API_OPENGL_XR_USE_PLATFORM_WIN32(_) \
    XR_LIST_STRUCTURE_TYPES_XR_USE_GRAPHICS_API_OPENGL_XR_USE_PLATFORM_XCB(_) \
    XR_LIST_STRUCTURE_TYPES_XR_USE_GRAPHICS_API_OPENGL_XR_USE_PLATFORM_XLIB(_) \
    XR_LIST_STRUCTURE_TYPES_XR_USE_GRAPHICS_API_OPENGL_ES(_) \
    XR_LIST_STRUCTURE_TYPES_XR_USE_GRAPHICS_API_OPENGL_ES_XR_USE_PLATFORM_ANDROID(_) \
    XR_LIST_STRUCTURE_TYPES_XR_USE_GRAPHICS_API_VULKAN(_) \
    XR_LIST_STRUCTURE_TYPES_XR_USE_PLATFORM_ANDROID(_) \
    XR_LIST_STRUCTURE_TYPES_XR_USE_PLATFORM_EGL(_) \
    XR_LIST_STRUCTURE_TYPES_XR_USE_PLATFORM_WIN32(_) \


#define XR_LIST_EXTENSIONS(_) \
    _(XR_KHR_android_thread_settings, 4) \
    _(XR_KHR_android_surface_swapchain, 5) \
    _(XR_KHR_composition_layer_cube, 7) \
    _(XR_KHR_android_create_instance, 9) \
    _(XR_KHR_composition_layer_depth, 11) \
    _(XR_KHR_vulkan_swapchain_format_list, 15) \
    _(XR_EXT_performance_settings, 16) \
    _(XR_EXT_thermal_query, 17) \
    _(XR_KHR_composition_layer_cylinder, 18) \
    _(XR_KHR_composition_layer_equirect, 19) \
    _(XR_EXT_debug_utils, 20) \
    _(XR_KHR_opengl_enable, 24) \
    _(XR_KHR_opengl_es_enable, 25) \
    _(XR_KHR_vulkan_enable, 26) \
    _(XR_KHR_D3D11_enable, 28) \
    _(XR_KHR_D3D12_enable, 29) \
    _(XR_EXT_eye_gaze_interaction, 31) \
    _(XR_KHR_visibility_mask, 32) \
    _(XR_EXTX_overlay, 34) \
    _(XR_KHR_composition_layer_color_scale_bias, 35) \
    _(XR_KHR_win32_convert_performance_counter_time, 36) \
    _(XR_KHR_convert_timespec_time, 37) \
    _(XR_VARJO_quad_views, 38) \
    _(XR_MSFT_unbounded_reference_space, 39) \
    _(XR_MSFT_spatial_anchor, 40) \
    _(XR_FB_composition_layer_image_layout, 41) \
    _(XR_FB_composition_layer_alpha_blend, 42) \
    _(XR_MND_headless, 43) \
    _(XR_OCULUS_android_session_state_enable, 45) \
    _(XR_EXT_view_configuration_depth_range, 47) \
    _(XR_EXT_conformance_automation, 48) \
    _(XR_MNDX_egl_enable, 49) \
    _(XR_MSFT_spatial_graph_bridge, 50) \
    _(XR_MSFT_hand_interaction, 51) \
    _(XR_EXT_hand_tracking, 52) \
    _(XR_MSFT_hand_tracking_mesh, 53) \
    _(XR_MSFT_secondary_view_configuration, 54) \
    _(XR_MSFT_first_person_observer, 55) \
    _(XR_MSFT_controller_model, 56) \
    _(XR_MSFT_perception_anchor_interop, 57) \
    _(XR_EXT_win32_appcontainer_compatible, 58) \
    _(XR_EPIC_view_configuration_fov, 60) \
    _(XR_MSFT_holographic_window_attachment, 64) \
    _(XR_MSFT_composition_layer_reprojection, 67) \
    _(XR_HUAWEI_controller_interaction, 70) \
    _(XR_FB_android_surface_swapchain_create, 71) \
    _(XR_FB_swapchain_update_state, 72) \
    _(XR_FB_composition_layer_secure_content, 73) \
    _(XR_EXT_dpad_binding, 79) \
    _(XR_VALVE_analog_threshold, 80) \
    _(XR_EXT_hand_joints_motion_range, 81) \
    _(XR_KHR_loader_init, 89) \
    _(XR_KHR_loader_init_android, 90) \
    _(XR_KHR_vulkan_enable2, 91) \
    _(XR_KHR_composition_layer_equirect2, 92) \
    _(XR_EXT_samsung_odyssey_controller, 95) \
    _(XR_EXT_hp_mixed_reality_controller, 96) \
    _(XR_MND_swapchain_usage_input_attachment_bit, 97) \
    _(XR_MSFT_scene_understanding, 98) \
    _(XR_MSFT_scene_understanding_serialization, 99) \
    _(XR_FB_display_refresh_rate, 102) \
    _(XR_HTC_vive_cosmos_controller_interaction, 103) \
    _(XR_HTCX_vive_tracker_interaction, 104) \
    _(XR_HTC_facial_tracking, 105) \
    _(XR_HTC_vive_focus3_controller_interaction, 106) \
    _(XR_HTC_hand_interaction, 107) \
    _(XR_HTC_vive_wrist_tracker_interaction, 108) \
    _(XR_FB_color_space, 109) \
    _(XR_FB_hand_tracking_mesh, 111) \
    _(XR_FB_hand_tracking_aim, 112) \
    _(XR_FB_hand_tracking_capsules, 113) \
    _(XR_FB_spatial_entity, 114) \
    _(XR_FB_foveation, 115) \
    _(XR_FB_foveation_configuration, 116) \
    _(XR_FB_keyboard_tracking, 117) \
    _(XR_FB_triangle_mesh, 118) \
    _(XR_FB_passthrough, 119) \
    _(XR_FB_render_model, 120) \
    _(XR_KHR_binding_modification, 121) \
    _(XR_VARJO_foveated_rendering, 122) \
    _(XR_VARJO_composition_layer_depth_test, 123) \
    _(XR_VARJO_environment_depth_estimation, 124) \
    _(XR_VARJO_marker_tracking, 125) \
    _(XR_VARJO_view_offset, 126) \
    _(XR_MSFT_spatial_anchor_persistence, 143) \
    _(XR_ULTRALEAP_hand_tracking_forearm, 150) \
    _(XR_FB_spatial_entity_query, 157) \
    _(XR_FB_spatial_entity_storage, 159) \
    _(XR_OCULUS_audio_device_guid, 160) \
    _(XR_FB_foveation_vulkan, 161) \
    _(XR_FB_swapchain_update_state_android_surface, 162) \
    _(XR_FB_swapchain_update_state_opengl_es, 163) \
    _(XR_FB_swapchain_update_state_vulkan, 164) \
    _(XR_KHR_swapchain_usage_input_attachment_bit, 166) \
    _(XR_FB_space_warp, 172) \
    _(XR_ALMALENCE_digital_lens_control, 197) \
    _(XR_FB_spatial_entity_container, 200) \
    _(XR_FB_passthrough_keyboard_hands, 204) \
    _(XR_FB_composition_layer_settings, 205) \
    _(XR_META_vulkan_swapchain_create_info, 228) \
    _(XR_META_performance_metrics, 233) \
    _(XR_EXT_uuid, 300) \


#endif

