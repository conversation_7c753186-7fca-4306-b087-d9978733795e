{"FileVersion": 3, "Version": 125, "VersionName": "1.2.5", "FriendlyName": "<PERSON><PERSON>jo OpenXR", "Description": "Varjo OpenXR plugin provides support for Varjo OpenXR extensions.", "Category": "Virtual Reality", "CreatedBy": "<PERSON><PERSON><PERSON>", "CreatedByURL": "http://www.varjo.com", "DocsURL": "https://developer.varjo.com/docs/unreal/ue5/unreal5", "MarketplaceURL": "com.epicgames.launcher://ue/marketplace/product/1804011a48a743cbbeb9336edcafe923", "SupportURL": "", "EngineVersion": "5.5.0", "CanContainContent": true, "Installed": true, "Modules": [{"Name": "VarjoOpenXR", "Type": "Runtime", "LoadingPhase": "PostConfigInit", "PlatformAllowList": ["Win64"]}, {"Name": "VarjoOpenXRRuntimeSettings", "Type": "Runtime", "LoadingPhase": "PostConfigInit", "PlatformAllowList": ["Win64"]}, {"Name": "VarjoOpenXREditor", "Type": "Editor", "LoadingPhase": "PostEngineInit", "PlatformAllowList": ["Win64"]}], "Plugins": [{"Name": "OpenXR", "Enabled": true}, {"Name": "OpenXREyeTracker", "Enabled": true}, {"Name": "OpenXRHandTracking", "Enabled": true}]}