# BP_XRHandsPawn Gesture Recognition & Teleport Setup Guide

## Overview
This guide provides step-by-step instructions for setting up gesture recognition and pinch-to-teleport functionality in BP_XRPassthroughPawn (equivalent to BP_XRHandsPawn).

## Stage 1: Gesture Recognition Events Implementation

### 1. Open BP_XRPassthroughPawn Blueprint
- Navigate to Content/Blueprints/Characters/BP_XRPassthroughPawn.uasset
- Double-click to open the blueprint editor

### 2. Event Graph Setup
In the Event Graph, add the following custom events:

#### Custom Events to Create:
1. **OnPinch_BP** - Triggered when pinch gesture detected
2. **OnGrab_BP** - Triggered when grab gesture detected  
3. **OnPoint_BP** - Triggered when pointing gesture detected

#### Event Parameters:
- **OnPinch_BP**: (float Strength, bool bIsLeftHand, FVector HandPosition)
- **OnGrab_BP**: (float Strength, bool bIsLeftHand, FVector HandPosition)
- **OnPoint_BP**: (float Confidence, bool bIsLeftHand, FVector HandPosition)

### 3. Gesture Detection Logic
Add these nodes to your Event Graph:

#### Pinch Detection:
```
[OnPinch_BP Event]
→ [Branch] (Strength >= 0.8)
   → [Print String] "Pinch Detected!"
   → [Teleport Logic]
```

#### Grab Detection:
```
[OnGrab_BP Event]
→ [Branch] (Strength >= 0.7)
   → [Print String] "Grab Detected!"
```

#### Point Detection:
```
[OnPoint_BP Event]
→ [Branch] (Confidence >= 0.6)
   → [Print String] "Point Detected!"
```

### 4. Teleport Implementation
Create the following variables in your blueprint:

#### Variables:
- **bIsTeleportAiming** (Boolean) - Tracks if teleport is active
- **PinchStartLocation** (Vector) - Starting position of pinch
- **TeleportTargetLocation** (Vector) - Calculated teleport destination
- **TeleportDistanceMultiplier** (Float) - Default: 2.0

#### Teleport Logic Flow:
```
[OnPinch_BP Event]
→ [Branch] (Strength >= 0.8)
   → [Set bIsTeleportAiming = true]
   → [Set PinchStartLocation = HandPosition]
   → [Show Teleport Indicator]

[OnPinch_BP Event] (Continuous)
→ [Branch] (bIsTeleportAiming == true)
   → [Calculate TeleportTargetLocation]
   → [Update Teleport Indicator Position]

[OnPinch_BP Event] (Release)
→ [Branch] (Strength < 0.3)
   → [Branch] (bIsTeleportAiming == true)
      → [Teleport Actor to TeleportTargetLocation]
      → [Set bIsTeleportAiming = false]
```

### 5. Debug Visualization
Add these debug features:

#### Debug Lines:
- Draw debug line from pinch start to target location
- Draw debug sphere at teleport target position

#### Debug Messages:
- "TELEPORT AIMING..." (Purple)
- "TELEPORTED TO: [Location]" (Green)
- "TELEPORT CANCELLED" (Red)

### 6. Testing Checklist
- [ ] Pinch gesture triggers debug message
- [ ] Grab gesture triggers debug message
- [ ] Point gesture triggers debug message
- [ ] Pinch-and-hold shows teleport indicator
- [ ] Release pinch completes teleport
- [ ] All debug messages appear correctly

## Blueprint Node Setup Example

### Event Graph Structure:
```
BeginPlay
→ [Initialize Hand Tracking]
→ [Bind Gesture Events]

OnPinch_BP(Strength, bIsLeftHand, HandPosition)
→ [Branch] Strength >= 0.8
   → [Set bIsPinching = true]
   → [Set PinchStartLocation = HandPosition]
   → [Set bIsTeleportAiming = true]
   → [Print String] "TELEPORT AIMING..."

OnPinch_BP(Strength < 0.3)
→ [Branch] bIsPinching == true
   → [Branch] bIsTeleportAiming == true
      → [Teleport Actor to Calculated Location]
      → [Print String] "TELEPORTED!"
   → [Set bIsPinching = false]
   → [Set bIsTeleportAiming = false]
```

## Integration Notes
- The C++ backend handles gesture detection thresholds
- Blueprint events are triggered automatically
- Teleport distance scales with hand movement from pinch start
- All debug features can be disabled in production