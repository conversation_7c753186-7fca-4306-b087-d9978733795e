{"Type": "Blueprint", "Name": "BP_XRPassthroughPawn", "ParentClass": "XRPassthroughPawn", "Category": "Characters", "Components": [{"Name": "HandTrackingComponent", "Type": "ULHandTrackingComponent", "Properties": {"bHandTrackingEnabled": true, "TrackingMode": "BothHands"}}, {"Name": "VarjoPassthroughComponent", "Type": "UVarjoPassthroughComponent", "Properties": {"bPassthroughEnabled": true, "PassthroughOpacity": 1.0}}, {"Name": "GestureProcessorComponent", "Type": "UGestureProcessorComponent", "Properties": {"bGestureRecognitionEnabled": true, "ConfidenceThreshold": 0.8}}], "InputConfiguration": {"DefaultMappingContext": "/Game/Input/IMC_XRGestureMapping.IMC_XRGestureMapping", "GestureTriggerAction": "/Game/Input/Actions/IA_GestureTrigger.IA_GestureTrigger"}, "BlueprintEvents": {"OnGestureRecognized_BP": {"Description": "Called when a gesture is recognized", "Parameters": [{"Name": "GestureName", "Type": "String"}, {"Name": "Confidence", "Type": "Float"}]}}}