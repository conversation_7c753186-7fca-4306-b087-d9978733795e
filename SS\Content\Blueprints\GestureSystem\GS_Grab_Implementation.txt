# GS_Grab Blueprint Implementation - Ready for UE5.5

## Blueprint Type: Actor Component
## Parent Class: ActorComponent
## Location: Content/Blueprints/GestureSystem/GS_Grab.uasset

## Variables Configuration
```
// Grab State Management
bIsGrabbing (Boolean) = false
  Category: "Grab|State"
  Tooltip: "True when actively grabbing an object"

bCanGrab (Boolean) = true
  Category: "Grab|State"
  Tooltip: "Master switch for grab functionality"

GrabbedObject (Actor | Object Reference) = None
  Category: "Grab|State"
  Tooltip: "Currently grabbed object reference"

GrabbingHand (Boolean) = false
  Category: "Grab|State"
  Tooltip: "True if left hand, false if right hand"

// Grab Configuration
GrabThreshold (Float) = 0.7 [Range: 0.1, 1.0]
  Category: "Grab|Configuration"
  Tooltip: "Minimum grab strength required to trigger grab"

ReleaseThreshold (Float) = 0.4 [Range: 0.1, 1.0]
  Category: "Grab|Configuration"
  Tooltip: "Maximum grab strength to trigger release"

GrabRange (Float) = 15.0 [Range: 5.0, 50.0]
  Category: "Grab|Configuration"
  Tooltip: "Maximum distance to detect grabbable objects (cm)"

SmoothingFactor (Float) = 0.8 [Range: 0.1, 1.0]
  Category: "Grab|Configuration"
  Tooltip: "Smoothing factor for object following (higher = smoother)"

// References
GestureManager (BP_GestureManager | Object Reference) = None
  Category: "Grab|References"
  Tooltip: "Reference to gesture manager component"

// Debug
bShowDebugInfo (Boolean) = false
  Category: "Grab|Debug"
  Tooltip: "Show debug information and visualizations"
```

## Custom Events
```
OnGrabAttempt (Custom Event)
  Inputs: TargetObject (Actor), HandPosition (Vector), bIsLeftHand (Boolean)
  Description: "Fired when grab attempt is made"

OnGrabSuccess (Custom Event)
  Inputs: GrabbedObject (Actor), HandPosition (Vector), bIsLeftHand (Boolean)
  Description: "Fired when object is successfully grabbed"

OnGrabRelease (Custom Event)
  Inputs: ReleasedObject (Actor), ReleaseVelocity (Vector), bIsLeftHand (Boolean)
  Description: "Fired when object is released"

OnGrabFailed (Custom Event)
  Inputs: Reason (String), AttemptedPosition (Vector)
  Description: "Fired when grab attempt fails"
```

## Event Graph Implementation

### BeginPlay Event Chain
```
[Event BeginPlay]
    ↓
[Delay] (0.1)
    ↓
[Get Owner] → [Get Component by Class] (BP_GestureManager) → [Set GestureManager]
    ↓
[IsValid] (GestureManager)
    ↓ True
    [Call Function] (GestureManager.RegisterWithManager)
      Input: Self Reference
    ↓
    [Bind Gesture Events] (Custom Function)
    ↓
    [Initialize Grab System] (Custom Function)
    ↓
    [Print String] ("GS_Grab Initialized" | Green | 2.0)
    
    False ↓
    [Print String] ("ERROR: No Gesture Manager Found" | Red | 5.0)
```

### Grab Detection Handler
```
[OnGrabGestureDetected] (Event Dispatcher Bound)
  Inputs: GestureType (String), Strength (Float), bIsLeftHand (Boolean), Position (Vector)
    ↓
[Branch] (GestureType == "Grab")
    ↓ True
    [Branch] (Strength >= GrabThreshold)
        ↓ True
        [Branch] (!bIsGrabbing AND bCanGrab)
            ↓ True
            [Find Closest Grabbable Object] (Custom Function)
              Input: Position
              Output: ClosestObject (Actor), Distance (Float)
            ↓
            [Branch] (IsValid ClosestObject AND Distance <= GrabRange)
                ↓ True
                [OnGrabAttempt] (Call Event)
                  TargetObject: ClosestObject
                  HandPosition: Position
                  bIsLeftHand: bIsLeftHand
                ↓
                [Execute Grab] (Custom Function)
                  Input: ClosestObject, Position, bIsLeftHand
                ↓
                [Branch] (bShowDebugInfo)
                    ↓ True
                    [Print String] ("Object Grabbed: " + ClosestObject.GetName() | Yellow | 1.0)
                
                False ↓
                [OnGrabFailed] (Call Event)
                  Reason: "No grabbable object in range"
                  AttemptedPosition: Position
```

### Grab Release Handler
```
[OnGrabGestureProgression] (Event Dispatcher Bound)
  Inputs: GestureType (String), Strength (Float), bIsLeftHand (Boolean), Position (Vector)
    ↓
[Branch] (GestureType == "Grab" AND bIsGrabbing)
    ↓ True
    [Branch] (Strength <= ReleaseThreshold)
        ↓ True
        [Calculate Release Velocity] (Custom Function)
          Output: ReleaseVelocity (Vector)
        ↓
        [Execute Release] (Custom Function)
          Input: ReleaseVelocity
        ↓
        [OnGrabRelease] (Call Event)
          ReleasedObject: GrabbedObject
          ReleaseVelocity: ReleaseVelocity
          bIsLeftHand: GrabbingHand
        ↓
        [Reset Grab State] (Custom Function)
        ↓
        [Branch] (bShowDebugInfo)
            ↓ True
            [Print String] ("Object Released" | Blue | 1.0)
    
    False ↓
    [Update Object Position] (Custom Function)
      Input: Position
```

## Custom Functions

### Find Closest Grabbable Object
```
Function: Find Closest Grabbable Object
Inputs: SearchPosition (Vector)
Outputs: ClosestObject (Actor), Distance (Float)

[Sphere Overlap Actors] 
  World Position: SearchPosition
  Radius: GrabRange
  Object Types: WorldStatic, WorldDynamic
  Actors to Ignore: Owner
  Output: OverlappingActors (Array)
    ↓
[For Each Loop] (OverlappingActors)
    ↓
    [Actor Has Tag] (Current Actor, "Grabbable")
        ↓ True
        [Vector Distance] (SearchPosition, Current Actor Location)
        ↓
        [Branch] (Distance < ClosestDistance OR ClosestObject == None)
            ↓ True
            [Set ClosestObject] = Current Actor
            [Set ClosestDistance] = Distance
    ↓
[Return] ClosestObject, ClosestDistance
```

### Execute Grab
```
Function: Execute Grab
Inputs: TargetObject (Actor), HandPosition (Vector), bIsLeftHand (Boolean)

[Set bIsGrabbing] = true
    ↓
[Set GrabbedObject] = TargetObject
    ↓
[Set GrabbingHand] = bIsLeftHand
    ↓
[Get Component by Class] (TargetObject, StaticMeshComponent)
    ↓
[Branch] (IsValid StaticMeshComponent)
    ↓ True
    [Set Simulate Physics] (StaticMeshComponent, false)
    ↓
    [Set Collision Enabled] (StaticMeshComponent, QueryOnly)
    ↓
[OnGrabSuccess] (Call Event)
  GrabbedObject: TargetObject
  HandPosition: HandPosition
  bIsLeftHand: bIsLeftHand
```

### Update Object Position
```
Function: Update Object Position
Inputs: NewHandPosition (Vector)

[Branch] (IsValid GrabbedObject)
    ↓ True
    [Get Actor Location] (GrabbedObject)
    ↓
    [Vector Lerp] 
      A: Current Location
      B: NewHandPosition
      Alpha: SmoothingFactor
      Output: SmoothedPosition
    ↓
    [Set Actor Location] (GrabbedObject, SmoothedPosition)
```

### Execute Release
```
Function: Execute Release
Inputs: ReleaseVelocity (Vector)

[Branch] (IsValid GrabbedObject)
    ↓ True
    [Get Component by Class] (GrabbedObject, StaticMeshComponent)
    ↓
    [Branch] (IsValid StaticMeshComponent)
        ↓ True
        [Set Simulate Physics] (StaticMeshComponent, true)
        ↓
        [Set Collision Enabled] (StaticMeshComponent, QueryAndPhysics)
        ↓
        [Add Impulse] (StaticMeshComponent, ReleaseVelocity * 100.0)
```

### Reset Grab State
```
Function: Reset Grab State

[Set bIsGrabbing] = false
    ↓
[Set GrabbedObject] = None
    ↓
[Set GrabbingHand] = false
```

## Integration Notes

### Add to VR Pawn
```
1. In VR Pawn Blueprint (BP_XRPassthroughPawn):
   - Add Component → GS_Grab
   - Ensure BP_GestureManager is also added
   - Compile and test
```

### Object Setup
```
For objects to be grabbable:
1. Add tag "Grabbable" to the actor
2. Ensure object has StaticMeshComponent or similar
3. Set up appropriate collision settings
4. Test grab detection range
```

This implementation provides complete grab functionality with physics support and smooth object manipulation.
