# Hand Tracking Occlusion Setup Guide

## Overview
This guide provides step-by-step instructions to enable proper hand tracking with occlusion in the IlPalazzo project.

## Prerequisites
- Project successfully built
- Ultraleap device connected
- VR headset connected (Varjo/OpenXR compatible)

## Step 1: Enable Required Plugins
✅ **XRBase plugin** - Added to project dependencies
✅ **Ultraleap Tracking** - Already configured
✅ **OpenXR Hand Tracking** - Enabled in project settings

## Step 2: Configure Project Settings

### In Unreal Editor:
1. **Edit → Project Settings**
2. **Plugins → Ultraleap Tracking**
   - Enable "Auto Enable Leap"
   - Set Tracking Mode to "Desktop" or "HMD" as appropriate
   - Enable "Use OpenXR Passthrough"

3. **Engine → Rendering**
   - Enable "Custom Depth-Stencil Pass"
   - Set "Custom Depth-Stencil Pass" to "Enabled with Stencil"

## Step 3: Set Up Hand Tracking Blueprint

### Create Hand Tracking Actor:
1. **Content Browser → Add New → Blueprint Class**
2. **Parent Class: Actor**
3. **Name: BP_HandTrackingManager**

### Add Components:
1. **LeapComponent** (from Ultraleap)
   - Enable "Show Debug Hands"
   - Set "Hand Material" to a material with proper occlusion
   - Enable "Use Custom Depth"

2. **SceneComponent** (root)
   - Set location to VR camera position

## Step 4: Configure Materials for Occlusion

### Create Occlusion Material:
1. **Content Browser → Add New → Material**
2. **Name: M_HandOcclusion**
3. **Material Properties:**
   - Blend Mode: Masked
   - Shading Model: Unlit
   - Enable "Use with Skeletal Mesh"
   - Enable "Use with Custom Depth"

### Material Graph:
```
[Texture Sample] → [Opacity Mask] → [Material Output]
Use hand texture with alpha channel for proper transparency
```

## Step 5: Set Up Collision for Occlusion

### Collision Settings:
1. **Select Hand Meshes**
2. **Collision Presets: Custom**
3. **Collision Responses:**
   - Visibility: Block
   - Camera: Block
   - WorldStatic: Block
   - WorldDynamic: Block

## Step 6: VR Pawn Setup

### Modify BP_XRPassthroughPawn:
1. **Add LeapComponent** to the pawn
2. **Set Auto Possess Player: Player 0**
3. **Configure Camera:**
   - Enable "Lock to HMD"
   - Set tracking origin to "Eye Level"

## Step 7: Test Hand Tracking

### In VR Preview:
1. **Play → VR Preview**
2. **Check Hand Visibility:**
   - Hands should appear in correct position
   - Hands should occlude virtual objects
   - Hands should respond to finger tracking

### Troubleshooting:
- **No hands visible:** Check Leap device connection
- **Hands not occluding:** Verify Custom Depth settings
- **Tracking offset:** Recalibrate tracking origin
- **Performance issues:** Reduce hand mesh complexity

## Step 8: Advanced Features

### Gesture Recognition:
- Use GestureProcessorComponent for custom gestures
- Map gestures to actions in IMC_XRGestureMapping

### Passthrough Integration:
- Enable Varjo passthrough for mixed reality
- Configure depth testing for proper layering

## Verification Checklist:
- [ ] Hands visible in VR preview
- [ ] Hands occlude virtual objects correctly
- [ ] Finger tracking working
- [ ] No tracking lag or jitter
- [ ] Proper collision with virtual objects
- [ ] Gesture recognition functional

## Common Issues and Solutions:

### Hands Not Visible:
1. Check Leap device connection
2. Verify Leap software is running
3. Ensure proper lighting conditions
4. Check plugin settings in Project Settings

### Occlusion Not Working:
1. Enable Custom Depth-Stencil Pass
2. Set proper collision channels
3. Verify material settings
4. Check render order settings

### Tracking Offset:
1. Recalibrate tracking origin
2. Check VR camera position
3. Verify pawn possession settings