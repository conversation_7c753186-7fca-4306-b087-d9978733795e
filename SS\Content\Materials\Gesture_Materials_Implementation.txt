# Gesture System Materials Implementation - Ready for UE5.5

## Material Creation Guide

### Location: Content/Materials/GestureSystem/

## M_HandState - Base Hand Material

### Material Properties
```
Material Domain: Surface
Blend Mode: Translucent
Shading Model: Unlit
Two Sided: True
```

### Material Graph
```
[Vector Parameter] "BaseColor" (Default: 0.2, 0.5, 1.0) // Blue
    ↓
[Multiply] × [Scalar Parameter] "Brightness" (Default: 1.5)
    ↓
[Emissive Color]

[Scalar Parameter] "Opacity" (Default: 0.8)
    ↓
[Opacity]

[Scalar Parameter] "EmissiveIntensity" (Default: 1.0)
    ↓
[Multiply] with Emissive Color output
```

### Parameter Setup
```
BaseColor (Vector3): Controls hand color
- Default: (0.2, 0.5, 1.0) - Blue
- Group: "Hand State"

Brightness (Scalar): Controls emissive brightness
- Default: 1.5
- Range: 0.1 - 3.0
- Group: "Hand State"

Opacity (Scalar): Controls transparency
- Default: 0.8
- Range: 0.1 - 1.0
- Group: "Hand State"

EmissiveIntensity (Scalar): Controls glow intensity
- Default: 1.0
- Range: 0.0 - 5.0
- Group: "Hand State"
```

---

## M_ObjectHighlight - Object Highlighting Material

### Material Properties
```
Material Domain: Surface
Blend Mode: Translucent
Shading Model: DefaultLit
Two Sided: False
```

### Material Graph
```
[Vector Parameter] "HighlightColor" (Default: 1.0, 1.0, 0.0) // Yellow
    ↓
[Multiply] × [Scalar Parameter] "Intensity" (Default: 2.0)
    ↓
[Emissive Color]

[Fresnel] 
  Exponent: [Scalar Parameter] "FresnelExponent" (Default: 2.0)
    ↓
[Multiply] × [Scalar Parameter] "FresnelIntensity" (Default: 1.0)
    ↓
[Opacity]

// Pulsing Animation
[Time] 
    ↓
[Multiply] × [Scalar Parameter] "PulseSpeed" (Default: 3.0)
    ↓
[Sine]
    ↓
[Multiply] × 0.3
    ↓
[Add] + 0.7
    ↓
[Multiply] with Emissive Color
```

### Parameter Setup
```
HighlightColor (Vector3): Highlight color
- Default: (1.0, 1.0, 0.0) - Yellow
- Group: "Highlight"

Intensity (Scalar): Highlight brightness
- Default: 2.0
- Range: 0.5 - 5.0
- Group: "Highlight"

FresnelExponent (Scalar): Edge detection sharpness
- Default: 2.0
- Range: 0.5 - 5.0
- Group: "Highlight"

FresnelIntensity (Scalar): Edge glow intensity
- Default: 1.0
- Range: 0.0 - 3.0
- Group: "Highlight"

PulseSpeed (Scalar): Animation speed
- Default: 3.0
- Range: 0.5 - 10.0
- Group: "Animation"
```

---

## M_TeleportArc - Teleport Arc Material

### Material Properties
```
Material Domain: Surface
Blend Mode: Translucent
Shading Model: Unlit
Two Sided: True
```

### Material Graph
```
[Vector Parameter] "ArcColor" (Default: 0.0, 1.0, 1.0) // Cyan
    ↓
[Multiply] × [Scalar Parameter] "ArcIntensity" (Default: 1.5)
    ↓
[Emissive Color]

// Flow Animation
[Texture Coordinate] UV Channel 0
    ↓
[Multiply] U × [Scalar Parameter] "FlowSpeed" (Default: 2.0)
    ↓
[Add] + [Time]
    ↓
[Frac] // Keep in 0-1 range
    ↓
[Texture Sample] (Noise Texture)
    ↓
[Multiply] × [Scalar Parameter] "FlowIntensity" (Default: 0.5)
    ↓
[Add] with base Emissive Color

[Scalar Parameter] "ArcOpacity" (Default: 0.6)
    ↓
[Multiply] × [Fresnel] (Exponent: 1.5)
    ↓
[Opacity]
```

### Parameter Setup
```
ArcColor (Vector3): Arc base color
- Default: (0.0, 1.0, 1.0) - Cyan
- Group: "Arc"

ArcIntensity (Scalar): Arc brightness
- Default: 1.5
- Range: 0.5 - 3.0
- Group: "Arc"

ArcOpacity (Scalar): Arc transparency
- Default: 0.6
- Range: 0.2 - 1.0
- Group: "Arc"

FlowSpeed (Scalar): Animation speed
- Default: 2.0
- Range: 0.5 - 5.0
- Group: "Animation"

FlowIntensity (Scalar): Flow effect strength
- Default: 0.5
- Range: 0.0 - 1.0
- Group: "Animation"
```

---

## M_TeleportTarget - Teleport Target Material

### Material Properties
```
Material Domain: Surface
Blend Mode: Translucent
Shading Model: Unlit
Two Sided: True
```

### Material Graph
```
[Vector Parameter] "TargetColor" (Default: 0.0, 1.0, 0.0) // Green
    ↓
[Multiply] × [Scalar Parameter] "TargetIntensity" (Default: 2.0)
    ↓
[Emissive Color]

// Pulsing Effect
[Time]
    ↓
[Multiply] × [Scalar Parameter] "PulseSpeed" (Default: 4.0)
    ↓
[Sine]
    ↓
[Multiply] × 0.4
    ↓
[Add] + 0.6
    ↓
[Multiply] with Emissive Color

// Radial Gradient
[Texture Coordinate] UV Channel 0
    ↓
[Subtract] - (0.5, 0.5)
    ↓
[Length] // Distance from center
    ↓
[OneMinus]
    ↓
[Power] Exponent: [Scalar Parameter] "RadialFalloff" (Default: 2.0)
    ↓
[Multiply] with final Emissive Color

[Scalar Parameter] "TargetOpacity" (Default: 0.8)
    ↓
[Opacity]
```

### Parameter Setup
```
TargetColor (Vector3): Target indicator color
- Default: (0.0, 1.0, 0.0) - Green
- Group: "Target"

TargetIntensity (Scalar): Target brightness
- Default: 2.0
- Range: 0.5 - 4.0
- Group: "Target"

TargetOpacity (Scalar): Target transparency
- Default: 0.8
- Range: 0.3 - 1.0
- Group: "Target"

PulseSpeed (Scalar): Pulse animation speed
- Default: 4.0
- Range: 1.0 - 8.0
- Group: "Animation"

RadialFalloff (Scalar): Edge softness
- Default: 2.0
- Range: 0.5 - 5.0
- Group: "Target"
```

---

## Material Variants

### M_HandState_Variants
Create material instances for different hand states:

```
MI_HandIdle:
- BaseColor: (0.2, 0.5, 1.0) - Blue
- EmissiveIntensity: 0.5

MI_HandHover:
- BaseColor: (1.0, 1.0, 0.0) - Yellow
- EmissiveIntensity: 1.0

MI_HandGrab:
- BaseColor: (0.0, 1.0, 0.0) - Green
- EmissiveIntensity: 1.5

MI_HandTeleport:
- BaseColor: (0.0, 1.0, 1.0) - Cyan
- EmissiveIntensity: 2.0
```

### M_ObjectHighlight_Variants
Create material instances for different highlight types:

```
MI_HighlightValid:
- HighlightColor: (0.0, 1.0, 0.0) - Green
- Intensity: 2.0

MI_HighlightInvalid:
- HighlightColor: (1.0, 0.0, 0.0) - Red
- Intensity: 2.5

MI_HighlightSelected:
- HighlightColor: (1.0, 1.0, 1.0) - White
- Intensity: 3.0
```

## Usage in Blueprints

### In BP_GestureVisualFeedback
```
HandMaterials Array:
[0] = MI_HandIdle
[1] = MI_HandHover
[2] = MI_HandGrab
[3] = MI_HandTeleport

HighlightMaterials Array:
[0] = MI_HighlightValid
[1] = MI_HighlightInvalid
[2] = MI_HighlightSelected
```

### In GS_Teleport
```
TeleportArcMaterial = M_TeleportArc
TeleportTargetMaterial = M_TeleportTarget
```

## Creation Steps in UE5.5

1. **Create Material**
   - Content Browser → Right-click → Material
   - Name according to convention (M_MaterialName)
   - Open material editor

2. **Build Material Graph**
   - Add parameter nodes as specified
   - Connect nodes according to graph layout
   - Set default values for parameters

3. **Create Material Instances**
   - Right-click material → Create Material Instance
   - Name with MI_ prefix
   - Override parameters as needed

4. **Assign to Components**
   - Reference materials in blueprint variables
   - Apply during runtime via Set Material nodes

This material system provides comprehensive visual feedback for all gesture interactions with proper performance optimization for VR.
