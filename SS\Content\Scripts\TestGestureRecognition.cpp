// Test script for verifying gesture recognition functionality
// This can be used to test the gesture system without the full Unreal setup

#include <iostream>
#include <string>
#include <cmath>

class GestureRecognitionTester {
public:
    struct HandData {
        float pinchStrength = 0.0f;
        float grabStrength = 0.0f;
        float pointConfidence = 0.0f;
        bool isLeftHand = false;
        float x = 0.0f, y = 0.0f, z = 0.0f;
    };

    // Gesture thresholds
    const float PINCH_THRESHOLD = 0.8f;
    const float GRAB_THRESHOLD = 0.7f;
    const float POINT_THRESHOLD = 0.6f;
    const float PINCH_RELEASE_THRESHOLD = 0.3f;

    // Teleport state
    bool isTeleportAiming = false;
    bool isPinching = false;
    float pinchStartX = 0.0f, pinchStartY = 0.0f, pinchStartZ = 0.0f;
    float teleportTargetX = 0.0f, teleportTargetY = 0.0f, teleportTargetZ = 0.0f;

    void TestGestureDetection(const HandData& hand) {
        std::cout << "\n=== Gesture Recognition Test ===" << std::endl;
        std::cout << "Hand: " << (hand.isLeftHand ? "Left" : "Right") << std::endl;
        std::cout << "Position: (" << hand.x << ", " << hand.y << ", " << hand.z << ")" << std::endl;
        std::cout << "Pinch Strength: " << hand.pinchStrength << std::endl;
        std::cout << "Grab Strength: " << hand.grabStrength << std::endl;
        std::cout << "Point Confidence: " << hand.pointConfidence << std::endl;

        // Test pinch gesture with teleport
        if (hand.pinchStrength >= PINCH_THRESHOLD) {
            if (!isPinching) {
                isPinching = true;
                isTeleportAiming = true;
                pinchStartX = hand.x;
                pinchStartY = hand.y;
                pinchStartZ = hand.z;
                std::cout << "✓ PINCH DETECTED - Teleport aiming started!" << std::endl;
                std::cout << "  Pinch start position: (" << pinchStartX << ", " << pinchStartY << ", " << pinchStartZ << ")" << std::endl;
            } else if (isTeleportAiming) {
                // Calculate teleport target
                float dx = hand.x - pinchStartX;
                float dy = hand.y - pinchStartY;
                float dz = hand.z - pinchStartZ;
                
                float distance = std::sqrt(dx*dx + dy*dy + dz*dz);
                float teleportDistance = std::min(distance * 2.0f, 2000.0f);
                
                teleportTargetX = pinchStartX + (dx * teleportDistance / std::max(distance, 1.0f));
                teleportTargetY = pinchStartY + (dy * teleportDistance / std::max(distance, 1.0f));
                teleportTargetZ = pinchStartZ + (dz * teleportDistance / std::max(distance, 1.0f));
                
                std::cout << "  Teleport aiming... Target: (" << teleportTargetX << ", " << teleportTargetY << ", " << teleportTargetZ << ")" << std::endl;
            }
        } else if (isPinching && hand.pinchStrength < PINCH_RELEASE_THRESHOLD) {
            if (isTeleportAiming) {
                std::cout << "✓ PINCH RELEASED - Teleporting to: (" << teleportTargetX << ", " << teleportTargetY << ", " << teleportTargetZ << ")" << std::endl;
                isTeleportAiming = false;
            }
            isPinching = false;
            std::cout << "  Teleport completed!" << std::endl;
        }

        // Test grab gesture
        if (hand.grabStrength >= GRAB_THRESHOLD) {
            std::cout << "✓ GRAB DETECTED - Strength: " << hand.grabStrength << std::endl;
        }

        // Test point gesture
        if (hand.pointConfidence >= POINT_THRESHOLD) {
            std::cout << "✓ POINT DETECTED - Confidence: " << hand.pointConfidence << std::endl;
        }
    }
};

// Test scenarios
int main() {
    GestureRecognitionTester tester;
    
    std::cout << "=== Gesture Recognition System Test ===" << std::endl;
    
    // Test 1: Pinch and teleport
    std::cout << "\n--- Test 1: Pinch and Teleport ---" << std::endl;
    GestureRecognitionTester::HandData hand1{0.0f, 0.0f, 0.0f, false, 0.0f, 0.0f, 100.0f};
    tester.TestGestureDetection(hand1);
    
    hand1.pinchStrength = 0.85f;
    tester.TestGestureDetection(hand1);
    
    hand1.x = 50.0f;
    hand1.y = 0.0f;
    hand1.z = 150.0f;
    tester.TestGestureDetection(hand1);
    
    hand1.pinchStrength = 0.2f;
    tester.TestGestureDetection(hand1);
    
    // Test 2: Grab gesture
    std::cout << "\n--- Test 2: Grab Gesture ---" << std::endl;
    GestureRecognitionTester::HandData hand2{0.0f, 0.75f, 0.0f, true, 100.0f, 50.0f, 200.0f};
    tester.TestGestureDetection(hand2);
    
    // Test 3: Point gesture
    std::cout << "\n--- Test 3: Point Gesture ---" << std::endl;
    GestureRecognitionTester::HandData hand3{0.0f, 0.0f, 0.8f, false, -100.0f, 75.0f, 150.0f};
    tester.TestGestureDetection(hand3);
    
    std::cout << "\n=== Test Complete ===" << std::endl;
    
    return 0;
}