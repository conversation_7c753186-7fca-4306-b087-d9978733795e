// Copyright Epic Games, Inc. All Rights Reserved.

#include "Characters/XRPassthroughPawn.h"
#include "EnhancedInputComponent.h"
#include "EnhancedInputSubsystems.h"
#include "InputActionValue.h"
#include "Engine/World.h"
#include "DrawDebugHelpers.h"
#include "Kismet/GameplayStatics.h"

// Component includes
#include "Components/ULHandTrackingComponent.h"
#include "Components/VarjoPassthroughComponent.h"
#include "Components/GestureProcessorComponent.h"

AXRPassthroughPawn::AXRPassthroughPawn()
{
	// Set this pawn to call Tick() every frame
	PrimaryActorTick.bCanEverTick = true;

	// Initialize XR components
	InitializeXRComponents();

	// Initialize teleport variables
	bIsTeleportAiming = false;
	bIsPinching = false;
	TeleportTargetLocation = FVector::ZeroVector;
	PinchStartLocation = FVector::ZeroVector;
}

void AXRPassthroughPawn::BeginPlay()
{
	Super::BeginPlay();

	// Add Input Mapping Context
	if (APlayerController* PlayerController = Cast<APlayerController>(Controller))
	{
		if (UEnhancedInputLocalPlayerSubsystem* Subsystem = ULocalPlayer::GetSubsystem<UEnhancedInputLocalPlayerSubsystem>(PlayerController->GetLocalPlayer()))
		{
			Subsystem->AddMappingContext(DefaultMappingContext, 0);
		}
	}

	// Setup gesture event bindings
	if (GestureProcessorComponent)
	{
		GestureProcessorComponent->OnGestureRecognized.AddDynamic(this, &AXRPassthroughPawn::OnGestureRecognized);
	}
}

void AXRPassthroughPawn::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

	// Update hand tracking data
	if (HandTrackingComponent)
	{
		HandTrackingComponent->UpdateHandTracking(DeltaTime);
	}

	// Process gesture recognition
	if (GestureProcessorComponent && HandTrackingComponent)
	{
		const TArray<FHandTrackingData>& HandData = HandTrackingComponent->GetHandTrackingData();
		GestureProcessorComponent->ProcessHandData(HandData, DeltaTime);
	}
}

void AXRPassthroughPawn::SetupPlayerInputComponent(UInputComponent* PlayerInputComponent)
{
	Super::SetupPlayerInputComponent(PlayerInputComponent);

	// Set up action bindings
	if (UEnhancedInputComponent* EnhancedInputComponent = CastChecked<UEnhancedInputComponent>(PlayerInputComponent))
	{
		// Gesture trigger binding
		EnhancedInputComponent->BindAction(GestureTriggerAction, ETriggerEvent::Triggered, this, &AXRPassthroughPawn::OnGestureTrigger);
	}
}

void AXRPassthroughPawn::InitializeXRComponents()
{
	// Create and setup UltraLeap hand tracking component
	HandTrackingComponent = CreateDefaultSubobject<ULHandTrackingComponent>(TEXT("HandTrackingComponent"));
	if (HandTrackingComponent)
	{
		HandTrackingComponent->SetRelativeLocation(FVector(0.0f, 0.0f, 0.0f));
		HandTrackingComponent->SetHandTrackingEnabled(true);
		HandTrackingComponent->SetTrackingMode(EHandTrackingMode::BothHands);
	}

	// Create and setup Varjo passthrough component
	VarjoPassthroughComponent = CreateDefaultSubobject<UVarjoPassthroughComponent>(TEXT("VarjoPassthroughComponent"));
	if (VarjoPassthroughComponent)
	{
		VarjoPassthroughComponent->SetRelativeLocation(FVector(0.0f, 0.0f, 0.0f));
		VarjoPassthroughComponent->SetPassthroughEnabled(true);
		VarjoPassthroughComponent->SetPassthroughOpacity(1.0f);
	}

	// Create and setup gesture processor component
	GestureProcessorComponent = CreateDefaultSubobject<UGestureProcessorComponent>(TEXT("GestureProcessorComponent"));
	if (GestureProcessorComponent)
	{
		GestureProcessorComponent->SetRelativeLocation(FVector(0.0f, 0.0f, 0.0f));
		GestureProcessorComponent->SetGestureRecognitionEnabled(true);
		GestureProcessorComponent->SetConfidenceThreshold(0.8f);
	}
}

void AXRPassthroughPawn::SetupGestureInputBindings()
{
	// This method can be extended for additional gesture-specific input bindings
	// Currently handled in SetupPlayerInputComponent
}

void AXRPassthroughPawn::OnGestureTrigger(const FInputActionValue& Value)
{
	const bool bTriggerPressed = Value.Get<bool>();
	if (bTriggerPressed && GestureProcessorComponent)
	{
		// Trigger gesture recognition
		GestureProcessorComponent->TriggerGestureRecognition();
	}
}

void AXRPassthroughPawn::OnGestureRecognized(const FString& GestureName, float Confidence)
{
	// Log recognized gesture
	UE_LOG(LogTemp, Log, TEXT("Gesture recognized: %s (Confidence: %.2f)"), *GestureName, Confidence);

	// Handle different gesture types
	if (GestureName.Equals(TEXT("Pinch"), ESearchCase::IgnoreCase))
	{
		// Handle pinch gesture
		if (VarjoPassthroughComponent)
		{
			const bool bCurrentState = VarjoPassthroughComponent->IsPassthroughEnabled();
			VarjoPassthroughComponent->SetPassthroughEnabled(!bCurrentState);
		}
	}
	else if (GestureName.Equals(TEXT("Point"), ESearchCase::IgnoreCase))
	{
		// Handle point gesture
		// Add custom point gesture handling here
	}
	else if (GestureName.Equals(TEXT("Grab"), ESearchCase::IgnoreCase))
	{
		// Handle grab gesture
		// Add custom grab gesture handling here
	}

	// Broadcast gesture event to blueprints
	OnGestureRecognized_BP(GestureName, Confidence);
}

void AXRPassthroughPawn::OnPinch(float Strength, bool bIsLeftHand, const FVector& HandPosition)
{
	// Debug print for pinch gesture
	UE_LOG(LogTemp, Warning, TEXT("PINCH DETECTED - Strength: %.2f, Hand: %s, Position: %s"),
		Strength, bIsLeftHand ? TEXT("Left") : TEXT("Right"), *HandPosition.ToString());

	// Add debug screen message
	if (GEngine)
	{
		FString DebugMessage = FString::Printf(TEXT("PINCH: %.2f"), Strength);
		GEngine->AddOnScreenDebugMessage(-1, 2.0f, FColor::Green, DebugMessage);
	}

	// Teleport functionality with pinch gesture
	if (Strength >= 0.8f) // Pinch threshold
	{
		if (!bIsPinching)
		{
			// Start pinch - begin teleport aim
			bIsPinching = true;
			StartTeleportAim(HandPosition);
		}
		else if (bIsTeleportAiming)
		{
			// Update teleport aim position
			CurrentHandPosition = HandPosition;
			
			// Calculate teleport target based on hand direction
			FVector HandDirection = (HandPosition - PinchStartLocation).GetSafeNormal();
			float TeleportDistance = FMath::Clamp((HandPosition - PinchStartLocation).Size() * 2.0f, 100.0f, 2000.0f);
			TeleportTargetLocation = PinchStartLocation + (HandDirection * TeleportDistance);
			
			// Debug visualization
			if (GetWorld())
			{
				DrawDebugLine(GetWorld(), PinchStartLocation, TeleportTargetLocation, FColor::Purple, false, 0.1f, 0, 2.0f);
				DrawDebugSphere(GetWorld(), TeleportTargetLocation, 20.0f, 12, FColor::Green, false, 0.1f);
			}
		}
	}
	else if (bIsPinching && Strength < 0.3f) // Pinch release threshold
	{
		// Pinch released - complete teleport
		if (bIsTeleportAiming)
		{
			CompleteTeleport(TeleportTargetLocation);
		}
		bIsPinching = false;
	}

	// Broadcast to blueprints
	OnPinch_BP(Strength, bIsLeftHand, HandPosition);
}

void AXRPassthroughPawn::OnGrab(float Strength, bool bIsLeftHand, const FVector& HandPosition)
{
	// Debug print for grab gesture
	UE_LOG(LogTemp, Warning, TEXT("GRAB DETECTED - Strength: %.2f, Hand: %s, Position: %s"),
		Strength, bIsLeftHand ? TEXT("Left") : TEXT("Right"), *HandPosition.ToString());

	// Add debug screen message
	if (GEngine)
	{
		FString DebugMessage = FString::Printf(TEXT("GRAB: %.2f"), Strength);
		GEngine->AddOnScreenDebugMessage(-1, 2.0f, FColor::Blue, DebugMessage);
	}

	// Broadcast to blueprints
	OnGrab_BP(Strength, bIsLeftHand, HandPosition);
}

void AXRPassthroughPawn::OnPoint(float Confidence, bool bIsLeftHand, const FVector& HandPosition)
{
	// Debug print for point gesture
	UE_LOG(LogTemp, Warning, TEXT("POINT DETECTED - Confidence: %.2f, Hand: %s, Position: %s"),
		Confidence, bIsLeftHand ? TEXT("Left") : TEXT("Right"), *HandPosition.ToString());

	// Add debug screen message
	if (GEngine)
	{
		FString DebugMessage = FString::Printf(TEXT("POINT: %.2f"), Confidence);
		GEngine->AddOnScreenDebugMessage(-1, 2.0f, FColor::Yellow, DebugMessage);
	}

	// Broadcast to blueprints
	OnPoint_BP(Confidence, bIsLeftHand, HandPosition);
}

void AXRPassthroughPawn::StartTeleportAim(const FVector& HandPosition)
{
	bIsTeleportAiming = true;
	PinchStartLocation = HandPosition;
	
	UE_LOG(LogTemp, Warning, TEXT("TELEPORT AIM STARTED - Hand Position: %s"), *HandPosition.ToString());
	
	if (GEngine)
	{
		GEngine->AddOnScreenDebugMessage(-1, 3.0f, FColor::Purple, TEXT("TELEPORT AIMING..."));
	}
}

void AXRPassthroughPawn::CompleteTeleport(const FVector& TargetLocation)
{
	if (!bIsTeleportAiming)
	{
		return;
	}

	bIsTeleportAiming = false;
	bIsPinching = false;
	
	// Perform teleport
	FVector NewLocation = TargetLocation;
	NewLocation.Z = GetActorLocation().Z; // Maintain current height
	
	// Simple teleport - snap to target location
	SetActorLocation(NewLocation, false, nullptr, ETeleportType::TeleportPhysics);
	
	UE_LOG(LogTemp, Warning, TEXT("TELEPORT COMPLETED - New Location: %s"), *NewLocation.ToString());
	
	if (GEngine)
	{
		FString DebugMessage = FString::Printf(TEXT("TELEPORTED TO: %s"), *TargetLocation.ToString());
		GEngine->AddOnScreenDebugMessage(-1, 3.0f, FColor::Green, DebugMessage);
	}
}

void AXRPassthroughPawn::CancelTeleport()
{
	bIsTeleportAiming = false;
	bIsPinching = false;
	
	UE_LOG(LogTemp, Warning, TEXT("TELEPORT CANCELLED"));
	
	if (GEngine)
	{
		GEngine->AddOnScreenDebugMessage(-1, 2.0f, FColor::Red, TEXT("TELEPORT CANCELLED"));
	}
}

void AXRPassthroughPawn::SetPassthroughEnabled(bool bEnabled)
{
	if (VarjoPassthroughComponent)
	{
		VarjoPassthroughComponent->SetPassthroughEnabled(bEnabled);
	}
}

bool AXRPassthroughPawn::IsPassthroughEnabled() const
{
	return VarjoPassthroughComponent ? VarjoPassthroughComponent->IsPassthroughEnabled() : false;
}

// Blueprint callable event for gesture recognition
UFUNCTION(BlueprintImplementableEvent, Category = "XR|Gestures")
void AXRPassthroughPawn::OnGestureRecognized_BP(const FString& GestureName, float Confidence);