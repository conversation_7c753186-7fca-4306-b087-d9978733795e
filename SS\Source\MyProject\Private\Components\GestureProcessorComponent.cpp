// Copyright Epic Games, Inc. All Rights Reserved.

#include "Components/GestureProcessorComponent.h"
#include "Engine/Engine.h"
#include "Components/ULHandTrackingComponent.h"
#include "Kismet/GameplayStatics.h"

UGestureProcessorComponent::UGestureProcessorComponent()
{
	PrimaryComponentTick.bCanEverTick = true;
	bGestureRecognitionEnabled = true;
	bShowDebugInfo = false;
}

void UGestureProcessorComponent::BeginPlay()
{
	Super::BeginPlay();
}

void UGestureProcessorComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);
	
	// Auto-process hand tracking data if available
	if (bGestureRecognitionEnabled)
	{
		// Find hand tracking component in owner
		AActor* Owner = GetOwner();
		if (Owner)
		{
			UULHandTrackingComponent* HandTrackingComp = Owner->FindComponentByClass<UULHandTrackingComponent>();
			if (HandTrackingComp && HandTrackingComp->IsHandTrackingActive())
			{
				const TArray<FHandTrackingData>& HandData = HandTrackingComp->GetHandTrackingData();
				ProcessHandData(HandData, DeltaTime);
			}
		}
	}
}

void UGestureProcessorComponent::SetGestureRecognitionEnabled(bool bEnabled)
{
	bGestureRecognitionEnabled = bEnabled;
	
	if (!bEnabled)
	{
		// Clear all active gestures when disabled
		CurrentGestures.Empty();
		ActiveGestures.Empty();
		GestureStartTimes.Empty();
	}
}

void UGestureProcessorComponent::SetGestureConfig(const FGestureConfig& NewConfig)
{
	GestureConfig = NewConfig;
}

void UGestureProcessorComponent::SetConfidenceThreshold(float Threshold)
{
	GestureConfig.ConfidenceThreshold = FMath::Clamp(Threshold, 0.0f, 1.0f);
}

void UGestureProcessorComponent::SetDebugVisualizationEnabled(bool bEnabled)
{
	bShowDebugInfo = bEnabled;
}

void UGestureProcessorComponent::ProcessHandData(const TArray<FHandTrackingData>& HandData, float DeltaTime)
{
	if (!bGestureRecognitionEnabled || HandData.Num() == 0)
	{
		return;
	}

	// Clear previous frame's gestures
	CurrentGestures.Empty();

	for (const FHandTrackingData& Hand : HandData)
	{
		if (!Hand.bIsValid)
			continue;

		ProcessPinchGesture(Hand, DeltaTime);
		ProcessPointGesture(Hand, DeltaTime);
		ProcessGrabGesture(Hand, DeltaTime);
	}

	// Remove completed gestures
	RemoveCompletedGestures();
	
	// Debug visualization
	if (bShowDebugInfo)
	{
		DebugDisplayGestures();
	}
}

void UGestureProcessorComponent::TriggerGestureRecognition()
{
	if (bGestureRecognitionEnabled)
	{
		UE_LOG(LogTemp, Log, TEXT("Manual gesture recognition triggered"));
		
		// Force processing of current hand data
		AActor* Owner = GetOwner();
		if (Owner)
		{
			UULHandTrackingComponent* HandTrackingComp = Owner->FindComponentByClass<UULHandTrackingComponent>();
			if (HandTrackingComp)
			{
				HandTrackingComp->UpdateHandTracking(0.0f);
				const TArray<FHandTrackingData>& HandData = HandTrackingComp->GetHandTrackingData();
				ProcessHandData(HandData, 0.0f);
			}
		}
	}
}

void UGestureProcessorComponent::ProcessPinchGesture(const FHandTrackingData& HandData, float DeltaTime)
{
	float Confidence = 0.0f;
	float Intensity = 0.0f;
	
	if (DetectPinch(HandData, Confidence, Intensity))
	{
		if (Confidence >= GestureConfig.ConfidenceThreshold)
		{
			UpdateGestureState(TEXT("Pinch"), EGestureType::Pinch, Confidence, HandData.PalmPosition, HandData.bIsLeft, DeltaTime);
		}
	}
}

void UGestureProcessorComponent::ProcessPointGesture(const FHandTrackingData& HandData, float DeltaTime)
{
	float Confidence = 0.0f;
	float Intensity = 0.0f;
	
	if (DetectPoint(HandData, Confidence, Intensity))
	{
		if (Confidence >= GestureConfig.ConfidenceThreshold)
		{
			UpdateGestureState(TEXT("Point"), EGestureType::Point, Confidence, HandData.PalmPosition, HandData.bIsLeft, DeltaTime);
		}
	}
}

void UGestureProcessorComponent::ProcessGrabGesture(const FHandTrackingData& HandData, float DeltaTime)
{
	float Confidence = 0.0f;
	float Intensity = 0.0f;
	
	if (DetectGrab(HandData, Confidence, Intensity))
	{
		if (Confidence >= GestureConfig.ConfidenceThreshold)
		{
			UpdateGestureState(TEXT("Grab"), EGestureType::Grab, Confidence, HandData.PalmPosition, HandData.bIsLeft, DeltaTime);
		}
	}
}

bool UGestureProcessorComponent::DetectPinch(const FHandTrackingData& HandData, float& OutConfidence, float& OutIntensity)
{
	if (HandData.FingerTips.Num() >= 2)
	{
		const FVector& ThumbTip = HandData.FingerTips[0];
		const FVector& IndexTip = HandData.FingerTips[1];
		float Distance = FVector::Dist(ThumbTip, IndexTip);
		
		// Calculate confidence based on distance
		float NormalizedDistance = FMath::Clamp(Distance / GestureConfig.PinchThreshold, 0.0f, 1.0f);
		OutConfidence = FMath::Clamp(1.0f - NormalizedDistance, 0.0f, 1.0f) * GestureConfig.PinchSensitivity;
		
		// Calculate intensity (how strong the pinch is)
		OutIntensity = FMath::Clamp(1.0f - (Distance / GestureConfig.PinchThreshold), 0.0f, 1.0f);
		
		return Distance < GestureConfig.PinchThreshold;
	}
	
	OutConfidence = 0.0f;
	OutIntensity = 0.0f;
	return false;
}

bool UGestureProcessorComponent::DetectPoint(const FHandTrackingData& HandData, float& OutConfidence, float& OutIntensity)
{
	if (HandData.FingerTips.Num() >= 5)
	{
		const FVector& IndexTip = HandData.FingerTips[1];
		const FVector& MiddleTip = HandData.FingerTips[2];
		const FVector& RingTip = HandData.FingerTips[3];
		const FVector& PinkyTip = HandData.FingerTips[4];
		
		// Calculate finger extensions
		float IndexExtension = FVector::Dist(HandData.PalmPosition, IndexTip);
		float MiddleExtension = FVector::Dist(HandData.PalmPosition, MiddleTip);
		float RingExtension = FVector::Dist(HandData.PalmPosition, RingTip);
		float PinkyExtension = FVector::Dist(HandData.PalmPosition, PinkyTip);
		
		// Check if index is extended and others are curled
		bool bIndexExtended = IndexExtension > GestureConfig.PointThreshold;
		bool bOthersCurled = MiddleExtension < GestureConfig.PointThreshold * 0.5f &&
							RingExtension < GestureConfig.PointThreshold * 0.5f &&
							PinkyExtension < GestureConfig.PointThreshold * 0.5f;
		
		if (bIndexExtended && bOthersCurled)
		{
			OutConfidence = GestureConfig.PointSensitivity;
			OutIntensity = FMath::Clamp(IndexExtension / (GestureConfig.PointThreshold * 2.0f), 0.0f, 1.0f);
			return true;
		}
	}
	
	OutConfidence = 0.0f;
	OutIntensity = 0.0f;
	return false;
}

bool UGestureProcessorComponent::DetectGrab(const FHandTrackingData& HandData, float& OutConfidence, float& OutIntensity)
{
	if (HandData.FingerTips.Num() >= 5)
	{
		float TotalDistance = 0.0f;
		for (const FVector& FingerTip : HandData.FingerTips)
		{
			TotalDistance += FVector::Dist(HandData.PalmPosition, FingerTip);
		}
		
		float AverageDistance = TotalDistance / HandData.FingerTips.Num();
		
		// Calculate confidence based on average distance
		float NormalizedDistance = FMath::Clamp(AverageDistance / GestureConfig.GrabThreshold, 0.0f, 1.0f);
		OutConfidence = FMath::Clamp(1.0f - NormalizedDistance, 0.0f, 1.0f) * GestureConfig.GrabSensitivity;
		
		// Calculate intensity (how strong the grab is)
		OutIntensity = FMath::Clamp(1.0f - (AverageDistance / GestureConfig.GrabThreshold), 0.0f, 1.0f);
		
		return AverageDistance < GestureConfig.GrabThreshold;
	}
	
	OutConfidence = 0.0f;
	OutIntensity = 0.0f;
	return false;
}

void UGestureProcessorComponent::UpdateGestureState(const FString& GestureName, EGestureType Type, float Confidence, const FVector& HandPosition, bool bIsLeft, float DeltaTime)
{
	FString GestureKey = GestureName + (bIsLeft ? TEXT("_Left") : TEXT("_Right"));
	
	if (!ActiveGestures.Contains(GestureKey))
	{
		// New gesture started
		FGestureEventData NewGesture;
		NewGesture.GestureType = Type;
		NewGesture.Confidence = Confidence;
		NewGesture.HandPosition = HandPosition;
		NewGesture.bIsLeftHand = bIsLeft;
		NewGesture.Duration = 0.0f;
		NewGesture.Intensity = Confidence;
		
		ActiveGestures.Add(GestureKey, NewGesture);
		GestureStartTimes.Add(GestureKey, 0.0f);
		
		OnGestureStarted.Broadcast(NewGesture);
	}
	else
	{
		// Update existing gesture
		FGestureEventData& ExistingGesture = ActiveGestures[GestureKey];
		ExistingGesture.Confidence = Confidence;
		ExistingGesture.HandPosition = HandPosition;
		ExistingGesture.Duration += DeltaTime;
		
		OnGestureUpdated.Broadcast(ExistingGesture);
	}
	
	// Add to current gestures list
	CurrentGestures.Add(ActiveGestures[GestureKey]);
}

void UGestureProcessorComponent::RemoveCompletedGestures()
{
	TArray<FString> GesturesToRemove;
	
	for (const auto& Pair : ActiveGestures)
	{
		bool bStillActive = false;
		
		// Check if gesture is still active in current frame
		for (const FGestureEventData& CurrentGesture : CurrentGestures)
		{
			FString CurrentKey;
			switch (CurrentGesture.GestureType)
			{
				case EGestureType::Pinch: CurrentKey = TEXT("Pinch"); break;
				case EGestureType::Grab: CurrentKey = TEXT("Grab"); break;
				case EGestureType::Point: CurrentKey = TEXT("Point"); break;
				default: continue;
			}
			
			CurrentKey += (CurrentGesture.bIsLeftHand ? TEXT("_Left") : TEXT("_Right"));
			
			if (CurrentKey == Pair.Key)
			{
				bStillActive = true;
				break;
			}
		}
		
		if (!bStillActive)
		{
			GesturesToRemove.Add(Pair.Key);
			OnGestureCompleted.Broadcast(Pair.Value);
		}
	}
	
	// Remove completed gestures
	for (const FString& Key : GesturesToRemove)
	{
		ActiveGestures.Remove(Key);
		GestureStartTimes.Remove(Key);
	}
}

void UGestureProcessorComponent::SetDebugVisualizationEnabled(bool bEnabled)
{
	bShowDebugInfo = bEnabled;
}

void UGestureProcessorComponent::DebugDisplayGestures()
{
	if (!GEngine || CurrentGestures.Num() == 0)
	{
		return;
	}
	
	FString DebugText = TEXT("Active Gestures:\n");
	for (const FGestureEventData& Gesture : CurrentGestures)
	{
		FString GestureName;
		switch (Gesture.GestureType)
		{
			case EGestureType::Pinch: GestureName = TEXT("Pinch"); break;
			case EGestureType::Grab: GestureName = TEXT("Grab"); break;
			case EGestureType::Point: GestureName = TEXT("Point"); break;
			default: GestureName = TEXT("Unknown"); break;
		}
		
		FString HandSide = Gesture.bIsLeftHand ? TEXT("Left") : TEXT("Right");
		DebugText += FString::Printf(TEXT("%s (%s): %.1f%%\n"), *GestureName, *HandSide, Gesture.Confidence * 100.0f);
	}
	
	// Display debug text on screen
	GEngine->AddOnScreenDebugMessage(-1, 0.0f, FColor::White, DebugText);
}