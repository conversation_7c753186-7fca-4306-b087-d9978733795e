// Copyright Epic Games, Inc. All Rights Reserved.

#include "Components/ULHandTrackingComponent.h"
#include "Engine/Engine.h"
#include "DrawDebugHelpers.h"

ULHandTrackingComponent::ULHandTrackingComponent()
{
	PrimaryComponentTick.bCanEverTick = true;
	bHandTrackingEnabled = true;
	TrackingMode = EHandTrackingMode::BothHands;
}

void ULHandTrackingComponent::BeginPlay()
{
	Super::BeginPlay();
}

void ULHandTrackingComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);
	
	UpdateHandTracking(DeltaTime);
}

void ULHandTrackingComponent::SetHandTrackingEnabled(bool bEnabled)
{
	bHandTrackingEnabled = bEnabled;
}

void ULHandTrackingComponent::SetTrackingMode(EHandTrackingMode NewMode)
{
	TrackingMode = NewMode;
}

void ULHandTrackingComponent::UpdateHandTracking(float DeltaTime)
{
	if (!bHandTrackingEnabled)
	{
		return;
	}

	// Clear previous data
	HandTrackingData.Empty();

	// Simulate hand tracking data based on tracking mode
	if (TrackingMode == EHandTrackingMode::LeftHand || TrackingMode == EHandTrackingMode::BothHands)
	{
		FHandTrackingData LeftHandData;
		LeftHandData.bIsValid = true;
		LeftHandData.bIsLeft = true;
		LeftHandData.PalmPosition = FVector(-20.0f, 0.0f, 100.0f);
		LeftHandData.PalmNormal = FVector(0.0f, 0.0f, 1.0f);
		
		// Simulate finger positions
		for (int32 i = 0; i < 5; i++)
		{
			LeftHandData.FingerTips[i] = FVector(-20.0f + i * 2.0f, 0.0f, 100.0f + i * 5.0f);
			
			// Simulate finger joints
			for (int32 j = 0; j < 5; j++)
			{
				int32 Index = i * 5 + j;
				if (Index < LeftHandData.FingerJoints.Num())
				{
					LeftHandData.FingerJoints[Index] = FVector(-20.0f + i * 2.0f, j * 0.5f, 100.0f + i * 5.0f + j * 2.0f);
					LeftHandData.FingerRotations[Index] = FQuat::Identity;
				}
			}
		}
		
		HandTrackingData.Add(LeftHandData);
	}

	if (TrackingMode == EHandTrackingMode::RightHand || TrackingMode == EHandTrackingMode::BothHands)
	{
		FHandTrackingData RightHandData;
		RightHandData.bIsValid = true;
		RightHandData.bIsLeft = false;
		RightHandData.PalmPosition = FVector(20.0f, 0.0f, 100.0f);
		RightHandData.PalmNormal = FVector(0.0f, 0.0f, 1.0f);
		
		// Simulate finger positions
		for (int32 i = 0; i < 5; i++)
		{
			RightHandData.FingerTips[i] = FVector(20.0f - i * 2.0f, 0.0f, 100.0f + i * 5.0f);
			
			// Simulate finger joints
			for (int32 j = 0; j < 5; j++)
			{
				int32 Index = i * 5 + j;
				if (Index < RightHandData.FingerJoints.Num())
				{
					RightHandData.FingerJoints[Index] = FVector(20.0f - i * 2.0f, j * 0.5f, 100.0f + i * 5.0f + j * 2.0f);
					RightHandData.FingerRotations[Index] = FQuat::Identity;
				}
			}
		}
		
		HandTrackingData.Add(RightHandData);
	}

	// Debug visualization
	if (GEngine && HandTrackingData.Num() > 0)
	{
		GEngine->AddOnScreenDebugMessage(-1, 0.0f, FColor::Blue,
			FString::Printf(TEXT("Tracking %d hands"), HandTrackingData.Num()));
		
		// Draw debug visualization
		for (const FHandTrackingData& Hand : HandTrackingData)
		{
			// Draw palm position
			DrawDebugSphere(GetWorld(), Hand.PalmPosition, 2.0f, 8, FColor::Green, false, 0.0f, 0, 1.0f);
			
			// Draw finger tips
			for (const FVector& FingerTip : Hand.FingerTips)
			{
				DrawDebugSphere(GetWorld(), FingerTip, 1.0f, 8, FColor::Red, false, 0.0f, 0, 0.5f);
			}
			
			// Draw palm normal
			DrawDebugLine(GetWorld(), Hand.PalmPosition, Hand.PalmPosition + Hand.PalmNormal * 10.0f, FColor::Blue, false, 0.0f, 0, 1.0f);
		}
	}
}

const TArray<FHandTrackingData>& ULHandTrackingComponent::GetHandTrackingData() const
{
	return HandTrackingData;
}

bool ULHandTrackingComponent::IsHandTrackingActive() const
{
	return bHandTrackingEnabled && HandTrackingData.Num() > 0;
}