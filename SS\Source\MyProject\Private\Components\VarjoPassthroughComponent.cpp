// Copyright Epic Games, Inc. All Rights Reserved.

#include "Components/VarjoPassthroughComponent.h"
#include "Engine/Engine.h"

UVarjoPassthroughComponent::UVarjoPassthroughComponent()
{
	PrimaryComponentTick.bCanEverTick = true;
	bPassthroughEnabled = true;
	PassthroughOpacity = 1.0f;
}

void UVarjoPassthroughComponent::BeginPlay()
{
	Super::BeginPlay();
	UpdatePassthroughRendering();
}

void UVarjoPassthroughComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);
}

void UVarjoPassthroughComponent::SetPassthroughEnabled(bool bEnabled)
{
	if (bPassthroughEnabled != bEnabled)
	{
		bPassthroughEnabled = bEnabled;
		UpdatePassthroughRendering();
		OnPassthroughStateChanged.Broadcast(bEnabled);
	}
}

void UVarjoPassthroughComponent::SetPassthroughOpacity(float Opacity)
{
	PassthroughOpacity = FMath::Clamp(Opacity, 0.0f, 1.0f);
	UpdatePassthroughRendering();
}

void UVarjoPassthroughComponent::UpdatePassthroughRendering()
{
	// Placeholder for Varjo-specific passthrough implementation
	// In a real implementation, this would interface with Varjo OpenXR extensions
	if (GEngine)
	{
		GEngine->AddOnScreenDebugMessage(-1, 2.0f, FColor::Green, 
			FString::Printf(TEXT("Varjo Passthrough: %s (Opacity: %.2f)"), 
			bPassthroughEnabled ? TEXT("Enabled") : TEXT("Disabled"), 
			PassthroughOpacity));
	}
}