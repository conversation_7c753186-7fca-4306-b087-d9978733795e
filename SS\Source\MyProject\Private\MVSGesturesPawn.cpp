
// Copyright 2025 MVS. All Rights Reserved.

#include "MVSGesturesPawn.h"
#include "PoseDetection/PinchDetector.h"
#include "InteractionEngine/Blueprints/Teleportation/LeapTeleportationComponent.h"
#include "Components/InputComponent.h"

AMVSGesturesPawn::AMVSGesturesPawn()
{
    // Create the pinch detectors
    PinchDetector_L = CreateDefaultSubobject<UPinchDetector>(TEXT("PinchDetector_L"));
    PinchDetector_R = CreateDefaultSubobject<UPinchDetector>(TEXT("PinchDetector_R"));

    // Create the teleportation component
    TeleportationComponent = CreateDefaultSubobject<ULeapTeleportationComponent>(TEXT("TeleportationComponent"));

    // Set default values for pinch detectors
    PinchDetector_L->HandType = EHandType::LEAP_HAND_LEFT;
    PinchDetector_R->HandType = EHandType::LEAP_HAND_RIGHT;
}

void AMVSGesturesPawn::BeginPlay()
{
    Super::BeginPlay();

    // Bind the pinch and un-pinch events
    PinchDetector_L->OnPoseDetected.AddDynamic(this, &AMVSGesturesPawn::OnPinch);
    PinchDetector_R->OnPoseDetected.AddDynamic(this, &AMVSGesturesPawn::OnPinch);
    PinchDetector_L->OnPoseLost.AddDynamic(this, &AMVSGesturesPawn::OnUnPinch);
    PinchDetector_R->OnPoseLost.AddDynamic(this, &AMVSGesturesPawn::OnUnPinch);
}

void AMVSGesturesPawn::SetupPlayerInputComponent(UInputComponent* PlayerInputComponent)
{
    Super::SetupPlayerInputComponent(PlayerInputComponent);
}

void AMVSGesturesPawn::OnPinch(const FLeapHandData& HandData)
{
    if (!bIsAiming)
    {
        bIsAiming = true;
        TeleportationComponent->Activate();
    }
}

void AMVSGesturesPawn::OnUnPinch(const FLeapHandData& HandData)
{
    if (bIsAiming)
    {
        bIsAiming = false;
        OnTeleportConfirm(HandData);
        TeleportationComponent->Deactivate();
    }
}

void AMVSGesturesPawn::OnTeleportConfirm(const FLeapHandData& HandData)
{
    // Check if the hand is open (you can adjust the threshold)
    if (HandData.GrabStrength < 0.1f)
    {
        TeleportationComponent->Teleport();
    }
}
