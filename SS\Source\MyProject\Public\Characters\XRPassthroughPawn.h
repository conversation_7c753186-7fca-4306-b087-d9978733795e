// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Pawn.h"
#include "XRPassthroughPawn.generated.h"

class ULHandTrackingComponent;
class UVarjoPassthroughComponent;
class UGestureProcessorComponent;

UCLASS()
class MYPROJECT_API AXRPassthroughPawn : public APawn
{
	GENERATED_BODY()

public:
	// Sets default values for this pawn's properties
	AXRPassthroughPawn();

protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

	// Called every frame
	virtual void Tick(float DeltaTime) override;

	// Called to bind functionality to input
	virtual void SetupPlayerInputComponent(class UInputComponent* PlayerInputComponent) override;

	// UltraLeap hand tracking component for hand gesture recognition
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "XR|Hand Tracking")
	ULHandTrackingComponent* HandTrackingComponent;

	// Varjo passthrough component for mixed reality
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "XR|Passthrough")
	UVarjoPassthroughComponent* VarjoPassthroughComponent;

	// Gesture processor component for gesture recognition and handling
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "XR|Gestures")
	UGestureProcessorComponent* GestureProcessorComponent;

	// Input action for gesture triggers
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Input|Gestures")
	class UInputAction* GestureTriggerAction;

	// Input mapping context
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Input")
	class UInputMappingContext* DefaultMappingContext;

	// Handle gesture trigger input
	UFUNCTION(BlueprintCallable, Category = "Input|Gestures")
	void OnGestureTrigger(const FInputActionValue& Value);

	// Handle gesture recognition events
	UFUNCTION(BlueprintCallable, Category = "XR|Gestures")
	void OnGestureRecognized(const FString& GestureName, float Confidence);

	// Custom gesture events for Stage 1 implementation
	UFUNCTION(BlueprintCallable, Category = "XR|Gestures")
	void OnPinch(float Strength, bool bIsLeftHand, const FVector& HandPosition);

	UFUNCTION(BlueprintCallable, Category = "XR|Gestures")
	void OnGrab(float Strength, bool bIsLeftHand, const FVector& HandPosition);

	UFUNCTION(BlueprintCallable, Category = "XR|Gestures")
	void OnPoint(float Confidence, bool bIsLeftHand, const FVector& HandPosition);

	// Enable/disable passthrough rendering
	UFUNCTION(BlueprintCallable, Category = "XR|Passthrough")
	void SetPassthroughEnabled(bool bEnabled);

	// Get current passthrough state
	UFUNCTION(BlueprintPure, Category = "XR|Passthrough")
	bool IsPassthroughEnabled() const;

private:
	// Initialize XR components
	void InitializeXRComponents();

	// Setup input bindings
	void SetupGestureInputBindings();

	// Teleport state variables
	UPROPERTY()
	bool bIsTeleportAiming;

	UPROPERTY()
	bool bIsPinching;

	UPROPERTY()
	FVector TeleportTargetLocation;

	UPROPERTY()
	FVector PinchStartLocation;

	UPROPERTY()
	FVector CurrentHandPosition;

public:
	// Blueprint implementable events for specific gestures
	UFUNCTION(BlueprintImplementableEvent, Category = "XR|Gestures")
	void OnPinch_BP(float Strength, bool bIsLeftHand, const FVector& HandPosition);

	UFUNCTION(BlueprintImplementableEvent, Category = "XR|Gestures")
	void OnGrab_BP(float Strength, bool bIsLeftHand, const FVector& HandPosition);

	UFUNCTION(BlueprintImplementableEvent, Category = "XR|Gestures")
	void OnPoint_BP(float Confidence, bool bIsLeftHand, const FVector& HandPosition);

	// Teleport functionality with pinch gesture
	UFUNCTION(BlueprintCallable, Category = "XR|Teleport")
	void StartTeleportAim(const FVector& HandPosition);

	UFUNCTION(BlueprintCallable, Category = "XR|Teleport")
	void CompleteTeleport(const FVector& TargetLocation);

	UFUNCTION(BlueprintCallable, Category = "XR|Teleport")
	void CancelTeleport();
};