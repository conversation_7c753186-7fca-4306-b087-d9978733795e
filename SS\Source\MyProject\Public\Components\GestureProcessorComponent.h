// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "GestureProcessorComponent.generated.h"

class UULHandTrackingComponent;

UENUM(BlueprintType)
enum class EGestureType : uint8
{
	None UMETA(DisplayName = "None"),
	Pinch UMETA(DisplayName = "Pinch"),
	Grab UMETA(DisplayName = "Grab"),
	Point UMETA(DisplayName = "Point")
};

USTRUCT(BlueprintType)
struct FGestureConfig
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gesture Config")
	float PinchThreshold = 2.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gesture Config")
	float GrabThreshold = 5.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gesture Config")
	float PointThreshold = 10.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gesture Config")
	float PinchSensitivity = 0.8f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gesture Config")
	float GrabSensitivity = 0.85f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gesture Config")
	float PointSensitivity = 0.9f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gesture Config")
	float ConfidenceThreshold = 0.75f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gesture Config")
	bool bEnableDebugVisualization = false;
};

USTRUCT(BlueprintType)
struct FGestureEventData
{
	GENERATED_BODY()

	UPROPERTY(BlueprintReadOnly, Category = "Gesture Event")
	EGestureType GestureType = EGestureType::None;

	UPROPERTY(BlueprintReadOnly, Category = "Gesture Event")
	float Confidence = 0.0f;

	UPROPERTY(BlueprintReadOnly, Category = "Gesture Event")
	FVector HandPosition = FVector::ZeroVector;

	UPROPERTY(BlueprintReadOnly, Category = "Gesture Event")
	bool bIsLeftHand = false;

	UPROPERTY(BlueprintReadOnly, Category = "Gesture Event")
	float Duration = 0.0f;

	UPROPERTY(BlueprintReadOnly, Category = "Gesture Event")
	float Intensity = 0.0f;

	FGestureEventData()
		: GestureType(EGestureType::None)
		, Confidence(0.0f)
		, HandPosition(FVector::ZeroVector)
		, bIsLeftHand(false)
		, Duration(0.0f)
		, Intensity(0.0f)
	{}
};

USTRUCT(BlueprintType)
struct FHandTrackingData
{
	GENERATED_BODY()

	UPROPERTY(BlueprintReadOnly, Category = "Hand Tracking")
	FVector PalmPosition;

	UPROPERTY(BlueprintReadOnly, Category = "Hand Tracking")
	FVector PalmNormal;

	UPROPERTY(BlueprintReadOnly, Category = "Hand Tracking")
	TArray<FVector> FingerTips;

	UPROPERTY(BlueprintReadOnly, Category = "Hand Tracking")
	TArray<FVector> FingerJoints;

	UPROPERTY(BlueprintReadOnly, Category = "Hand Tracking")
	TArray<FQuat> FingerRotations;

	UPROPERTY(BlueprintReadOnly, Category = "Hand Tracking")
	bool bIsValid;

	UPROPERTY(BlueprintReadOnly, Category = "Hand Tracking")
	bool bIsLeft;

	FHandTrackingData()
		: PalmPosition(FVector::ZeroVector)
		, PalmNormal(FVector::UpVector)
		, bIsValid(false)
		, bIsLeft(false)
	{
		FingerTips.SetNum(5);
		FingerJoints.SetNum(25); // 5 fingers * 5 joints per finger
		FingerRotations.SetNum(25);
	}
};

UCLASS(ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class MYPROJECT_API UGestureProcessorComponent : public UActorComponent
{
	GENERATED_BODY()

public:
	UGestureProcessorComponent();

protected:
	virtual void BeginPlay() override;

public:
	virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

	// Enable/disable gesture recognition
	UFUNCTION(BlueprintCallable, Category = "Gesture Recognition")
	void SetGestureRecognitionEnabled(bool bEnabled);

	// Set gesture configuration
	UFUNCTION(BlueprintCallable, Category = "Gesture Recognition")
	void SetGestureConfig(const FGestureConfig& NewConfig);

	// Get current gesture configuration
	UFUNCTION(BlueprintPure, Category = "Gesture Recognition")
	const FGestureConfig& GetGestureConfig() const { return GestureConfig; }

	// Process hand tracking data for gesture recognition
	UFUNCTION(BlueprintCallable, Category = "Gesture Recognition")
	void ProcessHandData(const TArray<FHandTrackingData>& HandData, float DeltaTime);

	// Get currently active gestures
	UFUNCTION(BlueprintPure, Category = "Gesture Recognition")
	const TArray<FGestureEventData>& GetCurrentGestures() const { return CurrentGestures; }

	// Trigger manual gesture recognition
	UFUNCTION(BlueprintCallable, Category = "Gesture Recognition")
	void TriggerGestureRecognition();

	// Set confidence threshold for gesture recognition (0.0 - 1.0)
	UFUNCTION(BlueprintCallable, Category = "Gesture Recognition")
	void SetConfidenceThreshold(float Threshold);

	// Get current confidence threshold
	UFUNCTION(BlueprintPure, Category = "Gesture Recognition")
	float GetConfidenceThreshold() const { return GestureConfig.ConfidenceThreshold; }

	// Enable/disable debug visualization
	UFUNCTION(BlueprintCallable, Category = "Gesture Recognition")
	void SetDebugVisualizationEnabled(bool bEnabled);

	// Event triggered when a gesture starts
	UPROPERTY(BlueprintAssignable, Category = "Gesture Recognition")
	FOnGestureStarted OnGestureStarted;

	// Event triggered when a gesture updates
	UPROPERTY(BlueprintAssignable, Category = "Gesture Recognition")
	FOnGestureUpdated OnGestureUpdated;

	// Event triggered when a gesture completes
	UPROPERTY(BlueprintAssignable, Category = "Gesture Recognition")
	FOnGestureCompleted OnGestureCompleted;

private:
	// Gesture configuration
	UPROPERTY(EditAnywhere, Category = "Gesture Recognition")
	FGestureConfig GestureConfig;

	// Is gesture recognition enabled
	UPROPERTY(EditAnywhere, Category = "Gesture Recognition")
	bool bGestureRecognitionEnabled;

	// Show debug visualization
	UPROPERTY(EditAnywhere, Category = "Gesture Recognition")
	bool bShowDebugInfo;

	// Currently active gestures
	UPROPERTY(BlueprintReadOnly, Category = "Gesture Recognition")
	TArray<FGestureEventData> CurrentGestures;

	// Gesture state tracking
	TMap<FString, float> GestureStartTimes;
	TMap<FString, FGestureEventData> ActiveGestures;

	// Internal gesture processing
	void ProcessPinchGesture(const FHandTrackingData& HandData, float DeltaTime);
	void ProcessPointGesture(const FHandTrackingData& HandData, float DeltaTime);
	void ProcessGrabGesture(const FHandTrackingData& HandData, float DeltaTime);
	
	bool DetectPinch(const FHandTrackingData& HandData, float& OutConfidence, float& OutIntensity);
	bool DetectPoint(const FHandTrackingData& HandData, float& OutConfidence, float& OutIntensity);
	bool DetectGrab(const FHandTrackingData& HandData, float& OutConfidence, float& OutIntensity);
	
	void UpdateGestureState(const FString& GestureName, EGestureType Type, float Confidence, const FVector& HandPosition, bool bIsLeft, float DeltaTime);
	void RemoveCompletedGestures();
	
	// Debug visualization
	void DebugDisplayGestures();
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnGestureStarted, const FGestureEventData&, GestureData);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnGestureUpdated, const FGestureEventData&, GestureData);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnGestureCompleted, const FGestureEventData&, GestureData);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnGestureRecognized, const FString&, GestureName, float, Confidence);