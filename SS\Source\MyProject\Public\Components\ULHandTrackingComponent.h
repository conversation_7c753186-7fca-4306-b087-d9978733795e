// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "ULHandTrackingComponent.generated.h"

UENUM(BlueprintType)
enum class EHandTrackingMode : uint8
{
	LeftHand UMETA(DisplayName = "Left Hand Only"),
	RightHand UMETA(DisplayName = "Right Hand Only"),
	BothHands UMETA(DisplayName = "Both Hands")
};

USTRUCT(BlueprintType)
struct FHandTrackingData
{
	GENERATED_BODY()

	UPROPERTY(BlueprintReadOnly, Category = "Hand Tracking")
	FVector PalmPosition;

	UPROPERTY(BlueprintReadOnly, Category = "Hand Tracking")
	FVector PalmNormal;

	UPROPERTY(BlueprintReadOnly, Category = "Hand Tracking")
	TArray<FVector> FingerTips;

	UPROPERTY(BlueprintReadOnly, Category = "Hand Tracking")
	TArray<FVector> FingerJoints;

	UPROPERTY(BlueprintReadOnly, Category = "Hand Tracking")
	TArray<FQuat> FingerRotations;

	UPROPERTY(BlueprintReadOnly, Category = "Hand Tracking")
	bool bIsValid;

	UPROPERTY(BlueprintReadOnly, Category = "Hand Tracking")
	bool bIsLeft;

	FHandTrackingData()
		: PalmPosition(FVector::ZeroVector)
		, PalmNormal(FVector::UpVector)
		, bIsValid(false)
		, bIsLeft(false)
	{
		FingerTips.SetNum(5);
		FingerJoints.SetNum(25); // 5 fingers * 5 joints per finger
		FingerRotations.SetNum(25);
	}
};

UCLASS(ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class MYPROJECT_API UULHandTrackingComponent : public UActorComponent
{
	GENERATED_BODY()

public:
	UULHandTrackingComponent();

protected:
	virtual void BeginPlay() override;

public:
	virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

	// Enable/disable hand tracking
	UFUNCTION(BlueprintCallable, Category = "Hand Tracking")
	void SetHandTrackingEnabled(bool bEnabled);

	// Set tracking mode
	UFUNCTION(BlueprintCallable, Category = "Hand Tracking")
	void SetTrackingMode(EHandTrackingMode NewMode);

	// Update hand tracking data
	UFUNCTION(BlueprintCallable, Category = "Hand Tracking")
	void UpdateHandTracking(float DeltaTime);

	// Get current hand tracking data
	UFUNCTION(BlueprintPure, Category = "Hand Tracking")
	const TArray<FHandTrackingData>& GetHandTrackingData() const;

	// Check if hand tracking is active
	UFUNCTION(BlueprintPure, Category = "Hand Tracking")
	bool IsHandTrackingActive() const;

private:
	// Is hand tracking enabled
	UPROPERTY(EditAnywhere, Category = "Hand Tracking")
	bool bHandTrackingEnabled;

	// Current tracking mode
	UPROPERTY(EditAnywhere, Category = "Hand Tracking")
	EHandTrackingMode TrackingMode;

	// Current hand tracking data
	UPROPERTY(BlueprintReadOnly, Category = "Hand Tracking")
	TArray<FHandTrackingData> HandTrackingData;
};