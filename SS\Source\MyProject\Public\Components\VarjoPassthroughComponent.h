// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Components/SceneComponent.h"
#include "VarjoPassthroughComponent.generated.h"

UCLASS(ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class MYPROJECT_API UVarjoPassthroughComponent : public USceneComponent
{
	GENERATED_BODY()

public:
	UVarjoPassthroughComponent();

protected:
	virtual void BeginPlay() override;

public:
	virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

	// Enable/disable passthrough rendering
	UFUNCTION(BlueprintCallable, Category = "Varjo|Passthrough")
	void SetPassthroughEnabled(bool bEnabled);

	// Check if passthrough is enabled
	UFUNCTION(BlueprintPure, Category = "Varjo|Passthrough")
	bool IsPassthroughEnabled() const { return bPassthroughEnabled; }

	// Set passthrough opacity (0.0 - 1.0)
	UFUNCTION(BlueprintCallable, Category = "Varjo|Passthrough")
	void SetPassthroughOpacity(float Opacity);

	// Get current opacity
	UFUNCTION(BlueprintPure, Category = "Varjo|Passthrough")
	float GetPassthroughOpacity() const { return PassthroughOpacity; }

	// Event triggered when passthrough state changes
	UPROPERTY(BlueprintAssignable, Category = "Varjo|Passthrough")
	FOnPassthroughStateChanged OnPassthroughStateChanged;

private:
	// Is passthrough currently enabled
	UPROPERTY(EditAnywhere, Category = "Varjo|Passthrough")
	bool bPassthroughEnabled;

	// Passthrough opacity (0.0 - 1.0)
	UPROPERTY(EditAnywhere, Category = "Varjo|Passthrough", meta = (ClampMin = "0.0", ClampMax = "1.0"))
	float PassthroughOpacity;

	// Internal update function
	void UpdatePassthroughRendering();
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnPassthroughStateChanged, bool, bEnabled);