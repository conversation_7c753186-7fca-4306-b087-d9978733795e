
// Copyright 2025 MVS. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Pawn.h"
#include "InteractionEngine/Pawn/IEPawnHands.h"
#include "MVSGesturesPawn.generated.h"

class UPinchDetector;
class ULeapTeleportationComponent;

UCLASS()
class MYPROJECT_API AMVSGesturesPawn : public AIEPawnHands
{
    GENERATED_BODY()

public:
    AMVSGesturesPawn();

protected:
    virtual void BeginPlay() override;
    virtual void SetupPlayerInputComponent(class UInputComponent* PlayerInputComponent) override;

    // Pinch Detector Components for Left and Right Hand
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "MVS Gestures|Components")
    TObjectPtr<UPinchDetector> PinchDetector_L;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "MVS Gestures|Components")
    TObjectPtr<UPinchDetector> PinchDetector_R;

    // Teleportation Component
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "MVS Gestures|Components")
    TObjectPtr<ULeapTeleportationComponent> TeleportationComponent;

    // Called when a pinch gesture is detected
    UFUNCTION()
    void OnPinch(const FLeapHandData& HandData);

    // Called when a pinch gesture ends
    UFUNCTION()
    void OnUnPinch(const FLeapHandData& HandData);

    // Called when the hand is considered "open" for teleport confirmation
    UFUNCTION()
    void OnTeleportConfirm(const FLeapHandData& HandData);

private:
    bool bIsAiming = false;
};
