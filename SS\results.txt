Build started at 7:39 PM...
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Plugins\ScriptPlugin\Source\ScriptGeneratorUbtPlugin\ScriptGeneratorUbtPlugin.ubtplugin.csproj (in 1.21 sec).
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\Shared\EpicGames.Slack\EpicGames.Slack.csproj (in 1.21 sec).
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\Shared\EpicGames.Core\EpicGames.Core.csproj (in 1.21 sec).
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\AutomationTool\Mutable\RunMutableCommandlet\RunMutableCommandlet.Automation.csproj (in 1.21 sec).
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\Shared\EpicGames.OIDC\EpicGames.OIDC.csproj (in 1.21 sec).
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\Shared\EpicGames.Jupiter\EpicGames.Jupiter.csproj (in 1.21 sec).
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\Shared\EpicGames.UBA\EpicGames.UBA.csproj (in 1.21 sec).
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\Shared\EpicGames.BuildGraph\EpicGames.BuildGraph.csproj (in 1.21 sec).
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\Shared\EpicGames.UHT\EpicGames.UHT.csproj (in 1.22 sec).
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\Shared\EpicGames.AspNet\EpicGames.AspNet.csproj (in 5 ms).
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\Shared\EpicGames.Oodle\EpicGames.Oodle.csproj (in 3 ms).
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\Shared\EpicGames.ProjectStore\EpicGames.ProjectStore.csproj (in 1 ms).
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\Shared\EpicGames.Tracing\EpicGames.Tracing.csproj (in 1 ms).
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\Shared\EpicGames.Perforce.Managed\EpicGames.Perforce.Managed.csproj (in 22 ms).
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\Shared\EpicGames.Redis\EpicGames.Redis.csproj (in 23 ms).
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\Shared\EpicGames.Serialization\EpicGames.Serialization.csproj (in 4 ms).
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\Shared\EpicGames.Perforce\EpicGames.Perforce.csproj (in 4 ms).
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\Shared\EpicGames.Build\EpicGames.Build.csproj (in 26 ms).
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Intermediate\Build\BuildRulesProjects\UE5Rules\UE5Rules.csproj (in 1.28 sec).
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\Shared\EpicGames.IoHash\EpicGames.IoHash.csproj (in 5 ms).
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Intermediate\Build\BuildRulesProjects\UE5ProgramRules\UE5ProgramRules.csproj (in 1.28 sec).
Restored C:\Users\<USER>\Desktop\IlPalazzo\Intermediate\Build\BuildRulesProjects\VectorTestModuleRules\VectorTestModuleRules.csproj (in 43 ms).
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\AutomationTool\AutomationTool.csproj (in 1.28 sec).
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Intermediate\Build\BuildRulesProjects\MarketplaceRules\MarketplaceRules.csproj (in 1.28 sec).
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\Shared\EpicGames.MsBuild\EpicGames.MsBuild.csproj (in 6 ms).
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\AutomationTool\SteamDeck\SteamDeck.Automation.csproj (in 1.3 sec).
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\AutomationTool\Mac\Mac.Automation.csproj (in 1.29 sec).
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\AutomationTool\Localization\Localization.Automation.csproj (in 1.3 sec).
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\AutomationTool\Apple\Apple.Automation.csproj (in 61 ms).
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\AutomationTool\Win\Win.Automation.csproj (in 45 ms).
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\AutomationTool\IOS\IOS.Automation.csproj (in 1.3 sec).
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\Shared\EpicGames.Horde\EpicGames.Horde.csproj (in 15 ms).
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\AutomationTool\CrowdinLocalization\CrowdinLocalization.Automation.csproj (in 1.3 sec).
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\AutomationTool\Scripts\AutomationScripts.Automation.csproj (in 1.3 sec).
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\AutomationTool\CookedEditor\CookedEditor.Automation.csproj (in 1.3 sec).
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\AutomationTool\BuildGraph\BuildGraph.Automation.csproj (in 1.3 sec).
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\AutomationTool\LiveLinkHub\LiveLinkHub.Automation.csproj (in 1.3 sec).
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\AutomationTool\SmartlingLocalization\SmartlingLocalization.Automation.csproj (in 62 ms).
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\AutomationTool\AutomationUtils\AutomationUtils.Automation.csproj (in 1.3 sec).
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\AutomationTool\LowLevelTests\LowLevelTests.Automation.csproj (in 1.3 sec).
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\AutomationTool\Android\Android.Automation.csproj (in 1.3 sec).
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\Shared\EpicGames.Perforce.Fixture\EpicGames.Perforce.Fixture.csproj (in 34 ms).
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\AutomationTool\Gauntlet\Gauntlet.Automation.csproj (in 1.3 sec).
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\AutomationTool\Linux\Linux.Automation.csproj (in 1.3 sec).
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\AutomationTool\OneSkyLocalization\OneSkyLocalization.Automation.csproj (in 1.3 sec).
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\AutomationTool\Turnkey\Turnkey.Automation.csproj (in 1.3 sec).
1>------ Build started: Project: EpicGames.UBA, Configuration: Development Any CPU ------
2>------ Build started: Project: EpicGames.Tracing, Configuration: Development Any CPU ------
3>------ Build started: Project: EpicGames.Slack, Configuration: Development Any CPU ------
4>------ Build started: Project: EpicGames.ProjectStore, Configuration: Development Any CPU ------
5>------ Build started: Project: EpicGames.Oodle, Configuration: Development Any CPU ------
6>------ Build started: Project: EpicGames.OIDC, Configuration: Development Any CPU ------
7>------ Build started: Project: EpicGames.MongoDB, Configuration: Development Any CPU ------
8>------ Build started: Project: EpicGames.Core, Configuration: Development Any CPU ------
9>------ Build started: Project: ScriptGeneratorUbtPlugin.ubtplugin, Configuration: Development Any CPU ------
10>------ Skipped Build: Project: UE5, Configuration: BuiltWithUnrealBuildTool Win64 ------
10>Project not selected to build for this solution configuration 
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\UnrealBuildTool\UnrealBuildTool.csproj (in 31 ms).
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\AutomationTool\TVOS\TVOS.Automation.csproj (in 1.3 sec).
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\Shared\EpicGames.MongoDB\EpicGames.MongoDB.csproj (in 56 ms).
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Platforms\VisionOS\Source\Programs\AutomationTool\VisionOS.Automation.csproj (in 1.3 sec).
Restored C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\AutomationTool\XLocLocalization\XLocLocalization.Automation.csproj (in 1.3 sec).
9>C:\Program Files\Epic Games\UE_5.5\Engine\Plugins\ScriptPlugin\Source\ScriptGeneratorUbtPlugin\ScriptCodeGeneratorBase.cs(12,7,12,22): error CS0246: The type or namespace name 'UnrealBuildTool' could not be found (are you missing a using directive or an assembly reference?)
9>Done building project "ScriptGeneratorUbtPlugin.ubtplugin.csproj" -- FAILED.
4>EpicGames.ProjectStore -> C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\Shared\EpicGames.ProjectStore\bin\Development\net8.0\EpicGames.ProjectStore.dll
5>EpicGames.Oodle -> C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\Shared\EpicGames.Oodle\bin\Development\net8.0\EpicGames.Oodle.dll
1>EpicGames.UBA -> C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\Shared\EpicGames.UBA\bin\Development\net8.0\EpicGames.UBA.dll
6>EpicGames.OIDC -> C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\Shared\EpicGames.OIDC\bin\Development\net8.0\EpicGames.OIDC.dll
8>EpicGames.Core -> C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\Shared\EpicGames.Core\bin\Development\net8.0\EpicGames.Core.dll
11>------ Build started: Project: EpicGames.Perforce, Configuration: Development Any CPU ------
12>------ Build started: Project: EpicGames.MsBuild, Configuration: Development Any CPU ------
13>------ Build started: Project: EpicGames.Jupiter, Configuration: Development Any CPU ------
14>------ Build started: Project: EpicGames.IoHash, Configuration: Development Any CPU ------
13>EpicGames.Jupiter -> C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\Shared\EpicGames.Jupiter\bin\Development\net8.0\EpicGames.Jupiter.dll
7>EpicGames.MongoDB -> C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\Shared\EpicGames.MongoDB\bin\Development\net8.0\EpicGames.MongoDB.dll
3>EpicGames.Slack -> C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\Shared\EpicGames.Slack\bin\Development\net8.0\EpicGames.Slack.dll
14>EpicGames.IoHash -> C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\Shared\EpicGames.IoHash\bin\Development\net8.0\EpicGames.IoHash.dll
15>------ Build started: Project: EpicGames.Serialization, Configuration: Development Any CPU ------
15>EpicGames.Serialization -> C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\Shared\EpicGames.Serialization\bin\Development\net8.0\EpicGames.Serialization.dll
16>------ Build started: Project: EpicGames.Redis, Configuration: Development Any CPU ------
17>------ Build started: Project: EpicGames.Horde, Configuration: Development Any CPU ------
18>------ Build started: Project: EpicGames.BuildGraph, Configuration: Development Any CPU ------
19>------ Build started: Project: EpicGames.AspNet, Configuration: Development Any CPU ------
2>EpicGames.Tracing -> C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\Shared\EpicGames.Tracing\bin\Development\net8.0\EpicGames.Tracing.dll
11>EpicGames.Perforce -> C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\Shared\EpicGames.Perforce\bin\Development\net8.0\EpicGames.Perforce.dll
20>------ Build started: Project: EpicGames.Perforce.Managed, Configuration: Development Any CPU ------
16>EpicGames.Redis -> C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\Shared\EpicGames.Redis\bin\Development\net8.0\EpicGames.Redis.dll
18>EpicGames.BuildGraph -> C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\Shared\EpicGames.BuildGraph\bin\Development\net8.0\EpicGames.BuildGraph.dll
19>EpicGames.AspNet -> C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\Shared\EpicGames.AspNet\bin\Development\net8.0\EpicGames.AspNet.dll
12>EpicGames.MsBuild -> C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\Shared\EpicGames.MsBuild\bin\Development\net8.0\EpicGames.MsBuild.dll
21>------ Build started: Project: EpicGames.Build, Configuration: Development Any CPU ------
21>EpicGames.Build -> C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\Shared\EpicGames.Build\bin\Development\net8.0\EpicGames.Build.dll
22>------ Build started: Project: EpicGames.UHT, Configuration: Development Any CPU ------
20>EpicGames.Perforce.Managed -> C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\Shared\EpicGames.Perforce.Managed\bin\Development\net8.0\EpicGames.Perforce.Managed.dll
23>------ Build started: Project: EpicGames.Perforce.Fixture, Configuration: Development Any CPU ------
17>EpicGames.Horde -> C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\Shared\EpicGames.Horde\bin\Development\net8.0\EpicGames.Horde.dll
22>EpicGames.UHT -> C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\Shared\EpicGames.UHT\bin\Development\net8.0\EpicGames.UHT.dll
24>------ Build started: Project: UnrealBuildTool, Configuration: Development Any CPU ------
23>EpicGames.Perforce.Fixture -> C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\Shared\EpicGames.Perforce.Fixture\bin\Development\net8.0\EpicGames.Perforce.Fixture.dll
24>UnrealBuildTool -> C:\Program Files\Epic Games\UE_5.5\Engine\Binaries\DotNET\UnrealBuildTool\UnrealBuildTool.dll
25>------ Build started: Project: UE5Rules, Configuration: Development Any CPU ------
26>------ Build started: Project: AutomationUtils.Automation, Configuration: Development Any CPU ------
27>------ Build started: Project: AutomationTool, Configuration: Development Any CPU ------
28>------ Build started: Project: VectorTest, Configuration: Development_Editor x64 ------
28>Using bundled DotNet SDK version: 8.0.300
28>Running UnrealBuildTool: dotnet "..\..\Engine\Binaries\DotNET\UnrealBuildTool\UnrealBuildTool.dll" VectorTestEditor Win64 Development -Project="C:\Users\<USER>\Desktop\IlPalazzo\VectorTest.uproject" -WaitMutex -FromMsBuild -architecture=x64
28>Log file: C:\Users\<USER>\AppData\Local\UnrealBuildTool\Log.txt
28>Total execution time: 0.77 seconds
28>Unable to find project 'C:\Users\<USER>\Desktop\IlPalazzo\VectorTest.uproject'.
28>C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Microsoft\VC\v170\Microsoft.MakeFile.Targets(44,5): error MSB3073: The command ""C:\Program Files\Epic Games\UE_5.5\Engine\Build\BatchFiles\Build.bat" VectorTestEditor Win64 Development -Project="C:\Users\<USER>\Desktop\IlPalazzo\VectorTest.uproject" -WaitMutex -FromMsBuild -architecture=x64" exited with code 6.
28>Done building project "VectorTest.vcxproj" -- FAILED.
25>C:\Program Files\Epic Games\UE_5.5\Engine\Source\ThirdParty\astcenc\astcenc.Build.cs(6,14,6,21): warning CS8981: The type name 'astcenc' only contains lower-cased ascii characters. Such names may become reserved for the language.
25>C:\Program Files\Epic Games\UE_5.5\Engine\Source\ThirdParty\mimalloc\mimalloc.Build.cs(6,14,6,22): warning CS8981: The type name 'mimalloc' only contains lower-cased ascii characters. Such names may become reserved for the language.
25>C:\Program Files\Epic Games\UE_5.5\Engine\Source\ThirdParty\coremod\coremod.Build.cs(6,14,6,21): warning CS8981: The type name 'coremod' only contains lower-cased ascii characters. Such names may become reserved for the language.
25>C:\Program Files\Epic Games\UE_5.5\Engine\Source\ThirdParty\libpas\src\libpas\libpas.Build.cs(7,14,7,20): warning CS8981: The type name 'libpas' only contains lower-cased ascii characters. Such names may become reserved for the language.
25>C:\Program Files\Epic Games\UE_5.5\Engine\Source\ThirdParty\nanoflann\nanoflann.Build.cs(6,14,6,23): warning CS8981: The type name 'nanoflann' only contains lower-cased ascii characters. Such names may become reserved for the language.
25>C:\Program Files\Epic Games\UE_5.5\Engine\Source\ThirdParty\libstrophe\libstrophe.Build.cs(6,14,6,24): warning CS8981: The type name 'libstrophe' only contains lower-cased ascii characters. Such names may become reserved for the language.
25>C:\Program Files\Epic Games\UE_5.5\Engine\Source\ThirdParty\heapprofd\heapprofd.Build.cs(6,14,6,23): warning CS8981: The type name 'heapprofd' only contains lower-cased ascii characters. Such names may become reserved for the language.
25>C:\Program Files\Epic Games\UE_5.5\Engine\Source\ThirdParty\jemalloc\jemalloc.Build.cs(5,14,5,22): warning CS8981: The type name 'jemalloc' only contains lower-cased ascii characters. Such names may become reserved for the language.
25>C:\Program Files\Epic Games\UE_5.5\Engine\Source\ThirdParty\libzip\libzip.Build.cs(6,14,6,20): warning CS8981: The type name 'libzip' only contains lower-cased ascii characters. Such names may become reserved for the language.
25>C:\Program Files\Epic Games\UE_5.5\Engine\Source\ThirdParty\metis\metis.Build.cs(5,14,5,19): warning CS8981: The type name 'metis' only contains lower-cased ascii characters. Such names may become reserved for the language.
25>C:\Program Files\Epic Games\UE_5.5\Engine\Source\ThirdParty\portmidi\portmidi.Build.cs(5,14,5,22): warning CS8981: The type name 'portmidi' only contains lower-cased ascii characters. Such names may become reserved for the language.
25>C:\Program Files\Epic Games\UE_5.5\Engine\Source\ThirdParty\xxhash\xxhash.Build.cs(6,14,6,20): warning CS8981: The type name 'xxhash' only contains lower-cased ascii characters. Such names may become reserved for the language.
25>C:\Program Files\Epic Games\UE_5.5\Engine\Source\ThirdParty\zlib\zlib.Build.cs(6,14,6,18): warning CS8981: The type name 'zlib' only contains lower-cased ascii characters. Such names may become reserved for the language.
25>C:\Program Files\Epic Games\UE_5.5\Engine\Source\ThirdParty\libav\libav.Build.cs(7,14,7,19): warning CS8981: The type name 'libav' only contains lower-cased ascii characters. Such names may become reserved for the language.
25>C:\Program Files\Epic Games\UE_5.5\Engine\Source\ThirdParty\libcurl\libcurl.Build.cs(6,14,6,21): warning CS8981: The type name 'libcurl' only contains lower-cased ascii characters. Such names may become reserved for the language.
25>UE5Rules -> C:\Program Files\Epic Games\UE_5.5\Engine\Intermediate\Build\BuildRulesProjects\UE5Rules\bin\Development\UE5Rules.dll
25>Done building project "UE5Rules.csproj".
29>------ Build started: Project: UE5ProgramRules, Configuration: Development Any CPU ------
27>AutomationTool -> C:\Program Files\Epic Games\UE_5.5\Engine\Binaries\DotNET\AutomationTool\AutomationTool.dll
26>AutomationUtils.Automation -> C:\Program Files\Epic Games\UE_5.5\Engine\Binaries\DotNET\AutomationTool\AutomationUtils\AutomationUtils.Automation.dll
30>------ Build started: Project: SteamDeck.Automation, Configuration: Development Any CPU ------
31>------ Build started: Project: Localization.Automation, Configuration: Development Any CPU ------
32>------ Build started: Project: BuildGraph.Automation, Configuration: Development Any CPU ------
33>------ Build started: Project: Apple.Automation, Configuration: Development Any CPU ------
29>UE5ProgramRules -> C:\Program Files\Epic Games\UE_5.5\Engine\Intermediate\Build\BuildRulesProjects\UE5ProgramRules\bin\Development\UE5ProgramRules.dll
34>------ Build started: Project: MarketplaceRules, Configuration: Development Any CPU ------
30>SteamDeck.Automation -> C:\Program Files\Epic Games\UE_5.5\Engine\Binaries\DotNET\AutomationTool\AutomationScripts\Platforms\SteamDeck\SteamDeck.Automation.dll
35>------ Build started: Project: Linux.Automation, Configuration: Development Any CPU ------
33>Apple.Automation -> C:\Program Files\Epic Games\UE_5.5\Engine\Binaries\DotNET\AutomationTool\AutomationScripts\Platforms\Apple\Apple.Automation.dll
36>------ Build started: Project: Mac.Automation, Configuration: Development Any CPU ------
37>------ Build started: Project: IOS.Automation, Configuration: Development Any CPU ------
31>Localization.Automation -> C:\Program Files\Epic Games\UE_5.5\Engine\Binaries\DotNET\AutomationTool\AutomationScripts\Localization\Localization.Automation.dll
38>------ Build started: Project: XLocLocalization.Automation, Configuration: Development Any CPU ------
39>------ Build started: Project: SmartlingLocalization.Automation, Configuration: Development Any CPU ------
40>------ Build started: Project: AutomationScripts.Automation, Configuration: Development Any CPU ------
41>------ Build started: Project: OneSkyLocalization.Automation, Configuration: Development Any CPU ------
42>------ Build started: Project: CrowdinLocalization.Automation, Configuration: Development Any CPU ------
34>MarketplaceRules -> C:\Program Files\Epic Games\UE_5.5\Engine\Intermediate\Build\BuildRulesProjects\MarketplaceRules\bin\Development\MarketplaceRules.dll
43>------ Build started: Project: VectorTestModuleRules, Configuration: Development Any CPU ------
41>OneSkyLocalization.Automation -> C:\Program Files\Epic Games\UE_5.5\Engine\Binaries\DotNET\AutomationTool\AutomationScripts\OneSkyLocalization\OneSkyLocalization.Automation.dll
36>Mac.Automation -> C:\Program Files\Epic Games\UE_5.5\Engine\Binaries\DotNET\AutomationTool\AutomationScripts\Platforms\Mac\Mac.Automation.dll
35>Linux.Automation -> C:\Program Files\Epic Games\UE_5.5\Engine\Binaries\DotNET\AutomationTool\AutomationScripts\Platforms\Linux\Linux.Automation.dll
37>IOS.Automation -> C:\Program Files\Epic Games\UE_5.5\Engine\Binaries\DotNET\AutomationTool\AutomationScripts\Platforms\IOS\IOS.Automation.dll
44>------ Build started: Project: TVOS.Automation, Configuration: Development Any CPU ------
42>CrowdinLocalization.Automation -> C:\Program Files\Epic Games\UE_5.5\Engine\Binaries\DotNET\AutomationTool\AutomationScripts\CrowdinLocalization\CrowdinLocalization.Automation.dll
39>SmartlingLocalization.Automation -> C:\Program Files\Epic Games\UE_5.5\Engine\Binaries\DotNET\AutomationTool\AutomationScripts\SmartlingLocalization\SmartlingLocalization.Automation.dll
38>XLocLocalization.Automation -> C:\Program Files\Epic Games\UE_5.5\Engine\Binaries\DotNET\AutomationTool\AutomationScripts\XLocLocalization\XLocLocalization.Automation.dll
43>VectorTestModuleRules -> C:\Users\<USER>\Desktop\IlPalazzo\Intermediate\Build\BuildRulesProjects\VectorTestModuleRules\bin\Development\VectorTestModuleRules.dll
44>TVOS.Automation -> C:\Program Files\Epic Games\UE_5.5\Engine\Binaries\DotNET\AutomationTool\AutomationScripts\Platforms\TVOS\TVOS.Automation.dll
40>AutomationScripts.Automation -> C:\Program Files\Epic Games\UE_5.5\Engine\Binaries\DotNET\AutomationTool\AutomationScripts\Scripts\AutomationScripts.Automation.dll
45>------ Build started: Project: VisionOS.Automation, Configuration: Development Any CPU ------
46>------ Build started: Project: Android.Automation, Configuration: Development Any CPU ------
45>VisionOS.Automation -> C:\Program Files\Epic Games\UE_5.5\Engine\Platforms\VisionOS\Binaries\DotNET\AutomationTool\AutomationScripts\VisionOS.Automation.dll
46>Android.Automation -> C:\Program Files\Epic Games\UE_5.5\Engine\Binaries\DotNET\AutomationTool\AutomationScripts\Platforms\Android\Android.Automation.dll
47>------ Build started: Project: Gauntlet.Automation, Configuration: Development Any CPU ------
32>BuildGraph.Automation -> C:\Program Files\Epic Games\UE_5.5\Engine\Binaries\DotNET\AutomationTool\AutomationScripts\BuildGraph\BuildGraph.Automation.dll
48>------ Build started: Project: Win.Automation, Configuration: Development Any CPU ------
47>Gauntlet.Automation -> C:\Program Files\Epic Games\UE_5.5\Engine\Binaries\DotNET\AutomationTool\AutomationScripts\Gauntlet\Gauntlet.Automation.dll
49>------ Build started: Project: Turnkey.Automation, Configuration: Development Any CPU ------
50>------ Build started: Project: RunMutableCommandlet.Automation, Configuration: Development Any CPU ------
51>------ Build started: Project: LowLevelTests.Automation, Configuration: Development Any CPU ------
48>Win.Automation -> C:\Program Files\Epic Games\UE_5.5\Engine\Binaries\DotNET\AutomationTool\AutomationScripts\Platforms\Windows\Win.Automation.dll
52>------ Build started: Project: CookedEditor.Automation, Configuration: Development Any CPU ------
50>RunMutableCommandlet.Automation -> C:\Program Files\Epic Games\UE_5.5\Engine\Binaries\DotNET\AutomationTool\AutomationScripts\Mutable\RunMutableCommandlet.Automation.dll
51>LowLevelTests.Automation -> C:\Program Files\Epic Games\UE_5.5\Engine\Binaries\DotNET\AutomationTool\LowLevelTests\LowLevelTests.Automation.dll
49>Turnkey.Automation -> C:\Program Files\Epic Games\UE_5.5\Engine\Binaries\DotNET\AutomationTool\AutomationScripts\Turnkey\Turnkey.Automation.dll
52>CookedEditor.Automation -> C:\Program Files\Epic Games\UE_5.5\Engine\Binaries\DotNET\AutomationTool\AutomationScripts\CookedEditor\CookedEditor.Automation.dll
53>------ Build started: Project: LiveLinkHub.Automation, Configuration: Development Any CPU ------
53>LiveLinkHub.Automation -> C:\Program Files\Epic Games\UE_5.5\Engine\Binaries\DotNET\AutomationTool\AutomationScripts\LiveLinkHub\LiveLinkHub.Automation.dll
========== Build: 50 succeeded, 2 failed, 0 up-to-date, 1 skipped ==========
========== Build completed at 7:40 PM and took 27.595 seconds ==========
