/**
 * Debug Remote Control connection
 * Used by VS Code launch configuration for troubleshooting
 */

const { UE5RemoteControl } = require('./ue-remote-control-client');
const axios = require('axios');

async function debugConnection() {
    const host = process.env.UE_REMOTE_CONTROL_HOST || 'localhost';
    const port = parseInt(process.env.UE_REMOTE_CONTROL_PORT) || 30010;
    
    console.log('🔍 UE5 Remote Control Connection Debug');
    console.log('=====================================');
    console.log(`Host: ${host}`);
    console.log(`Port: ${port}`);
    console.log(`URL: http://${host}:${port}`);
    console.log('');
    
    // Test 1: Basic HTTP connectivity
    console.log('1️⃣ Testing basic HTTP connectivity...');
    try {
        const response = await axios.get(`http://${host}:${port}`, { timeout: 5000 });
        console.log(`   ✅ HTTP server responding (status: ${response.status})`);
    } catch (error) {
        if (error.code === 'ECONNREFUSED') {
            console.log('   ❌ Connection refused - server not running');
            console.log('   💡 Make sure UE5.5 is running with Remote Control enabled');
            return;
        } else if (error.response?.status === 404) {
            console.log('   ✅ HTTP server responding (404 expected for root path)');
        } else {
            console.log(`   ⚠️  HTTP error: ${error.message}`);
        }
    }
    
    // Test 2: Remote Control API endpoints
    console.log('');
    console.log('2️⃣ Testing Remote Control API endpoints...');
    
    const ue = new UE5RemoteControl(host, port);
    
    try {
        const connected = await ue.connect();
        if (connected) {
            console.log('   ✅ Remote Control API responding');
        } else {
            console.log('   ❌ Remote Control API not responding');
            return;
        }
    } catch (error) {
        console.log(`   ❌ API connection failed: ${error.message}`);
        return;
    }
    
    // Test 3: Basic property access
    console.log('');
    console.log('3️⃣ Testing basic property access...');
    try {
        // Try a simpler, more reliable property first
        const result = await ue.getProperty('/Game', 'Name');
        console.log('   ✅ Can read basic properties');
        console.log(`   📊 Game Name: ${JSON.stringify(result)}`);
    } catch (error) {
        console.log(`   ❌ Property access failed: ${error.message}`);
        console.log('   💡 This is normal if no specific game objects are exposed');
    }
    
    // Test 4: Engine stats
    console.log('');
    console.log('4️⃣ Testing engine statistics...');
    try {
        const stats = await ue.getEngineStats();
        if (stats) {
            console.log('   ✅ Can read engine statistics');
            console.log(`   📈 Average FPS: ${stats.fps?.toFixed(1) || 'N/A'}`);
        } else {
            console.log('   ⚠️  Engine statistics not available');
        }
    } catch (error) {
        console.log(`   ❌ Statistics access failed: ${error.message}`);
    }
    
    // Test 5: Common object paths
    console.log('');
    console.log('5️⃣ Testing common object paths...');
    
    const commonPaths = [
        '/Game/ThirdPersonBP/Blueprints/ThirdPersonCharacter',
        '/Game/Maps/ThirdPersonExampleMap:PersistentLevel.DirectionalLight_0',
        '/Game/Maps/ThirdPersonExampleMap:PersistentLevel.SkySphereBlueprint'
    ];
    
    for (const objectPath of commonPaths) {
        try {
            await ue.getProperty(objectPath, 'ActorLocation');
            console.log(`   ✅ ${objectPath}`);
        } catch (error) {
            console.log(`   ❌ ${objectPath} - ${error.message}`);
        }
    }
    
    // Test 6: Function calling
    console.log('');
    console.log('6️⃣ Testing function calling...');
    try {
        // Try a basic function call - this may fail if no functions are exposed
        console.log('   ℹ️  Function calling requires exposed Blueprint functions');
        console.log('   ℹ️  Create a Remote Control Preset in UE5.5 to expose functions');
    } catch (error) {
        console.log(`   ❌ Function calling failed: ${error.message}`);
    }
    
    console.log('');
    console.log('🎯 Debug Summary:');
    console.log('================');
    console.log('If all tests pass, Remote Control is working correctly!');
    console.log('');
    console.log('Common issues and solutions:');
    console.log('• Connection refused → Start UE5.5 and enable Remote Control plugin');
    console.log('• API not responding → Run "WebControl.StartServer" in UE5.5 console');
    console.log('• Object paths fail → Run "node automation/discover-objects.js" to find correct paths');
    console.log('• Function calls fail → Create Remote Control presets to expose functions');
    console.log('');
    console.log('Next steps:');
    console.log('• Run: node automation/discover-objects.js');
    console.log('• See: PRPs/ue5-remote-control-setup.md');
}

debugConnection().catch(error => {
    console.error('❌ Debug failed:', error.message);
    process.exit(1);
});
