/**
 * Discover available objects in UE5.5 Remote Control
 * This script helps you find the correct object paths for your project
 */

const { UE5RemoteControl } = require('./ue-remote-control-client');

async function discoverObjects() {
    console.log('🔍 UE5.5 Remote Control Object Discovery');
    console.log('========================================');
    
    const ue = new UE5RemoteControl();
    
    // Test connection
    const connected = await ue.connect();
    if (!connected) {
        console.error('❌ Cannot connect to UE5.5. Make sure:');
        console.error('   1. UE5.5 is running');
        console.error('   2. Remote Control API plugin is enabled');
        console.error('   3. WebControl.StartServer was executed');
        process.exit(1);
    }
    
    console.log('');
    console.log('1️⃣ Checking Remote Control Presets...');
    try {
        const presets = await ue.getRemoteControlPresets();
        if (presets && presets.length > 0) {
            console.log(`   ✅ Found ${presets.length} Remote Control preset(s):`);
            for (const preset of presets) {
                console.log(`      📋 ${preset.Name || preset.name || 'Unnamed Preset'}`);
                
                // Get detailed info about this preset
                const presetInfo = await ue.getPresetInfo(preset.Name || preset.name);
                if (presetInfo) {
                    console.log(`         Objects: ${presetInfo.ExposedObjects?.length || 0}`);
                    console.log(`         Properties: ${presetInfo.ExposedProperties?.length || 0}`);
                    console.log(`         Functions: ${presetInfo.ExposedFunctions?.length || 0}`);
                }
            }
        } else {
            console.log('   ⚠️  No Remote Control presets found');
            console.log('   💡 Create a Remote Control preset in UE5.5 to expose objects:');
            console.log('      1. Window → Remote Control → Remote Control Panel');
            console.log('      2. Create a new preset');
            console.log('      3. Drag objects from World Outliner to expose them');
        }
    } catch (error) {
        console.log(`   ❌ Error checking presets: ${error.message}`);
    }
    
    console.log('');
    console.log('2️⃣ Testing common object paths...');
    
    const testPaths = [
        // World and engine objects
        { path: '/Engine/World', property: 'WorldSettings', description: 'World Settings' },
        { path: '/Engine/World', property: 'AuthorityGameMode', description: 'Game Mode' },
        { path: '/Engine/World', property: 'FirstPlayerController', description: 'Player Controller' },
        
        // Common Third Person template objects
        { path: '/Game/ThirdPersonBP/Blueprints/ThirdPersonCharacter', property: 'ActorLocation', description: 'Third Person Character' },
        { path: '/Game/Maps/ThirdPersonExampleMap:PersistentLevel.DirectionalLight_0', property: 'Intensity', description: 'Directional Light' },
        { path: '/Game/Maps/ThirdPersonExampleMap:PersistentLevel.SkySphereBlueprint', property: 'ActorLocation', description: 'Sky Sphere' },
        
        // Alternative common paths
        { path: '/Game/Maps/Level:PersistentLevel.DirectionalLight_0', property: 'Intensity', description: 'Directional Light (alternative)' },
        { path: '/Game/Maps/Level:PersistentLevel.SkySphere', property: 'ActorLocation', description: 'Sky Sphere (alternative)' },
    ];
    
    const workingPaths = [];
    
    for (const test of testPaths) {
        try {
            const result = await ue.getProperty(test.path, test.property);
            console.log(`   ✅ ${test.description}: ${test.path}`);
            workingPaths.push(test);
        } catch (error) {
            console.log(`   ❌ ${test.description}: ${test.path} - ${error.message}`);
        }
    }
    
    console.log('');
    console.log('3️⃣ Summary and Recommendations');
    console.log('===============================');
    
    if (workingPaths.length > 0) {
        console.log(`✅ Found ${workingPaths.length} working object path(s):`);
        console.log('');
        console.log('// Copy these paths to your automation scripts:');
        console.log('const WORKING_PATHS = {');
        for (const path of workingPaths) {
            const varName = path.description.toUpperCase().replace(/[^A-Z0-9]/g, '_');
            console.log(`    ${varName}: '${path.path}',`);
        }
        console.log('};');
        console.log('');
    } else {
        console.log('❌ No working object paths found.');
    }
    
    console.log('💡 To expose more objects for Remote Control:');
    console.log('');
    console.log('1. **Create Remote Control Preset:**');
    console.log('   - Window → Remote Control → Remote Control Panel');
    console.log('   - Click "+" to create new preset');
    console.log('   - Name it (e.g., "MyProjectControl")');
    console.log('');
    console.log('2. **Expose Objects:**');
    console.log('   - Open World Outliner (Window → World Outliner)');
    console.log('   - Drag actors from World Outliner to Remote Control Panel');
    console.log('   - Select properties and functions to expose');
    console.log('');
    console.log('3. **Expose Blueprint Functions:**');
    console.log('   - Mark functions as "BlueprintCallable" in C++');
    console.log('   - Or create Blueprint functions with "Call in Editor" enabled');
    console.log('');
    console.log('4. **Test Your Setup:**');
    console.log('   - Run this script again to see newly exposed objects');
    console.log('   - Use node automation/debug-connection.js for detailed testing');
    console.log('');
    console.log('📚 For more help, see: PRPs/ue5-remote-control-setup.md');
}

// Handle errors gracefully
process.on('unhandledRejection', (error) => {
    console.error('❌ Unhandled error:', error.message);
    process.exit(1);
});

discoverObjects().catch(error => {
    console.error('❌ Discovery failed:', error.message);
    process.exit(1);
});
