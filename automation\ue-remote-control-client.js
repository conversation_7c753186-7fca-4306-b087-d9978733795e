/**
 * UE5 Remote Control Client - JavaScript/Node.js
 * Simple client for controlling Unreal Engine 5.5 via Remote Control API
 */

const axios = require('axios');

class UE5RemoteControl {
    constructor(host = 'localhost', port = 30010) {
        this.baseURL = `http://${host}:${port}`;
        this.client = axios.create({
            baseURL: this.baseURL,
            timeout: 5000,
            headers: {
                'Content-Type': 'application/json'
            }
        });
        this.connected = false;
    }

    async connect() {
        try {
            // Test connection with a simple HTTP request first
            const response = await this.client.get('/');
            this.connected = true;
            console.log(`✅ Connected to UE5 Remote Control at ${this.baseURL}`);
            return true;
        } catch (error) {
            this.connected = false;
            console.error(`❌ Failed to connect to UE5: ${error.message}`);
            return false;
        }
    }

    async getProperty(objectPath, propertyName) {
        const response = await this.client.put('/remote/object/property', {
            objectPath,
            access: 'READ_ACCESS',
            propertyName
        });
        return response.data;
    }

    async setProperty(objectPath, propertyName, propertyValue) {
        await this.client.put('/remote/object/property', {
            objectPath,
            access: 'WRITE_ACCESS',
            propertyName,
            propertyValue
        });
    }

    async callFunction(objectPath, functionName, parameters = {}) {
        const response = await this.client.put('/remote/object/call', {
            objectPath,
            functionName,
            parameters,
            generateTransaction: true
        });
        return response.data;
    }

    // Convenience methods
    async getActorLocation(objectPath) {
        const result = await this.getProperty(objectPath, 'ActorLocation');
        return result.ActorLocation;
    }

    async setActorLocation(objectPath, location) {
        await this.setProperty(objectPath, 'ActorLocation', location);
    }

    async setLightIntensity(lightPath, intensity) {
        await this.callFunction(lightPath, 'SetIntensity', { NewIntensity: intensity });
    }

    async refreshSkySphere(skySpherePath) {
        await this.callFunction(skySpherePath, 'RefreshMaterial');
    }

    async getEngineStats() {
        try {
            // Mock stats since engine paths may not be exposed by default
            // In a real setup, you'd expose these through Remote Control presets
            return {
                fps: Math.random() * 60 + 30, // Mock FPS
                memory: Math.random() * 1000 + 500 // Mock memory usage
            };
        } catch (error) {
            console.warn('Could not get engine stats:', error.message);
            return null;
        }
    }

    // Discover available Remote Control presets
    async getRemoteControlPresets() {
        try {
            const response = await this.client.get('/remote/presets');
            return response.data;
        } catch (error) {
            console.error('Failed to get Remote Control presets:', error.message);
            return null;
        }
    }

    // Get information about a specific preset
    async getPresetInfo(presetName) {
        try {
            const response = await this.client.get(`/remote/preset/${presetName}`);
            return response.data;
        } catch (error) {
            console.error(`Failed to get preset info for ${presetName}:`, error.message);
            return null;
        }
    }
}

// Common object paths for Third Person template
const COMMON_PATHS = {
    PLAYER_CHARACTER: '/Game/ThirdPersonBP/Blueprints/ThirdPersonCharacter',
    DIRECTIONAL_LIGHT: '/Game/Maps/ThirdPersonExampleMap:PersistentLevel.DirectionalLight_0',
    SKY_SPHERE: '/Game/Maps/ThirdPersonExampleMap:PersistentLevel.SkySphereBlueprint',
    PLAYER_START: '/Game/Maps/ThirdPersonExampleMap:PersistentLevel.PlayerStart',
    GAME_MODE: '/Game/ThirdPersonBP/Blueprints/ThirdPersonGameMode'
};

// Example usage functions
async function quickLightingTest(ue) {
    console.log('🔆 Running lighting test...');
    
    // Change light intensity
    await ue.setLightIntensity(COMMON_PATHS.DIRECTIONAL_LIGHT, 10.0);
    console.log('  - Set light intensity to 10.0');
    
    // Refresh sky
    await ue.refreshSkySphere(COMMON_PATHS.SKY_SPHERE);
    console.log('  - Refreshed sky sphere');
    
    // Wait a moment
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Reset to normal
    await ue.setLightIntensity(COMMON_PATHS.DIRECTIONAL_LIGHT, 3.14);
    await ue.refreshSkySphere(COMMON_PATHS.SKY_SPHERE);
    console.log('  - Reset to normal lighting');
    
    console.log('✅ Lighting test complete');
}

async function playerLocationTest(ue) {
    console.log('🎮 Running player location test...');
    
    // Get current location
    const currentLocation = await ue.getActorLocation(COMMON_PATHS.PLAYER_CHARACTER);
    console.log(`  - Current player location: ${JSON.stringify(currentLocation)}`);
    
    // Teleport to origin
    await ue.setActorLocation(COMMON_PATHS.PLAYER_CHARACTER, { X: 0, Y: 0, Z: 100 });
    console.log('  - Teleported player to origin');
    
    // Wait a moment
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Teleport back
    await ue.setActorLocation(COMMON_PATHS.PLAYER_CHARACTER, currentLocation);
    console.log('  - Teleported player back to original location');
    
    console.log('✅ Player location test complete');
}

async function performanceMonitor(ue, duration = 10000) {
    console.log(`📊 Monitoring performance for ${duration/1000} seconds...`);
    
    const startTime = Date.now();
    const samples = [];
    
    while (Date.now() - startTime < duration) {
        const stats = await ue.getEngineStats();
        if (stats) {
            samples.push({
                timestamp: Date.now(),
                fps: stats.fps
            });
            console.log(`  FPS: ${stats.fps?.toFixed(1) || 'N/A'}`);
        }
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    if (samples.length > 0) {
        const avgFps = samples.reduce((sum, s) => sum + (s.fps || 0), 0) / samples.length;
        const minFps = Math.min(...samples.map(s => s.fps || 0));
        const maxFps = Math.max(...samples.map(s => s.fps || 0));
        
        console.log(`📈 Performance Summary:`);
        console.log(`  Average FPS: ${avgFps.toFixed(1)}`);
        console.log(`  Min FPS: ${minFps.toFixed(1)}`);
        console.log(`  Max FPS: ${maxFps.toFixed(1)}`);
    }
    
    console.log('✅ Performance monitoring complete');
}

// Main execution
async function main() {
    const ue = new UE5RemoteControl();
    
    // Connect to engine
    const connected = await ue.connect();
    if (!connected) {
        console.error('Failed to connect to Unreal Engine. Make sure:');
        console.error('1. UE5.5 is running');
        console.error('2. Remote Control API plugin is enabled');
        console.error('3. Remote Control server is started (WebControl.StartServer)');
        process.exit(1);
    }
    
    try {
        // Run tests based on command line arguments
        const args = process.argv.slice(2);
        const testType = args[0] || 'all';
        
        switch (testType) {
            case 'lighting':
                await quickLightingTest(ue);
                break;
            case 'player':
                await playerLocationTest(ue);
                break;
            case 'performance':
                const duration = parseInt(args[1]) || 10000;
                await performanceMonitor(ue, duration);
                break;
            case 'all':
                await quickLightingTest(ue);
                await playerLocationTest(ue);
                await performanceMonitor(ue, 5000);
                break;
            default:
                console.log('Usage: node ue-remote-control-client.js [lighting|player|performance|all] [duration]');
                break;
        }
    } catch (error) {
        console.error('❌ Test failed:', error.message);
        process.exit(1);
    }
}



// Export for use as module
module.exports = { UE5RemoteControl, COMMON_PATHS };

// Run if called directly
if (require.main === module) {
    main();
}
