{"FileVersion": 3, "bIsPluginExtension": true, "Modules": [{"Name": "StreamlineShaders", "Type": "Runtime", "LoadingPhase": "PostConfigInit", "PlatformAllowList": ["WinGDK"]}, {"Name": "StreamlineCore", "Type": "Runtime", "LoadingPhase": "PostEngineInit", "PlatformAllowList": ["WinGDK"]}, {"Name": "StreamlineRHI", "Type": "Runtime", "LoadingPhase": "PostSplashScreen", "PlatformAllowList": ["WinGDK"]}, {"Name": "StreamlineD3D11RHI", "Type": "Runtime", "LoadingPhase": "None", "PlatformAllowList": ["WinGDK"]}, {"Name": "StreamlineD3D12RHI", "Type": "Runtime", "LoadingPhase": "None", "PlatformAllowList": ["WinGDK"]}]}