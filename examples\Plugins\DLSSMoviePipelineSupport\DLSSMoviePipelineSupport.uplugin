{"FileVersion": 3, "Version": 28, "VersionName": "8.1.0", "FriendlyName": "Movie Render Queue DLSS/DLAA Support", "Description": "Plugin that adds DLSS/DLAA support to Movie Render Queue.", "Category": "Rendering", "CreatedBy": "NVIDIA", "CreatedByURL": "https://developer.nvidia.com/dlss", "DocsURL": "", "MarketplaceURL": "https://www.unrealengine.com/marketplace/en-US/product/nvidia-dlss", "SupportURL": "mailto:<EMAIL>", "EngineVersion": "5.5.0", "CanContainContent": true, "Installed": true, "Modules": [{"Name": "DLSSMoviePipelineSupport", "Type": "Runtime", "LoadingPhase": "PostEngineInit", "PlatformAllowList": ["Win64"]}], "Plugins": [{"Name": "MovieRenderPipeline", "Enabled": true}, {"Name": "DLSS", "Enabled": true}]}