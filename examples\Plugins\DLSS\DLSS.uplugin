{"FileVersion": 3, "Version": 50, "VersionName": "8.1.0-NGX310.2.1.0", "FriendlyName": "NVIDIA DLSS Super Resolution/Ray Reconstruction/DLAA", "Description": "DLSS Super Resolution boosts frame rates by rendering fewer pixels and using AI to output high resolution frames. DLSS Ray Reconstruction enhances image quality by generating high-quality pixels between sampled rays for intensive ray traced content. DLAA is an AI-based anti-aliasing mode to improve image quality.", "Category": "Rendering", "CreatedBy": "NVIDIA", "CreatedByURL": "https://developer.nvidia.com/dlss", "DocsURL": "", "MarketplaceURL": "https://www.unrealengine.com/marketplace/en-US/product/nvidia-dlss", "SupportURL": "mailto:<EMAIL>", "EngineVersion": "5.5.0", "CanContainContent": true, "Installed": true, "Modules": [{"Name": "DLSSUtility", "Type": "Runtime", "LoadingPhase": "PostConfigInit", "PlatformAllowList": ["Win64"]}, {"Name": "DLSS", "Type": "Runtime", "LoadingPhase": "PostEngineInit", "PlatformAllowList": ["Win64"]}, {"Name": "DLSSBlueprint", "Type": "Runtime", "LoadingPhase": "PostConfigInit"}, {"Name": "NGXRHI", "Type": "Runtime", "LoadingPhase": "PostEngineInit", "PlatformAllowList": ["Win64"]}, {"Name": "NGXD3D11RHI", "Type": "Runtime", "LoadingPhase": "PostEngineInit", "PlatformAllowList": ["Win64"]}, {"Name": "NGXD3D12RHI", "Type": "Runtime", "LoadingPhase": "PostEngineInit", "PlatformAllowList": ["Win64"]}, {"Name": "NGXVulkanRHIPreInit", "Type": "Runtime", "LoadingPhase": "PostConfigInit", "PlatformAllowList": ["Win64"]}, {"Name": "NGXVulkanRHI", "Type": "Runtime", "LoadingPhase": "PostEngineInit", "PlatformAllowList": ["Win64"]}, {"Name": "DLSSEditor", "Type": "Editor", "LoadingPhase": "PostEngineInit", "PlatformAllowList": ["Win64"]}]}