// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeDLSS_init() {}
	static FPackageRegistrationInfo Z_Registration_Info_UPackage__Script_DLSS;
	FORCENOINLINE UPackage* Z_Construct_UPackage__Script_DLSS()
	{
		if (!Z_Registration_Info_UPackage__Script_DLSS.OuterSingleton)
		{
			static const UECodeGen_Private::FPackageParams PackageParams = {
				"/Script/DLSS",
				nullptr,
				0,
				PKG_CompiledIn | 0x00000000,
				0x4799A4EE,
				0x9E17DD1B,
				METADATA_PARAMS(0, nullptr)
			};
			UECodeGen_Private::ConstructUPackage(Z_Registration_Info_UPackage__Script_DLSS.OuterSingleton, PackageParams);
		}
		return Z_Registration_Info_UPackage__Script_DLSS.OuterSingleton;
	}
	static FRegisterCompiledInInfo Z_CompiledInDeferPackage_UPackage__Script_DLSS(Z_Construct_UPackage__Script_DLSS, TEXT("/Script/DLSS"), Z_Registration_Info_UPackage__Script_DLSS, CONSTRUCT_RELOAD_VERSION_INFO(FPackageReloadVersionInfo, 0x4799A4EE, 0x9E17DD1B));
PRAGMA_ENABLE_DEPRECATION_WARNINGS
