// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeNISBlueprint_init() {}
	static FPackageRegistrationInfo Z_Registration_Info_UPackage__Script_NISBlueprint;
	FORCENOINLINE UPackage* Z_Construct_UPackage__Script_NISBlueprint()
	{
		if (!Z_Registration_Info_UPackage__Script_NISBlueprint.OuterSingleton)
		{
			static const UECodeGen_Private::FPackageParams PackageParams = {
				"/Script/NISBlueprint",
				nullptr,
				0,
				PKG_CompiledIn | 0x00000000,
				0x161AB1EC,
				0x4FFFA4C6,
				METADATA_PARAMS(0, nullptr)
			};
			UECodeGen_Private::ConstructUPackage(Z_Registration_Info_UPackage__Script_NISBlueprint.OuterSingleton, PackageParams);
		}
		return Z_Registration_Info_UPackage__Script_NISBlueprint.OuterSingleton;
	}
	static FRegisterCompiledInInfo Z_CompiledInDeferPackage_UPackage__Script_NISBlueprint(Z_Construct_UPackage__Script_NISBlueprint, TEXT("/Script/NISBlueprint"), Z_Registration_Info_UPackage__Script_NISBlueprint, CONSTRUCT_RELOAD_VERSION_INFO(FPackageReloadVersionInfo, 0x161AB1EC, 0x4FFFA4C6));
PRAGMA_ENABLE_DEPRECATION_WARNINGS
