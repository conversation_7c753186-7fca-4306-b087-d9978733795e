{"FileVersion": 3, "Version": 10, "VersionName": "8.1.0", "FriendlyName": "NVIDIA Image Scaling (NIS)", "Description": "NVIDIA Image Scaling boosts frame rates using GPU scaling and sharpening.", "Category": "Rendering", "CreatedBy": "NVIDIA", "CreatedByURL": "https://developer.nvidia.com/image-scaling", "DocsURL": "", "MarketplaceURL": "https://www.unrealengine.com/marketplace/en-US/product/nvidia-dlss", "SupportURL": "mailto:<EMAIL>", "EngineVersion": "5.5.0", "CanContainContent": false, "Installed": true, "Modules": [{"Name": "NISCore", "Type": "Runtime", "LoadingPhase": "PostEngineInit"}, {"Name": "NISShaders", "Type": "Runtime", "LoadingPhase": "PostConfigInit"}, {"Name": "NISBlueprint", "Type": "Runtime", "LoadingPhase": "PostConfigInit"}]}