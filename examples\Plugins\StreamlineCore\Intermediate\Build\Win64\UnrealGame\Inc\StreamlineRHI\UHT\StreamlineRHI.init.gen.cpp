// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeStreamlineRHI_init() {}
	static FPackageRegistrationInfo Z_Registration_Info_UPackage__Script_StreamlineRHI;
	FORCENOINLINE UPackage* Z_Construct_UPackage__Script_StreamlineRHI()
	{
		if (!Z_Registration_Info_UPackage__Script_StreamlineRHI.OuterSingleton)
		{
			static const UECodeGen_Private::FPackageParams PackageParams = {
				"/Script/StreamlineRHI",
				nullptr,
				0,
				PKG_CompiledIn | 0x00000000,
				0x8FB13967,
				0xCBCB1839,
				METADATA_PARAMS(0, nullptr)
			};
			UECodeGen_Private::ConstructUPackage(Z_Registration_Info_UPackage__Script_StreamlineRHI.OuterSingleton, PackageParams);
		}
		return Z_Registration_Info_UPackage__Script_StreamlineRHI.OuterSingleton;
	}
	static FRegisterCompiledInInfo Z_CompiledInDeferPackage_UPackage__Script_StreamlineRHI(Z_Construct_UPackage__Script_StreamlineRHI, TEXT("/Script/StreamlineRHI"), Z_Registration_Info_UPackage__Script_StreamlineRHI, CONSTRUCT_RELOAD_VERSION_INFO(FPackageReloadVersionInfo, 0x8FB13967, 0xCBCB1839));
PRAGMA_ENABLE_DEPRECATION_WARNINGS
