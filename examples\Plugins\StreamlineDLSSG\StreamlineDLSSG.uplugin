{"FileVersion": 3, "Version": 121, "VersionName": "8.1.0-SL2.7.30", "FriendlyName": "NVIDIA DLSS Frame Generation and DLSS Multi Frame Generation", "Description": "NVIDIA DLSS Frame Generation and DLSS Multi Frame Generation use AI to boost frame rates by generating additional high-quality frames, all while optimizing responsiveness with NVIDIA Reflex.", "Category": "Rendering", "CreatedBy": "NVIDIA", "CreatedByURL": "https://developer.nvidia.com/rtx/streamline", "DocsURL": "", "MarketplaceURL": "https://www.unrealengine.com/marketplace/en-US/product/nvidia-dlss", "SupportURL": "mailto:<EMAIL>", "EngineVersion": "5.5.0", "CanContainContent": false, "Installed": true, "Modules": [{"Name": "StreamlineDLSSGBlueprint", "Type": "Runtime", "LoadingPhase": "PostEngineInit"}], "Plugins": [{"Name": "StreamlineCore", "Enabled": true}, {"Name": "StreamlineReflex", "Enabled": true}]}