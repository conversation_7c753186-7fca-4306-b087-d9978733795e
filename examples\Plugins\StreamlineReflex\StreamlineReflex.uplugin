{"FileVersion": 3, "Version": 121, "VersionName": "8.1.0-SL2.7.30", "FriendlyName": "NVIDIA Reflex Low Latency", "Description": "NVIDIA Reflex Low Latency reduces PC latency and increases responsiveness.", "Category": "Rendering", "CreatedBy": "NVIDIA", "CreatedByURL": "https://developer.nvidia.com/rtx/streamline", "DocsURL": "", "MarketplaceURL": "https://www.unrealengine.com/marketplace/en-US/product/nvidia-dlss", "SupportURL": "mailto:<EMAIL>", "EngineVersion": "5.5.0", "CanContainContent": false, "Installed": true, "Modules": [{"Name": "StreamlineReflexBlueprint", "Type": "Runtime", "LoadingPhase": "PostEngineInit"}], "Plugins": [{"Name": "StreamlineCore", "Enabled": true}]}