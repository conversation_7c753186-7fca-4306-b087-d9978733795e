{"FileVersion": 3, "Version": 121, "VersionName": "8.1.0-SL2.7.30", "FriendlyName": "NVIDIA Streamline (deprecated)", "Description": "For compatibility with projects using previous versions of the Streamline plugins", "Category": "Rendering", "CreatedBy": "NVIDIA", "CreatedByURL": "https://developer.nvidia.com/rtx/streamline", "DocsURL": "", "MarketplaceURL": "https://www.unrealengine.com/marketplace/en-US/product/nvidia-dlss", "SupportURL": "mailto:<EMAIL>", "EngineVersion": "5.5.0", "CanContainContent": false, "Installed": true, "Plugins": [{"Name": "StreamlineCore", "Enabled": true}, {"Name": "StreamlineDLSSG", "Enabled": true}, {"Name": "StreamlineDeepDVC", "Enabled": true}, {"Name": "StreamlineReflex", "Enabled": true}]}