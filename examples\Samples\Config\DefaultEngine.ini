[URL]
GameName=StarterContent

[/Script/Engine.RendererSettings]
r.MobileHDR=True
r.AllowOcclusionQueries=True
r.MinScreenRadiusForLights=0.030000
r.MinScreenRadiusForDepthPrepass=0.030000
r.PrecomputedVisibilityWarning=False
r.TextureStreaming=True
Compat.UseDXT5NormalMaps=False
r.AllowStaticLighting=False
r.NormalMapsForStaticLighting=False
r.GBuffer=True
r.GenerateMeshDistanceFields=True
r.Shadow.DistanceFieldPenumbraSize=0.050000
r.TessellationAdaptivePixelsPerTriangle=48.000000
r.SeparateTranslucency=True
r.CustomDepth=3
r.DefaultFeature.Bloom=True
r.DefaultFeature.AmbientOcclusion=True
r.DefaultFeature.AutoExposure=True
r.DefaultFeature.MotionBlur=True
r.DefaultFeature.LensFlare=True
r.DefaultFeature.AntiAliasing=2
r.EarlyZPass=3
r.EarlyZPassMovable=False
r.ClearSceneMethod=1
r.MSAA.CompositingSampleCount=4
r.WireframeCullThreshold=5.000000
UIScaleCurve=(EditorCurveData=(Keys=((Time=480,Value=0.444),(Time=720,Value=0.666),(Time=1080,Value=1.0),(Time=8640,Value=8.0))),ExternalCurve=None)
UIScaleCurve=(EditorCurveData=(Keys=((Time=480.000000,Value=0.444000),(Time=720.000000,Value=0.666000),(Time=1080.000000,Value=1.000000),(Time=8640.000000,Value=8.000000))),ExternalCurve=None)
r.NGX.AppId=0
r.SkinCache.CompileShaders=True
r.RayTracing=True
r.PostProcessing.PropagateAlpha=1
r.RayTracing.Shadows=True
r.RayTracing.SampledLighting=False
r.RayTracing.Skylight=True
r.Lumen.HardwareRayTracing=True
r.RayTracing.UseTextureLod=True
r.Lumen.Reflections.BilateralFilter=0
r.ReflectionMethod=1
r.DynamicGlobalIlluminationMethod=1
r.SupportLowQualityLightmaps=False
r.SupportStationarySkylight=True

[/Script/EngineSettings.GameMapsSettings]
EditorStartupMap=/Game/Maps/Testing/NGXSettingsUI_TestMap.NGXSettingsUI_TestMap
LocalMapOptions=
TransitionMap=
bUseSplitscreen=True
TwoPlayerSplitscreenLayout=Horizontal
ThreePlayerSplitscreenLayout=FavorTop
GameInstanceClass=/Script/Engine.GameInstance
GameDefaultMap=/Game/Maps/Testing/NGXSettingsUI_TestMap.NGXSettingsUI_TestMap
ServerDefaultMap=/Engine/Maps/Entry
GlobalDefaultGameMode=/Script/Engine.GameMode
GlobalDefaultServerGameMode=None

[/Script/IOSRuntimeSettings.IOSRuntimeSettings]
MinimumiOSVersion=IOS_11

[/Script/Engine.PhysicsSettings]
DefaultGravityZ=-980.000000
DefaultTerminalVelocity=4000.000000
DefaultFluidFriction=0.300000
SimulateScratchMemorySize=262144
RagdollAggregateThreshold=4
TriangleMeshTriangleMinAreaThreshold=5.000000
bEnableShapeSharing=False
bEnablePCM=True
bEnableStabilization=False
bWarnMissingLocks=True
bEnable2DPhysics=False
PhysicErrorCorrection=(PingExtrapolation=0.100000,PingLimit=100.000000,ErrorPerLinearDifference=1.000000,ErrorPerAngularDifference=1.000000,MaxRestoredStateError=1.000000,MaxLinearHardSnapDistance=400.000000,PositionLerp=0.000000,AngleLerp=0.400000,LinearVelocityCoefficient=100.000000,AngularVelocityCoefficient=10.000000,ErrorAccumulationSeconds=0.500000,ErrorAccumulationDistanceSq=15.000000,ErrorAccumulationSimilarity=100.000000)
LockedAxis=Invalid
DefaultDegreesOfFreedom=Full3D
BounceThresholdVelocity=200.000000
FrictionCombineMode=Average
RestitutionCombineMode=Average
MaxAngularVelocity=3600.000000
MaxDepenetrationVelocity=0.000000
ContactOffsetMultiplier=0.020000
MinContactOffset=2.000000
MaxContactOffset=8.000000
bSimulateSkeletalMeshOnDedicatedServer=True
DefaultShapeComplexity=CTF_UseSimpleAndComplex
bDefaultHasComplexCollision=True
bSuppressFaceRemapTable=False
bSupportUVFromHitResults=False
bDisableActiveActors=False
bDisableKinematicStaticPairs=False
bDisableKinematicKinematicPairs=False
bDisableCCD=False
bEnableEnhancedDeterminism=False
AnimPhysicsMinDeltaTime=0.000000
bSimulateAnimPhysicsAfterReset=False
MaxPhysicsDeltaTime=0.033333
bSubstepping=False
bSubsteppingAsync=False
MaxSubstepDeltaTime=0.016667
MaxSubsteps=6
SyncSceneSmoothingFactor=0.000000
InitialAverageFrameRate=0.016667
PhysXTreeRebuildRate=10
DefaultBroadphaseSettings=(bUseMBPOnClient=False,bUseMBPOnServer=False,bUseMBPOuterBounds=False,MBPBounds=(Min=(X=0.000000,Y=0.000000,Z=0.000000),Max=(X=0.000000,Y=0.000000,Z=0.000000),IsValid=0),MBPOuterBounds=(Min=(X=0.000000,Y=0.000000,Z=0.000000),Max=(X=0.000000,Y=0.000000,Z=0.000000),IsValid=0),MBPNumSubdivs=2)
ChaosSettings=(DefaultThreadingModel=DedicatedThread,DedicatedThreadTickMode=VariableCappedWithTarget,DedicatedThreadBufferMode=Double)

[/Script/DLSS.DLSSSettings]
bEnableDLSSInEditorViewports=False
bEnableDLSSInPlayInEditorViewports=True
bEnableNonProductionBinariesInShipping=True
NVIDIANGXApplicationId=0

[/Script/WindowsTargetPlatform.WindowsTargetSettings]
DefaultGraphicsRHI=DefaultGraphicsRHI_DX12
-D3D12TargetedShaderFormats=PCD3D_SM5
+D3D12TargetedShaderFormats=PCD3D_SM5
+D3D12TargetedShaderFormats=PCD3D_SM6
-D3D11TargetedShaderFormats=PCD3D_SM5
+D3D11TargetedShaderFormats=PCD3D_SM5
+VulkanTargetedShaderFormats=SF_VULKAN_SM5
Compiler=Default
AudioSampleRate=48000
AudioCallbackBufferFrameSize=1024
AudioNumBuffersToEnqueue=1
AudioMaxChannels=0
AudioNumSourceWorkers=4
SpatializationPlugin=
SourceDataOverridePlugin=
ReverbPlugin=
OcclusionPlugin=
CompressionOverrides=(bOverrideCompressionTimes=False,DurationThreshold=5.000000,MaxNumRandomBranches=0,SoundCueQualityIndex=0)
CacheSizeKB=0
MaxChunkSizeOverrideKB=0
bResampleForDevice=False
MaxSampleRate=0.000000
HighSampleRate=0.000000
MedSampleRate=0.000000
LowSampleRate=0.000000
MinSampleRate=0.000000
CompressionQualityModifier=0.000000
AutoStreamingThreshold=0.000000
SoundCueCookQualityIndex=-1

[/Script/AndroidFileServerEditor.AndroidFileServerRuntimeSettings]
bEnablePlugin=True
bAllowNetworkConnection=True
SecurityToken=1DD3A94842555366FDAD429670C0A6AF
bIncludeInShipping=False
bAllowExternalStartInShipping=False
bCompileAFSProject=False
bUseCompression=False
bLogFiles=False
bReportStats=False
ConnectionType=USBOnly
bUseManualIPAddress=False
ManualIPAddress=

[/Script/StreamlineRHI.StreamlineSettings]
bLoadDebugOverlay=False

[/Script/UnrealEd.CookerSettings]
bCookOnTheFlyForLaunchOn=True

