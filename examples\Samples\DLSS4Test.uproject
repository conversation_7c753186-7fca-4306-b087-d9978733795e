{"FileVersion": 3, "EngineAssociation": "", "Category": "", "Description": "", "Plugins": [{"Name": "DLSS", "Enabled": true, "MarketplaceURL": "https://www.unrealengine.com/marketplace/en-US/product/nvidia-dlss"}, {"Name": "DLSSMoviePipelineSupport", "Enabled": true, "MarketplaceURL": "https://www.unrealengine.com/marketplace/en-US/product/nvidia-dlss"}, {"Name": "NIS", "Enabled": true, "MarketplaceURL": "https://www.unrealengine.com/marketplace/en-US/product/nvidia-dlss"}, {"Name": "Streamline", "Enabled": true, "MarketplaceURL": "https://www.unrealengine.com/marketplace/en-US/product/nvidia-dlss"}, {"Name": "UIWidgetsLib", "Enabled": true}, {"Name": "StreamlineDeepDVC", "Enabled": true, "MarketplaceURL": "https://www.unrealengine.com/marketplace/en-US/product/nvidia-dlss"}, {"Name": "StreamlineDLSSG", "Enabled": true, "MarketplaceURL": "https://www.unrealengine.com/marketplace/en-US/product/nvidia-dlss"}, {"Name": "StreamlineReflex", "Enabled": true, "MarketplaceURL": "https://www.unrealengine.com/marketplace/en-US/product/nvidia-dlss"}, {"Name": "StreamlineCore", "Enabled": true, "MarketplaceURL": "https://www.unrealengine.com/marketplace/en-US/product/nvidia-dlss"}]}