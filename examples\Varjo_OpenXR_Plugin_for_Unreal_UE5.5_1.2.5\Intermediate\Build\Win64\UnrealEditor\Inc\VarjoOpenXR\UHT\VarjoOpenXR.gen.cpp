// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "VarjoOpenXR/Public/VarjoOpenXR.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeVarjoOpenXR() {}

// Begin Cross Module References
ENGINE_API UClass* Z_Construct_UClass_UBlueprintFunctionLibrary();
UPackage* Z_Construct_UPackage__Script_VarjoOpenXR();
VARJOOPENXR_API UClass* Z_Construct_UClass_UVarjoOpenXRFunctionLibrary();
VARJOOPENXR_API UClass* Z_Construct_UClass_UVarjoOpenXRFunctionLibrary_NoRegister();
VARJOOPENXR_API UEnum* Z_Construct_UEnum_VarjoOpenXR_EMarkerTrackingMode();
// End Cross Module References

// Begin Enum EMarkerTrackingMode
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EMarkerTrackingMode;
static UEnum* EMarkerTrackingMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EMarkerTrackingMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EMarkerTrackingMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_VarjoOpenXR_EMarkerTrackingMode, (UObject*)Z_Construct_UPackage__Script_VarjoOpenXR(), TEXT("EMarkerTrackingMode"));
	}
	return Z_Registration_Info_UEnum_EMarkerTrackingMode.OuterSingleton;
}
template<> VARJOOPENXR_API UEnum* StaticEnum<EMarkerTrackingMode>()
{
	return EMarkerTrackingMode_StaticEnum();
}
struct Z_Construct_UEnum_VarjoOpenXR_EMarkerTrackingMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Category", "VarjoOpenXR|Markers" },
		{ "Dynamic.Name", "EMarkerTrackingMode::Dynamic" },
		{ "ModuleRelativePath", "Public/VarjoOpenXR.h" },
		{ "Stationary.Name", "EMarkerTrackingMode::Stationary" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EMarkerTrackingMode::Stationary", (int64)EMarkerTrackingMode::Stationary },
		{ "EMarkerTrackingMode::Dynamic", (int64)EMarkerTrackingMode::Dynamic },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_VarjoOpenXR_EMarkerTrackingMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_VarjoOpenXR,
	nullptr,
	"EMarkerTrackingMode",
	"EMarkerTrackingMode",
	Z_Construct_UEnum_VarjoOpenXR_EMarkerTrackingMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_VarjoOpenXR_EMarkerTrackingMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_VarjoOpenXR_EMarkerTrackingMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_VarjoOpenXR_EMarkerTrackingMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_VarjoOpenXR_EMarkerTrackingMode()
{
	if (!Z_Registration_Info_UEnum_EMarkerTrackingMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EMarkerTrackingMode.InnerSingleton, Z_Construct_UEnum_VarjoOpenXR_EMarkerTrackingMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EMarkerTrackingMode.InnerSingleton;
}
// End Enum EMarkerTrackingMode

// Begin Class UVarjoOpenXRFunctionLibrary Function GetDepthTestRange
struct Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetDepthTestRange_Statics
{
	struct VarjoOpenXRFunctionLibrary_eventGetDepthTestRange_Parms
	{
		bool bIsEnabled;
		float NearZ;
		float FarZ;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "VarjoOpenXR|Depth" },
		{ "Comment", "/**\n     * Get depth test range.\n     */" },
		{ "DisplayName", "Get Depth Test Range" },
		{ "Keywords", "VarjoOpenXR depth test range" },
		{ "ModuleRelativePath", "Public/VarjoOpenXR.h" },
		{ "ToolTip", "Get depth test range." },
	};
#endif // WITH_METADATA
	static void NewProp_bIsEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsEnabled;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NearZ;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FarZ;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetDepthTestRange_Statics::NewProp_bIsEnabled_SetBit(void* Obj)
{
	((VarjoOpenXRFunctionLibrary_eventGetDepthTestRange_Parms*)Obj)->bIsEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetDepthTestRange_Statics::NewProp_bIsEnabled = { "bIsEnabled", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(VarjoOpenXRFunctionLibrary_eventGetDepthTestRange_Parms), &Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetDepthTestRange_Statics::NewProp_bIsEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetDepthTestRange_Statics::NewProp_NearZ = { "NearZ", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VarjoOpenXRFunctionLibrary_eventGetDepthTestRange_Parms, NearZ), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetDepthTestRange_Statics::NewProp_FarZ = { "FarZ", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VarjoOpenXRFunctionLibrary_eventGetDepthTestRange_Parms, FarZ), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetDepthTestRange_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetDepthTestRange_Statics::NewProp_bIsEnabled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetDepthTestRange_Statics::NewProp_NearZ,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetDepthTestRange_Statics::NewProp_FarZ,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetDepthTestRange_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetDepthTestRange_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UVarjoOpenXRFunctionLibrary, nullptr, "GetDepthTestRange", nullptr, nullptr, Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetDepthTestRange_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetDepthTestRange_Statics::PropPointers), sizeof(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetDepthTestRange_Statics::VarjoOpenXRFunctionLibrary_eventGetDepthTestRange_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetDepthTestRange_Statics::Function_MetaDataParams), Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetDepthTestRange_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetDepthTestRange_Statics::VarjoOpenXRFunctionLibrary_eventGetDepthTestRange_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetDepthTestRange()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetDepthTestRange_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UVarjoOpenXRFunctionLibrary::execGetDepthTestRange)
{
	P_GET_UBOOL_REF(Z_Param_Out_bIsEnabled);
	P_GET_PROPERTY_REF(FFloatProperty,Z_Param_Out_NearZ);
	P_GET_PROPERTY_REF(FFloatProperty,Z_Param_Out_FarZ);
	P_FINISH;
	P_NATIVE_BEGIN;
	UVarjoOpenXRFunctionLibrary::GetDepthTestRange(Z_Param_Out_bIsEnabled,Z_Param_Out_NearZ,Z_Param_Out_FarZ);
	P_NATIVE_END;
}
// End Class UVarjoOpenXRFunctionLibrary Function GetDepthTestRange

// Begin Class UVarjoOpenXRFunctionLibrary Function GetMarkerTrackingMode
struct Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetMarkerTrackingMode_Statics
{
	struct VarjoOpenXRFunctionLibrary_eventGetMarkerTrackingMode_Parms
	{
		int32 MarkerId;
		EMarkerTrackingMode TrackingMode;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "VarjoOpenXR|Markers" },
		{ "Comment", "/**\n     * Get marker tracking mode.\n     * @param    MarkerId\n     * @param    TrackingMode\n     * @return   True if valid MarkerId is given.\n     */" },
		{ "DisplayName", "Get Marker Tracking Mode" },
		{ "Keywords", "VarjoOpenXR Marker Tracking" },
		{ "ModuleRelativePath", "Public/VarjoOpenXR.h" },
		{ "ToolTip", "Get marker tracking mode.\n@param    MarkerId\n@param    TrackingMode\n@return   True if valid MarkerId is given." },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_MarkerId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TrackingMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TrackingMode;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetMarkerTrackingMode_Statics::NewProp_MarkerId = { "MarkerId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VarjoOpenXRFunctionLibrary_eventGetMarkerTrackingMode_Parms, MarkerId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetMarkerTrackingMode_Statics::NewProp_TrackingMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetMarkerTrackingMode_Statics::NewProp_TrackingMode = { "TrackingMode", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VarjoOpenXRFunctionLibrary_eventGetMarkerTrackingMode_Parms, TrackingMode), Z_Construct_UEnum_VarjoOpenXR_EMarkerTrackingMode, METADATA_PARAMS(0, nullptr) }; // 3078737400
void Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetMarkerTrackingMode_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((VarjoOpenXRFunctionLibrary_eventGetMarkerTrackingMode_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetMarkerTrackingMode_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(VarjoOpenXRFunctionLibrary_eventGetMarkerTrackingMode_Parms), &Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetMarkerTrackingMode_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetMarkerTrackingMode_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetMarkerTrackingMode_Statics::NewProp_MarkerId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetMarkerTrackingMode_Statics::NewProp_TrackingMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetMarkerTrackingMode_Statics::NewProp_TrackingMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetMarkerTrackingMode_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetMarkerTrackingMode_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetMarkerTrackingMode_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UVarjoOpenXRFunctionLibrary, nullptr, "GetMarkerTrackingMode", nullptr, nullptr, Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetMarkerTrackingMode_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetMarkerTrackingMode_Statics::PropPointers), sizeof(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetMarkerTrackingMode_Statics::VarjoOpenXRFunctionLibrary_eventGetMarkerTrackingMode_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetMarkerTrackingMode_Statics::Function_MetaDataParams), Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetMarkerTrackingMode_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetMarkerTrackingMode_Statics::VarjoOpenXRFunctionLibrary_eventGetMarkerTrackingMode_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetMarkerTrackingMode()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetMarkerTrackingMode_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UVarjoOpenXRFunctionLibrary::execGetMarkerTrackingMode)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_MarkerId);
	P_GET_ENUM_REF(EMarkerTrackingMode,Z_Param_Out_TrackingMode);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UVarjoOpenXRFunctionLibrary::GetMarkerTrackingMode(Z_Param_MarkerId,(EMarkerTrackingMode&)(Z_Param_Out_TrackingMode));
	P_NATIVE_END;
}
// End Class UVarjoOpenXRFunctionLibrary Function GetMarkerTrackingMode

// Begin Class UVarjoOpenXRFunctionLibrary Function GetViewOffset
struct Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetViewOffset_Statics
{
	struct VarjoOpenXRFunctionLibrary_eventGetViewOffset_Parms
	{
		float Offset;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "VarjoOpenXR|MR" },
		{ "Comment", "/**\n     * Get view offset.\n     */" },
		{ "DisplayName", "Get View Offset" },
		{ "Keywords", "VarjoOpenXR view offset" },
		{ "ModuleRelativePath", "Public/VarjoOpenXR.h" },
		{ "ToolTip", "Get view offset." },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Offset;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetViewOffset_Statics::NewProp_Offset = { "Offset", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VarjoOpenXRFunctionLibrary_eventGetViewOffset_Parms, Offset), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetViewOffset_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetViewOffset_Statics::NewProp_Offset,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetViewOffset_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetViewOffset_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UVarjoOpenXRFunctionLibrary, nullptr, "GetViewOffset", nullptr, nullptr, Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetViewOffset_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetViewOffset_Statics::PropPointers), sizeof(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetViewOffset_Statics::VarjoOpenXRFunctionLibrary_eventGetViewOffset_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetViewOffset_Statics::Function_MetaDataParams), Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetViewOffset_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetViewOffset_Statics::VarjoOpenXRFunctionLibrary_eventGetViewOffset_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetViewOffset()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetViewOffset_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UVarjoOpenXRFunctionLibrary::execGetViewOffset)
{
	P_GET_PROPERTY_REF(FFloatProperty,Z_Param_Out_Offset);
	P_FINISH;
	P_NATIVE_BEGIN;
	UVarjoOpenXRFunctionLibrary::GetViewOffset(Z_Param_Out_Offset);
	P_NATIVE_END;
}
// End Class UVarjoOpenXRFunctionLibrary Function GetViewOffset

// Begin Class UVarjoOpenXRFunctionLibrary Function IsDepthTestEnabled
struct Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsDepthTestEnabled_Statics
{
	struct VarjoOpenXRFunctionLibrary_eventIsDepthTestEnabled_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "VarjoOpenXR|Depth" },
		{ "Comment", "/**\n     * Check if depth testing is enabled.\n     */" },
		{ "DisplayName", "Is Depth Test Enabled" },
		{ "Keywords", "VarjoOpenXR depth test" },
		{ "ModuleRelativePath", "Public/VarjoOpenXR.h" },
		{ "ToolTip", "Check if depth testing is enabled." },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsDepthTestEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((VarjoOpenXRFunctionLibrary_eventIsDepthTestEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsDepthTestEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(VarjoOpenXRFunctionLibrary_eventIsDepthTestEnabled_Parms), &Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsDepthTestEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsDepthTestEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsDepthTestEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsDepthTestEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsDepthTestEnabled_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UVarjoOpenXRFunctionLibrary, nullptr, "IsDepthTestEnabled", nullptr, nullptr, Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsDepthTestEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsDepthTestEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsDepthTestEnabled_Statics::VarjoOpenXRFunctionLibrary_eventIsDepthTestEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsDepthTestEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsDepthTestEnabled_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsDepthTestEnabled_Statics::VarjoOpenXRFunctionLibrary_eventIsDepthTestEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsDepthTestEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsDepthTestEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UVarjoOpenXRFunctionLibrary::execIsDepthTestEnabled)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UVarjoOpenXRFunctionLibrary::IsDepthTestEnabled();
	P_NATIVE_END;
}
// End Class UVarjoOpenXRFunctionLibrary Function IsDepthTestEnabled

// Begin Class UVarjoOpenXRFunctionLibrary Function IsDepthTestSupported
struct Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsDepthTestSupported_Statics
{
	struct VarjoOpenXRFunctionLibrary_eventIsDepthTestSupported_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "VarjoOpenXR|Depth" },
		{ "Comment", "/**\n     * Check if depth testing is supported by active OpenXR runtime.\n     */" },
		{ "DisplayName", "Is Depth Test Supported" },
		{ "Keywords", "VarjoOpenXR depth test" },
		{ "ModuleRelativePath", "Public/VarjoOpenXR.h" },
		{ "ToolTip", "Check if depth testing is supported by active OpenXR runtime." },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsDepthTestSupported_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((VarjoOpenXRFunctionLibrary_eventIsDepthTestSupported_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsDepthTestSupported_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(VarjoOpenXRFunctionLibrary_eventIsDepthTestSupported_Parms), &Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsDepthTestSupported_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsDepthTestSupported_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsDepthTestSupported_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsDepthTestSupported_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsDepthTestSupported_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UVarjoOpenXRFunctionLibrary, nullptr, "IsDepthTestSupported", nullptr, nullptr, Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsDepthTestSupported_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsDepthTestSupported_Statics::PropPointers), sizeof(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsDepthTestSupported_Statics::VarjoOpenXRFunctionLibrary_eventIsDepthTestSupported_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsDepthTestSupported_Statics::Function_MetaDataParams), Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsDepthTestSupported_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsDepthTestSupported_Statics::VarjoOpenXRFunctionLibrary_eventIsDepthTestSupported_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsDepthTestSupported()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsDepthTestSupported_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UVarjoOpenXRFunctionLibrary::execIsDepthTestSupported)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UVarjoOpenXRFunctionLibrary::IsDepthTestSupported();
	P_NATIVE_END;
}
// End Class UVarjoOpenXRFunctionLibrary Function IsDepthTestSupported

// Begin Class UVarjoOpenXRFunctionLibrary Function IsEnvironmentDepthEstimationEnabled
struct Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsEnvironmentDepthEstimationEnabled_Statics
{
	struct VarjoOpenXRFunctionLibrary_eventIsEnvironmentDepthEstimationEnabled_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "VarjoOpenXR|Depth" },
		{ "Comment", "/**\n     * Check if environment depth estimation is enabled.\n     */" },
		{ "DisplayName", "Is Environment Depth Estimation Enabled" },
		{ "Keywords", "VarjoOpenXR environment depth estimation" },
		{ "ModuleRelativePath", "Public/VarjoOpenXR.h" },
		{ "ToolTip", "Check if environment depth estimation is enabled." },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsEnvironmentDepthEstimationEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((VarjoOpenXRFunctionLibrary_eventIsEnvironmentDepthEstimationEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsEnvironmentDepthEstimationEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(VarjoOpenXRFunctionLibrary_eventIsEnvironmentDepthEstimationEnabled_Parms), &Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsEnvironmentDepthEstimationEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsEnvironmentDepthEstimationEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsEnvironmentDepthEstimationEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsEnvironmentDepthEstimationEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsEnvironmentDepthEstimationEnabled_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UVarjoOpenXRFunctionLibrary, nullptr, "IsEnvironmentDepthEstimationEnabled", nullptr, nullptr, Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsEnvironmentDepthEstimationEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsEnvironmentDepthEstimationEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsEnvironmentDepthEstimationEnabled_Statics::VarjoOpenXRFunctionLibrary_eventIsEnvironmentDepthEstimationEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsEnvironmentDepthEstimationEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsEnvironmentDepthEstimationEnabled_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsEnvironmentDepthEstimationEnabled_Statics::VarjoOpenXRFunctionLibrary_eventIsEnvironmentDepthEstimationEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsEnvironmentDepthEstimationEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsEnvironmentDepthEstimationEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UVarjoOpenXRFunctionLibrary::execIsEnvironmentDepthEstimationEnabled)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UVarjoOpenXRFunctionLibrary::IsEnvironmentDepthEstimationEnabled();
	P_NATIVE_END;
}
// End Class UVarjoOpenXRFunctionLibrary Function IsEnvironmentDepthEstimationEnabled

// Begin Class UVarjoOpenXRFunctionLibrary Function IsEnvironmentDepthEstimationSupported
struct Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsEnvironmentDepthEstimationSupported_Statics
{
	struct VarjoOpenXRFunctionLibrary_eventIsEnvironmentDepthEstimationSupported_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "VarjoOpenXR|Depth" },
		{ "Comment", "/**\n     * Check if environment depth estimation is supported by active OpenXR runtime.\n     */" },
		{ "DisplayName", "Is Environment Depth Estimation Supported" },
		{ "Keywords", "VarjoOpenXR environment depth estimation" },
		{ "ModuleRelativePath", "Public/VarjoOpenXR.h" },
		{ "ToolTip", "Check if environment depth estimation is supported by active OpenXR runtime." },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsEnvironmentDepthEstimationSupported_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((VarjoOpenXRFunctionLibrary_eventIsEnvironmentDepthEstimationSupported_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsEnvironmentDepthEstimationSupported_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(VarjoOpenXRFunctionLibrary_eventIsEnvironmentDepthEstimationSupported_Parms), &Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsEnvironmentDepthEstimationSupported_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsEnvironmentDepthEstimationSupported_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsEnvironmentDepthEstimationSupported_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsEnvironmentDepthEstimationSupported_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsEnvironmentDepthEstimationSupported_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UVarjoOpenXRFunctionLibrary, nullptr, "IsEnvironmentDepthEstimationSupported", nullptr, nullptr, Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsEnvironmentDepthEstimationSupported_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsEnvironmentDepthEstimationSupported_Statics::PropPointers), sizeof(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsEnvironmentDepthEstimationSupported_Statics::VarjoOpenXRFunctionLibrary_eventIsEnvironmentDepthEstimationSupported_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsEnvironmentDepthEstimationSupported_Statics::Function_MetaDataParams), Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsEnvironmentDepthEstimationSupported_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsEnvironmentDepthEstimationSupported_Statics::VarjoOpenXRFunctionLibrary_eventIsEnvironmentDepthEstimationSupported_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsEnvironmentDepthEstimationSupported()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsEnvironmentDepthEstimationSupported_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UVarjoOpenXRFunctionLibrary::execIsEnvironmentDepthEstimationSupported)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UVarjoOpenXRFunctionLibrary::IsEnvironmentDepthEstimationSupported();
	P_NATIVE_END;
}
// End Class UVarjoOpenXRFunctionLibrary Function IsEnvironmentDepthEstimationSupported

// Begin Class UVarjoOpenXRFunctionLibrary Function IsFoveatedRenderingEnabled
struct Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsFoveatedRenderingEnabled_Statics
{
	struct VarjoOpenXRFunctionLibrary_eventIsFoveatedRenderingEnabled_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "VarjoOpenXR|Foveated Rendering" },
		{ "Comment", "/**\n     * Check if foveated rendering is enabled.\n     */" },
		{ "DisplayName", "Is Foveated Rendering Enabled" },
		{ "Keywords", "VarjoOpenXR foveated rendering" },
		{ "ModuleRelativePath", "Public/VarjoOpenXR.h" },
		{ "ToolTip", "Check if foveated rendering is enabled." },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsFoveatedRenderingEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((VarjoOpenXRFunctionLibrary_eventIsFoveatedRenderingEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsFoveatedRenderingEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(VarjoOpenXRFunctionLibrary_eventIsFoveatedRenderingEnabled_Parms), &Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsFoveatedRenderingEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsFoveatedRenderingEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsFoveatedRenderingEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsFoveatedRenderingEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsFoveatedRenderingEnabled_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UVarjoOpenXRFunctionLibrary, nullptr, "IsFoveatedRenderingEnabled", nullptr, nullptr, Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsFoveatedRenderingEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsFoveatedRenderingEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsFoveatedRenderingEnabled_Statics::VarjoOpenXRFunctionLibrary_eventIsFoveatedRenderingEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsFoveatedRenderingEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsFoveatedRenderingEnabled_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsFoveatedRenderingEnabled_Statics::VarjoOpenXRFunctionLibrary_eventIsFoveatedRenderingEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsFoveatedRenderingEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsFoveatedRenderingEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UVarjoOpenXRFunctionLibrary::execIsFoveatedRenderingEnabled)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UVarjoOpenXRFunctionLibrary::IsFoveatedRenderingEnabled();
	P_NATIVE_END;
}
// End Class UVarjoOpenXRFunctionLibrary Function IsFoveatedRenderingEnabled

// Begin Class UVarjoOpenXRFunctionLibrary Function IsFoveatedRenderingSupported
struct Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsFoveatedRenderingSupported_Statics
{
	struct VarjoOpenXRFunctionLibrary_eventIsFoveatedRenderingSupported_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "VarjoOpenXR|Foveated Rendering" },
		{ "Comment", "/**\n     * Check if foveated rendering is supported by active OpenXR runtime.\n     */" },
		{ "DisplayName", "Is Foveated Rendering Supported" },
		{ "Keywords", "VarjoOpenXR foveated rendering" },
		{ "ModuleRelativePath", "Public/VarjoOpenXR.h" },
		{ "ToolTip", "Check if foveated rendering is supported by active OpenXR runtime." },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsFoveatedRenderingSupported_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((VarjoOpenXRFunctionLibrary_eventIsFoveatedRenderingSupported_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsFoveatedRenderingSupported_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(VarjoOpenXRFunctionLibrary_eventIsFoveatedRenderingSupported_Parms), &Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsFoveatedRenderingSupported_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsFoveatedRenderingSupported_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsFoveatedRenderingSupported_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsFoveatedRenderingSupported_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsFoveatedRenderingSupported_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UVarjoOpenXRFunctionLibrary, nullptr, "IsFoveatedRenderingSupported", nullptr, nullptr, Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsFoveatedRenderingSupported_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsFoveatedRenderingSupported_Statics::PropPointers), sizeof(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsFoveatedRenderingSupported_Statics::VarjoOpenXRFunctionLibrary_eventIsFoveatedRenderingSupported_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsFoveatedRenderingSupported_Statics::Function_MetaDataParams), Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsFoveatedRenderingSupported_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsFoveatedRenderingSupported_Statics::VarjoOpenXRFunctionLibrary_eventIsFoveatedRenderingSupported_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsFoveatedRenderingSupported()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsFoveatedRenderingSupported_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UVarjoOpenXRFunctionLibrary::execIsFoveatedRenderingSupported)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UVarjoOpenXRFunctionLibrary::IsFoveatedRenderingSupported();
	P_NATIVE_END;
}
// End Class UVarjoOpenXRFunctionLibrary Function IsFoveatedRenderingSupported

// Begin Class UVarjoOpenXRFunctionLibrary Function IsMixedRealityEnabled
struct Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsMixedRealityEnabled_Statics
{
	struct VarjoOpenXRFunctionLibrary_eventIsMixedRealityEnabled_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "VarjoOpenXR|MR" },
		{ "Comment", "/**\n     * Check if mixed reality is enabled.\n     */" },
		{ "DisplayName", "Is Mixed Reality Enabled" },
		{ "Keywords", "VarjoOpenXR mixed reality" },
		{ "ModuleRelativePath", "Public/VarjoOpenXR.h" },
		{ "ToolTip", "Check if mixed reality is enabled." },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsMixedRealityEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((VarjoOpenXRFunctionLibrary_eventIsMixedRealityEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsMixedRealityEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(VarjoOpenXRFunctionLibrary_eventIsMixedRealityEnabled_Parms), &Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsMixedRealityEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsMixedRealityEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsMixedRealityEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsMixedRealityEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsMixedRealityEnabled_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UVarjoOpenXRFunctionLibrary, nullptr, "IsMixedRealityEnabled", nullptr, nullptr, Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsMixedRealityEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsMixedRealityEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsMixedRealityEnabled_Statics::VarjoOpenXRFunctionLibrary_eventIsMixedRealityEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsMixedRealityEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsMixedRealityEnabled_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsMixedRealityEnabled_Statics::VarjoOpenXRFunctionLibrary_eventIsMixedRealityEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsMixedRealityEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsMixedRealityEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UVarjoOpenXRFunctionLibrary::execIsMixedRealityEnabled)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UVarjoOpenXRFunctionLibrary::IsMixedRealityEnabled();
	P_NATIVE_END;
}
// End Class UVarjoOpenXRFunctionLibrary Function IsMixedRealityEnabled

// Begin Class UVarjoOpenXRFunctionLibrary Function IsMixedRealitySupported
struct Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsMixedRealitySupported_Statics
{
	struct VarjoOpenXRFunctionLibrary_eventIsMixedRealitySupported_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "VarjoOpenXR|MR" },
		{ "Comment", "/**\n     * Check if mixed reality is supported by active OpenXR runtime.\n     */" },
		{ "DisplayName", "Is Mixed Reality Supported" },
		{ "Keywords", "VarjoOpenXR mixed reality" },
		{ "ModuleRelativePath", "Public/VarjoOpenXR.h" },
		{ "ToolTip", "Check if mixed reality is supported by active OpenXR runtime." },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsMixedRealitySupported_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((VarjoOpenXRFunctionLibrary_eventIsMixedRealitySupported_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsMixedRealitySupported_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(VarjoOpenXRFunctionLibrary_eventIsMixedRealitySupported_Parms), &Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsMixedRealitySupported_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsMixedRealitySupported_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsMixedRealitySupported_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsMixedRealitySupported_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsMixedRealitySupported_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UVarjoOpenXRFunctionLibrary, nullptr, "IsMixedRealitySupported", nullptr, nullptr, Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsMixedRealitySupported_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsMixedRealitySupported_Statics::PropPointers), sizeof(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsMixedRealitySupported_Statics::VarjoOpenXRFunctionLibrary_eventIsMixedRealitySupported_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsMixedRealitySupported_Statics::Function_MetaDataParams), Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsMixedRealitySupported_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsMixedRealitySupported_Statics::VarjoOpenXRFunctionLibrary_eventIsMixedRealitySupported_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsMixedRealitySupported()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsMixedRealitySupported_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UVarjoOpenXRFunctionLibrary::execIsMixedRealitySupported)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UVarjoOpenXRFunctionLibrary::IsMixedRealitySupported();
	P_NATIVE_END;
}
// End Class UVarjoOpenXRFunctionLibrary Function IsMixedRealitySupported

// Begin Class UVarjoOpenXRFunctionLibrary Function IsVarjoMarkersEnabled
struct Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsVarjoMarkersEnabled_Statics
{
	struct VarjoOpenXRFunctionLibrary_eventIsVarjoMarkersEnabled_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "VarjoOpenXR|Markers" },
		{ "Comment", "/**\n     * Check if Varjo Marker Tracking is enabled.\n     */" },
		{ "DisplayName", "Is Varjo Marker Tracking Enabled" },
		{ "Keywords", "VarjoOpenXR Marker Tracking" },
		{ "ModuleRelativePath", "Public/VarjoOpenXR.h" },
		{ "ToolTip", "Check if Varjo Marker Tracking is enabled." },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsVarjoMarkersEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((VarjoOpenXRFunctionLibrary_eventIsVarjoMarkersEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsVarjoMarkersEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(VarjoOpenXRFunctionLibrary_eventIsVarjoMarkersEnabled_Parms), &Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsVarjoMarkersEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsVarjoMarkersEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsVarjoMarkersEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsVarjoMarkersEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsVarjoMarkersEnabled_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UVarjoOpenXRFunctionLibrary, nullptr, "IsVarjoMarkersEnabled", nullptr, nullptr, Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsVarjoMarkersEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsVarjoMarkersEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsVarjoMarkersEnabled_Statics::VarjoOpenXRFunctionLibrary_eventIsVarjoMarkersEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsVarjoMarkersEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsVarjoMarkersEnabled_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsVarjoMarkersEnabled_Statics::VarjoOpenXRFunctionLibrary_eventIsVarjoMarkersEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsVarjoMarkersEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsVarjoMarkersEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UVarjoOpenXRFunctionLibrary::execIsVarjoMarkersEnabled)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UVarjoOpenXRFunctionLibrary::IsVarjoMarkersEnabled();
	P_NATIVE_END;
}
// End Class UVarjoOpenXRFunctionLibrary Function IsVarjoMarkersEnabled

// Begin Class UVarjoOpenXRFunctionLibrary Function IsVarjoMarkersSupported
struct Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsVarjoMarkersSupported_Statics
{
	struct VarjoOpenXRFunctionLibrary_eventIsVarjoMarkersSupported_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "VarjoOpenXR|Markers" },
		{ "Comment", "/**\n     * Check if Varjo Marker tracking is supported.\n     */" },
		{ "DisplayName", "Is Varjo Marker Tracking Supported" },
		{ "Keywords", "VarjoOpenXR Marker Tracking" },
		{ "ModuleRelativePath", "Public/VarjoOpenXR.h" },
		{ "ToolTip", "Check if Varjo Marker tracking is supported." },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsVarjoMarkersSupported_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((VarjoOpenXRFunctionLibrary_eventIsVarjoMarkersSupported_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsVarjoMarkersSupported_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(VarjoOpenXRFunctionLibrary_eventIsVarjoMarkersSupported_Parms), &Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsVarjoMarkersSupported_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsVarjoMarkersSupported_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsVarjoMarkersSupported_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsVarjoMarkersSupported_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsVarjoMarkersSupported_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UVarjoOpenXRFunctionLibrary, nullptr, "IsVarjoMarkersSupported", nullptr, nullptr, Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsVarjoMarkersSupported_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsVarjoMarkersSupported_Statics::PropPointers), sizeof(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsVarjoMarkersSupported_Statics::VarjoOpenXRFunctionLibrary_eventIsVarjoMarkersSupported_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsVarjoMarkersSupported_Statics::Function_MetaDataParams), Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsVarjoMarkersSupported_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsVarjoMarkersSupported_Statics::VarjoOpenXRFunctionLibrary_eventIsVarjoMarkersSupported_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsVarjoMarkersSupported()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsVarjoMarkersSupported_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UVarjoOpenXRFunctionLibrary::execIsVarjoMarkersSupported)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UVarjoOpenXRFunctionLibrary::IsVarjoMarkersSupported();
	P_NATIVE_END;
}
// End Class UVarjoOpenXRFunctionLibrary Function IsVarjoMarkersSupported

// Begin Class UVarjoOpenXRFunctionLibrary Function SetDepthTestEnabled
struct Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetDepthTestEnabled_Statics
{
	struct VarjoOpenXRFunctionLibrary_eventSetDepthTestEnabled_Parms
	{
		bool Enabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "VarjoOpenXR|Depth" },
		{ "Comment", "/**\n     * Enables/disables depth testing.\n     *\n     * @param    Enabled\n     */" },
		{ "DisplayName", "Set Depth Test Enabled" },
		{ "Keywords", "VarjoOpenXR depth test" },
		{ "ModuleRelativePath", "Public/VarjoOpenXR.h" },
		{ "ToolTip", "Enables/disables depth testing.\n\n@param    Enabled" },
	};
#endif // WITH_METADATA
	static void NewProp_Enabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_Enabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetDepthTestEnabled_Statics::NewProp_Enabled_SetBit(void* Obj)
{
	((VarjoOpenXRFunctionLibrary_eventSetDepthTestEnabled_Parms*)Obj)->Enabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetDepthTestEnabled_Statics::NewProp_Enabled = { "Enabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(VarjoOpenXRFunctionLibrary_eventSetDepthTestEnabled_Parms), &Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetDepthTestEnabled_Statics::NewProp_Enabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetDepthTestEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetDepthTestEnabled_Statics::NewProp_Enabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetDepthTestEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetDepthTestEnabled_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UVarjoOpenXRFunctionLibrary, nullptr, "SetDepthTestEnabled", nullptr, nullptr, Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetDepthTestEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetDepthTestEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetDepthTestEnabled_Statics::VarjoOpenXRFunctionLibrary_eventSetDepthTestEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetDepthTestEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetDepthTestEnabled_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetDepthTestEnabled_Statics::VarjoOpenXRFunctionLibrary_eventSetDepthTestEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetDepthTestEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetDepthTestEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UVarjoOpenXRFunctionLibrary::execSetDepthTestEnabled)
{
	P_GET_UBOOL(Z_Param_Enabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	UVarjoOpenXRFunctionLibrary::SetDepthTestEnabled(Z_Param_Enabled);
	P_NATIVE_END;
}
// End Class UVarjoOpenXRFunctionLibrary Function SetDepthTestEnabled

// Begin Class UVarjoOpenXRFunctionLibrary Function SetDepthTestRange
struct Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetDepthTestRange_Statics
{
	struct VarjoOpenXRFunctionLibrary_eventSetDepthTestRange_Parms
	{
		bool Enabled;
		float NearZ;
		float FarZ;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "VarjoOpenXR|Depth" },
		{ "Comment", "/**\n     * Sets the depth test range.\n     *\n     * @param    Enabled\n     * @param    NearZ is a non-negative distance in meters that specifies the lower bound of the range where depth testing should be performed. Must be less than depthTestRangeFarZ.\n     * @param    FarZ is a positive distance in meters that specifies the upper bound of the range where depth testing should be performed. Must be greater than depthTestRangeNearZ.\n     */" },
		{ "CPP_Default_FarZ", "1.000000" },
		{ "CPP_Default_NearZ", "0.000000" },
		{ "DisplayName", "Set Depth Test Range" },
		{ "Keywords", "VarjoOpenXR depth test range" },
		{ "ModuleRelativePath", "Public/VarjoOpenXR.h" },
		{ "ToolTip", "Sets the depth test range.\n\n@param    Enabled\n@param    NearZ is a non-negative distance in meters that specifies the lower bound of the range where depth testing should be performed. Must be less than depthTestRangeFarZ.\n@param    FarZ is a positive distance in meters that specifies the upper bound of the range where depth testing should be performed. Must be greater than depthTestRangeNearZ." },
	};
#endif // WITH_METADATA
	static void NewProp_Enabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_Enabled;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NearZ;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FarZ;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetDepthTestRange_Statics::NewProp_Enabled_SetBit(void* Obj)
{
	((VarjoOpenXRFunctionLibrary_eventSetDepthTestRange_Parms*)Obj)->Enabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetDepthTestRange_Statics::NewProp_Enabled = { "Enabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(VarjoOpenXRFunctionLibrary_eventSetDepthTestRange_Parms), &Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetDepthTestRange_Statics::NewProp_Enabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetDepthTestRange_Statics::NewProp_NearZ = { "NearZ", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VarjoOpenXRFunctionLibrary_eventSetDepthTestRange_Parms, NearZ), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetDepthTestRange_Statics::NewProp_FarZ = { "FarZ", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VarjoOpenXRFunctionLibrary_eventSetDepthTestRange_Parms, FarZ), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetDepthTestRange_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetDepthTestRange_Statics::NewProp_Enabled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetDepthTestRange_Statics::NewProp_NearZ,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetDepthTestRange_Statics::NewProp_FarZ,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetDepthTestRange_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetDepthTestRange_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UVarjoOpenXRFunctionLibrary, nullptr, "SetDepthTestRange", nullptr, nullptr, Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetDepthTestRange_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetDepthTestRange_Statics::PropPointers), sizeof(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetDepthTestRange_Statics::VarjoOpenXRFunctionLibrary_eventSetDepthTestRange_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetDepthTestRange_Statics::Function_MetaDataParams), Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetDepthTestRange_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetDepthTestRange_Statics::VarjoOpenXRFunctionLibrary_eventSetDepthTestRange_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetDepthTestRange()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetDepthTestRange_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UVarjoOpenXRFunctionLibrary::execSetDepthTestRange)
{
	P_GET_UBOOL(Z_Param_Enabled);
	P_GET_PROPERTY(FFloatProperty,Z_Param_NearZ);
	P_GET_PROPERTY(FFloatProperty,Z_Param_FarZ);
	P_FINISH;
	P_NATIVE_BEGIN;
	UVarjoOpenXRFunctionLibrary::SetDepthTestRange(Z_Param_Enabled,Z_Param_NearZ,Z_Param_FarZ);
	P_NATIVE_END;
}
// End Class UVarjoOpenXRFunctionLibrary Function SetDepthTestRange

// Begin Class UVarjoOpenXRFunctionLibrary Function SetEnvironmentDepthEstimationEnabled
struct Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetEnvironmentDepthEstimationEnabled_Statics
{
	struct VarjoOpenXRFunctionLibrary_eventSetEnvironmentDepthEstimationEnabled_Parms
	{
		bool Enabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "VarjoOpenXR|Depth" },
		{ "Comment", "/**\n     * Enables/disables environment depth estimation.\n     *\n     * @param    Enabled\n     */" },
		{ "DisplayName", "Set Environment Depth Estimation Enabled" },
		{ "Keywords", "VarjoOpenXR environment depth estimation" },
		{ "ModuleRelativePath", "Public/VarjoOpenXR.h" },
		{ "ToolTip", "Enables/disables environment depth estimation.\n\n@param    Enabled" },
	};
#endif // WITH_METADATA
	static void NewProp_Enabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_Enabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetEnvironmentDepthEstimationEnabled_Statics::NewProp_Enabled_SetBit(void* Obj)
{
	((VarjoOpenXRFunctionLibrary_eventSetEnvironmentDepthEstimationEnabled_Parms*)Obj)->Enabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetEnvironmentDepthEstimationEnabled_Statics::NewProp_Enabled = { "Enabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(VarjoOpenXRFunctionLibrary_eventSetEnvironmentDepthEstimationEnabled_Parms), &Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetEnvironmentDepthEstimationEnabled_Statics::NewProp_Enabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetEnvironmentDepthEstimationEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetEnvironmentDepthEstimationEnabled_Statics::NewProp_Enabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetEnvironmentDepthEstimationEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetEnvironmentDepthEstimationEnabled_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UVarjoOpenXRFunctionLibrary, nullptr, "SetEnvironmentDepthEstimationEnabled", nullptr, nullptr, Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetEnvironmentDepthEstimationEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetEnvironmentDepthEstimationEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetEnvironmentDepthEstimationEnabled_Statics::VarjoOpenXRFunctionLibrary_eventSetEnvironmentDepthEstimationEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetEnvironmentDepthEstimationEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetEnvironmentDepthEstimationEnabled_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetEnvironmentDepthEstimationEnabled_Statics::VarjoOpenXRFunctionLibrary_eventSetEnvironmentDepthEstimationEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetEnvironmentDepthEstimationEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetEnvironmentDepthEstimationEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UVarjoOpenXRFunctionLibrary::execSetEnvironmentDepthEstimationEnabled)
{
	P_GET_UBOOL(Z_Param_Enabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	UVarjoOpenXRFunctionLibrary::SetEnvironmentDepthEstimationEnabled(Z_Param_Enabled);
	P_NATIVE_END;
}
// End Class UVarjoOpenXRFunctionLibrary Function SetEnvironmentDepthEstimationEnabled

// Begin Class UVarjoOpenXRFunctionLibrary Function SetMarkerTimeout
struct Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetMarkerTimeout_Statics
{
	struct VarjoOpenXRFunctionLibrary_eventSetMarkerTimeout_Parms
	{
		int32 MarkerId;
		float Duration;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "VarjoOpenXR|Markers" },
		{ "Comment", "/**\n     * Set timeout on a marker\n     * @param    MarkerId\n     * @param    Duration in seconds.\n     * @return   True if marker timeout was set successfully.\n     */" },
		{ "DisplayName", "Set Marker Timeout (Default is ZERO)" },
		{ "Keywords", "VarjoOpenXR Marker Tracking" },
		{ "ModuleRelativePath", "Public/VarjoOpenXR.h" },
		{ "ToolTip", "Set timeout on a marker\n@param    MarkerId\n@param    Duration in seconds.\n@return   True if marker timeout was set successfully." },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_MarkerId;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetMarkerTimeout_Statics::NewProp_MarkerId = { "MarkerId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VarjoOpenXRFunctionLibrary_eventSetMarkerTimeout_Parms, MarkerId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetMarkerTimeout_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VarjoOpenXRFunctionLibrary_eventSetMarkerTimeout_Parms, Duration), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetMarkerTimeout_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((VarjoOpenXRFunctionLibrary_eventSetMarkerTimeout_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetMarkerTimeout_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(VarjoOpenXRFunctionLibrary_eventSetMarkerTimeout_Parms), &Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetMarkerTimeout_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetMarkerTimeout_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetMarkerTimeout_Statics::NewProp_MarkerId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetMarkerTimeout_Statics::NewProp_Duration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetMarkerTimeout_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetMarkerTimeout_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetMarkerTimeout_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UVarjoOpenXRFunctionLibrary, nullptr, "SetMarkerTimeout", nullptr, nullptr, Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetMarkerTimeout_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetMarkerTimeout_Statics::PropPointers), sizeof(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetMarkerTimeout_Statics::VarjoOpenXRFunctionLibrary_eventSetMarkerTimeout_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetMarkerTimeout_Statics::Function_MetaDataParams), Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetMarkerTimeout_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetMarkerTimeout_Statics::VarjoOpenXRFunctionLibrary_eventSetMarkerTimeout_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetMarkerTimeout()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetMarkerTimeout_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UVarjoOpenXRFunctionLibrary::execSetMarkerTimeout)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_MarkerId);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Duration);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UVarjoOpenXRFunctionLibrary::SetMarkerTimeout(Z_Param_MarkerId,Z_Param_Duration);
	P_NATIVE_END;
}
// End Class UVarjoOpenXRFunctionLibrary Function SetMarkerTimeout

// Begin Class UVarjoOpenXRFunctionLibrary Function SetMarkerTrackingMode
struct Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetMarkerTrackingMode_Statics
{
	struct VarjoOpenXRFunctionLibrary_eventSetMarkerTrackingMode_Parms
	{
		int32 MarkerId;
		EMarkerTrackingMode TrackingMode;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "VarjoOpenXR|Markers" },
		{ "Comment", "/**\n     * Set marker tracking mode. Use Dynamic for more responsive pose updates according to marker's movements. By default markers are treated as Stationary - less responsive to marker's movements.\n     * @param    MarkerId\n     * @param    TrackingMode\n     * @return   True if marker tracking mode was set successfully.\n     */" },
		{ "DisplayName", "Set Marker Tracking Mode" },
		{ "Keywords", "VarjoOpenXR Marker Tracking" },
		{ "ModuleRelativePath", "Public/VarjoOpenXR.h" },
		{ "ToolTip", "Set marker tracking mode. Use Dynamic for more responsive pose updates according to marker's movements. By default markers are treated as Stationary - less responsive to marker's movements.\n@param    MarkerId\n@param    TrackingMode\n@return   True if marker tracking mode was set successfully." },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_MarkerId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TrackingMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TrackingMode;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetMarkerTrackingMode_Statics::NewProp_MarkerId = { "MarkerId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VarjoOpenXRFunctionLibrary_eventSetMarkerTrackingMode_Parms, MarkerId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetMarkerTrackingMode_Statics::NewProp_TrackingMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetMarkerTrackingMode_Statics::NewProp_TrackingMode = { "TrackingMode", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VarjoOpenXRFunctionLibrary_eventSetMarkerTrackingMode_Parms, TrackingMode), Z_Construct_UEnum_VarjoOpenXR_EMarkerTrackingMode, METADATA_PARAMS(0, nullptr) }; // 3078737400
void Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetMarkerTrackingMode_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((VarjoOpenXRFunctionLibrary_eventSetMarkerTrackingMode_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetMarkerTrackingMode_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(VarjoOpenXRFunctionLibrary_eventSetMarkerTrackingMode_Parms), &Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetMarkerTrackingMode_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetMarkerTrackingMode_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetMarkerTrackingMode_Statics::NewProp_MarkerId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetMarkerTrackingMode_Statics::NewProp_TrackingMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetMarkerTrackingMode_Statics::NewProp_TrackingMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetMarkerTrackingMode_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetMarkerTrackingMode_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetMarkerTrackingMode_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UVarjoOpenXRFunctionLibrary, nullptr, "SetMarkerTrackingMode", nullptr, nullptr, Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetMarkerTrackingMode_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetMarkerTrackingMode_Statics::PropPointers), sizeof(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetMarkerTrackingMode_Statics::VarjoOpenXRFunctionLibrary_eventSetMarkerTrackingMode_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetMarkerTrackingMode_Statics::Function_MetaDataParams), Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetMarkerTrackingMode_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetMarkerTrackingMode_Statics::VarjoOpenXRFunctionLibrary_eventSetMarkerTrackingMode_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetMarkerTrackingMode()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetMarkerTrackingMode_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UVarjoOpenXRFunctionLibrary::execSetMarkerTrackingMode)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_MarkerId);
	P_GET_ENUM(EMarkerTrackingMode,Z_Param_TrackingMode);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UVarjoOpenXRFunctionLibrary::SetMarkerTrackingMode(Z_Param_MarkerId,EMarkerTrackingMode(Z_Param_TrackingMode));
	P_NATIVE_END;
}
// End Class UVarjoOpenXRFunctionLibrary Function SetMarkerTrackingMode

// Begin Class UVarjoOpenXRFunctionLibrary Function SetVarjoMarkersEnabled
struct Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetVarjoMarkersEnabled_Statics
{
	struct VarjoOpenXRFunctionLibrary_eventSetVarjoMarkersEnabled_Parms
	{
		bool Enabled;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "VarjoOpenXR|Markers" },
		{ "Comment", "/**\n     * Enables/disables Varjo Marker tracking.\n     *\n     * @param    Enabled\n     * @return   True if Varjo Marker tracking is enabled. This call will fail if HMD is not capable of tracking markers.\n     */" },
		{ "DisplayName", "Set Varjo Marker Tracking Enabled" },
		{ "Keywords", "VarjoOpenXR Marker Tracking" },
		{ "ModuleRelativePath", "Public/VarjoOpenXR.h" },
		{ "ToolTip", "Enables/disables Varjo Marker tracking.\n\n@param    Enabled\n@return   True if Varjo Marker tracking is enabled. This call will fail if HMD is not capable of tracking markers." },
	};
#endif // WITH_METADATA
	static void NewProp_Enabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_Enabled;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetVarjoMarkersEnabled_Statics::NewProp_Enabled_SetBit(void* Obj)
{
	((VarjoOpenXRFunctionLibrary_eventSetVarjoMarkersEnabled_Parms*)Obj)->Enabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetVarjoMarkersEnabled_Statics::NewProp_Enabled = { "Enabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(VarjoOpenXRFunctionLibrary_eventSetVarjoMarkersEnabled_Parms), &Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetVarjoMarkersEnabled_Statics::NewProp_Enabled_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetVarjoMarkersEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((VarjoOpenXRFunctionLibrary_eventSetVarjoMarkersEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetVarjoMarkersEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(VarjoOpenXRFunctionLibrary_eventSetVarjoMarkersEnabled_Parms), &Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetVarjoMarkersEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetVarjoMarkersEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetVarjoMarkersEnabled_Statics::NewProp_Enabled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetVarjoMarkersEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetVarjoMarkersEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetVarjoMarkersEnabled_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UVarjoOpenXRFunctionLibrary, nullptr, "SetVarjoMarkersEnabled", nullptr, nullptr, Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetVarjoMarkersEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetVarjoMarkersEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetVarjoMarkersEnabled_Statics::VarjoOpenXRFunctionLibrary_eventSetVarjoMarkersEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetVarjoMarkersEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetVarjoMarkersEnabled_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetVarjoMarkersEnabled_Statics::VarjoOpenXRFunctionLibrary_eventSetVarjoMarkersEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetVarjoMarkersEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetVarjoMarkersEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UVarjoOpenXRFunctionLibrary::execSetVarjoMarkersEnabled)
{
	P_GET_UBOOL(Z_Param_Enabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UVarjoOpenXRFunctionLibrary::SetVarjoMarkersEnabled(Z_Param_Enabled);
	P_NATIVE_END;
}
// End Class UVarjoOpenXRFunctionLibrary Function SetVarjoMarkersEnabled

// Begin Class UVarjoOpenXRFunctionLibrary Function SetViewOffset
struct Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetViewOffset_Statics
{
	struct VarjoOpenXRFunctionLibrary_eventSetViewOffset_Parms
	{
		float Offset;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "VarjoOpenXR|MR" },
		{ "Comment", "/**\n     * Sets the view offset. When the offset is 0.0, rendering happens at the eye location. When the offset is 1.0, rendering happens at the video pass-through camera location.\n     *\n     * @param    Offset. Must be between 0.0 and 1.0.\n     */" },
		{ "CPP_Default_Offset", "1.000000" },
		{ "DisplayName", "Set View Offset" },
		{ "Keywords", "VarjoOpenXR view offset" },
		{ "ModuleRelativePath", "Public/VarjoOpenXR.h" },
		{ "ToolTip", "Sets the view offset. When the offset is 0.0, rendering happens at the eye location. When the offset is 1.0, rendering happens at the video pass-through camera location.\n\n@param    Offset. Must be between 0.0 and 1.0." },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Offset;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetViewOffset_Statics::NewProp_Offset = { "Offset", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VarjoOpenXRFunctionLibrary_eventSetViewOffset_Parms, Offset), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetViewOffset_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((VarjoOpenXRFunctionLibrary_eventSetViewOffset_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetViewOffset_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(VarjoOpenXRFunctionLibrary_eventSetViewOffset_Parms), &Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetViewOffset_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetViewOffset_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetViewOffset_Statics::NewProp_Offset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetViewOffset_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetViewOffset_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetViewOffset_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UVarjoOpenXRFunctionLibrary, nullptr, "SetViewOffset", nullptr, nullptr, Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetViewOffset_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetViewOffset_Statics::PropPointers), sizeof(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetViewOffset_Statics::VarjoOpenXRFunctionLibrary_eventSetViewOffset_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetViewOffset_Statics::Function_MetaDataParams), Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetViewOffset_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetViewOffset_Statics::VarjoOpenXRFunctionLibrary_eventSetViewOffset_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetViewOffset()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetViewOffset_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UVarjoOpenXRFunctionLibrary::execSetViewOffset)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Offset);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UVarjoOpenXRFunctionLibrary::SetViewOffset(Z_Param_Offset);
	P_NATIVE_END;
}
// End Class UVarjoOpenXRFunctionLibrary Function SetViewOffset

// Begin Class UVarjoOpenXRFunctionLibrary
void UVarjoOpenXRFunctionLibrary::StaticRegisterNativesUVarjoOpenXRFunctionLibrary()
{
	UClass* Class = UVarjoOpenXRFunctionLibrary::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "GetDepthTestRange", &UVarjoOpenXRFunctionLibrary::execGetDepthTestRange },
		{ "GetMarkerTrackingMode", &UVarjoOpenXRFunctionLibrary::execGetMarkerTrackingMode },
		{ "GetViewOffset", &UVarjoOpenXRFunctionLibrary::execGetViewOffset },
		{ "IsDepthTestEnabled", &UVarjoOpenXRFunctionLibrary::execIsDepthTestEnabled },
		{ "IsDepthTestSupported", &UVarjoOpenXRFunctionLibrary::execIsDepthTestSupported },
		{ "IsEnvironmentDepthEstimationEnabled", &UVarjoOpenXRFunctionLibrary::execIsEnvironmentDepthEstimationEnabled },
		{ "IsEnvironmentDepthEstimationSupported", &UVarjoOpenXRFunctionLibrary::execIsEnvironmentDepthEstimationSupported },
		{ "IsFoveatedRenderingEnabled", &UVarjoOpenXRFunctionLibrary::execIsFoveatedRenderingEnabled },
		{ "IsFoveatedRenderingSupported", &UVarjoOpenXRFunctionLibrary::execIsFoveatedRenderingSupported },
		{ "IsMixedRealityEnabled", &UVarjoOpenXRFunctionLibrary::execIsMixedRealityEnabled },
		{ "IsMixedRealitySupported", &UVarjoOpenXRFunctionLibrary::execIsMixedRealitySupported },
		{ "IsVarjoMarkersEnabled", &UVarjoOpenXRFunctionLibrary::execIsVarjoMarkersEnabled },
		{ "IsVarjoMarkersSupported", &UVarjoOpenXRFunctionLibrary::execIsVarjoMarkersSupported },
		{ "SetDepthTestEnabled", &UVarjoOpenXRFunctionLibrary::execSetDepthTestEnabled },
		{ "SetDepthTestRange", &UVarjoOpenXRFunctionLibrary::execSetDepthTestRange },
		{ "SetEnvironmentDepthEstimationEnabled", &UVarjoOpenXRFunctionLibrary::execSetEnvironmentDepthEstimationEnabled },
		{ "SetMarkerTimeout", &UVarjoOpenXRFunctionLibrary::execSetMarkerTimeout },
		{ "SetMarkerTrackingMode", &UVarjoOpenXRFunctionLibrary::execSetMarkerTrackingMode },
		{ "SetVarjoMarkersEnabled", &UVarjoOpenXRFunctionLibrary::execSetVarjoMarkersEnabled },
		{ "SetViewOffset", &UVarjoOpenXRFunctionLibrary::execSetViewOffset },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(UVarjoOpenXRFunctionLibrary);
UClass* Z_Construct_UClass_UVarjoOpenXRFunctionLibrary_NoRegister()
{
	return UVarjoOpenXRFunctionLibrary::StaticClass();
}
struct Z_Construct_UClass_UVarjoOpenXRFunctionLibrary_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "ClassGroupNames", "OpenXR" },
		{ "IncludePath", "VarjoOpenXR.h" },
		{ "ModuleRelativePath", "Public/VarjoOpenXR.h" },
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetDepthTestRange, "GetDepthTestRange" }, // 2419417893
		{ &Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetMarkerTrackingMode, "GetMarkerTrackingMode" }, // 3347696663
		{ &Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_GetViewOffset, "GetViewOffset" }, // 1087724436
		{ &Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsDepthTestEnabled, "IsDepthTestEnabled" }, // 4119531210
		{ &Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsDepthTestSupported, "IsDepthTestSupported" }, // 2356935428
		{ &Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsEnvironmentDepthEstimationEnabled, "IsEnvironmentDepthEstimationEnabled" }, // 4129713420
		{ &Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsEnvironmentDepthEstimationSupported, "IsEnvironmentDepthEstimationSupported" }, // 18858225
		{ &Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsFoveatedRenderingEnabled, "IsFoveatedRenderingEnabled" }, // 931747775
		{ &Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsFoveatedRenderingSupported, "IsFoveatedRenderingSupported" }, // 1157087053
		{ &Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsMixedRealityEnabled, "IsMixedRealityEnabled" }, // 221591207
		{ &Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsMixedRealitySupported, "IsMixedRealitySupported" }, // 2665117035
		{ &Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsVarjoMarkersEnabled, "IsVarjoMarkersEnabled" }, // 2705741176
		{ &Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_IsVarjoMarkersSupported, "IsVarjoMarkersSupported" }, // 195406532
		{ &Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetDepthTestEnabled, "SetDepthTestEnabled" }, // 3180450422
		{ &Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetDepthTestRange, "SetDepthTestRange" }, // 1509809649
		{ &Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetEnvironmentDepthEstimationEnabled, "SetEnvironmentDepthEstimationEnabled" }, // 141896011
		{ &Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetMarkerTimeout, "SetMarkerTimeout" }, // 2688693589
		{ &Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetMarkerTrackingMode, "SetMarkerTrackingMode" }, // 1167443282
		{ &Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetVarjoMarkersEnabled, "SetVarjoMarkersEnabled" }, // 3043738
		{ &Z_Construct_UFunction_UVarjoOpenXRFunctionLibrary_SetViewOffset, "SetViewOffset" }, // 1406536796
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UVarjoOpenXRFunctionLibrary>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_UVarjoOpenXRFunctionLibrary_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UBlueprintFunctionLibrary,
	(UObject* (*)())Z_Construct_UPackage__Script_VarjoOpenXR,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UVarjoOpenXRFunctionLibrary_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UVarjoOpenXRFunctionLibrary_Statics::ClassParams = {
	&UVarjoOpenXRFunctionLibrary::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	0,
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UVarjoOpenXRFunctionLibrary_Statics::Class_MetaDataParams), Z_Construct_UClass_UVarjoOpenXRFunctionLibrary_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UVarjoOpenXRFunctionLibrary()
{
	if (!Z_Registration_Info_UClass_UVarjoOpenXRFunctionLibrary.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UVarjoOpenXRFunctionLibrary.OuterSingleton, Z_Construct_UClass_UVarjoOpenXRFunctionLibrary_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UVarjoOpenXRFunctionLibrary.OuterSingleton;
}
template<> VARJOOPENXR_API UClass* StaticClass<UVarjoOpenXRFunctionLibrary>()
{
	return UVarjoOpenXRFunctionLibrary::StaticClass();
}
UVarjoOpenXRFunctionLibrary::UVarjoOpenXRFunctionLibrary(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UVarjoOpenXRFunctionLibrary);
UVarjoOpenXRFunctionLibrary::~UVarjoOpenXRFunctionLibrary() {}
// End Class UVarjoOpenXRFunctionLibrary

// Begin Registration
struct Z_CompiledInDeferFile_FID_HostProject_Plugins_VarjoOpenXR_Source_VarjoOpenXR_Public_VarjoOpenXR_h_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EMarkerTrackingMode_StaticEnum, TEXT("EMarkerTrackingMode"), &Z_Registration_Info_UEnum_EMarkerTrackingMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3078737400U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UVarjoOpenXRFunctionLibrary, UVarjoOpenXRFunctionLibrary::StaticClass, TEXT("UVarjoOpenXRFunctionLibrary"), &Z_Registration_Info_UClass_UVarjoOpenXRFunctionLibrary, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UVarjoOpenXRFunctionLibrary), 2079031235U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_HostProject_Plugins_VarjoOpenXR_Source_VarjoOpenXR_Public_VarjoOpenXR_h_1333213606(TEXT("/Script/VarjoOpenXR"),
	Z_CompiledInDeferFile_FID_HostProject_Plugins_VarjoOpenXR_Source_VarjoOpenXR_Public_VarjoOpenXR_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_HostProject_Plugins_VarjoOpenXR_Source_VarjoOpenXR_Public_VarjoOpenXR_h_Statics::ClassInfo),
	nullptr, 0,
	Z_CompiledInDeferFile_FID_HostProject_Plugins_VarjoOpenXR_Source_VarjoOpenXR_Public_VarjoOpenXR_h_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_HostProject_Plugins_VarjoOpenXR_Source_VarjoOpenXR_Public_VarjoOpenXR_h_Statics::EnumInfo));
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
