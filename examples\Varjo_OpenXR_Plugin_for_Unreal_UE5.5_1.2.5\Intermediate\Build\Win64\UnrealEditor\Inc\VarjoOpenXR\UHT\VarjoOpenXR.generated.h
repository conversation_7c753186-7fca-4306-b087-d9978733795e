// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "VarjoOpenXR.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
enum class EMarkerTrackingMode : uint8;
#ifdef VARJOOPENXR_VarjoOpenXR_generated_h
#error "VarjoOpenXR.generated.h already included, missing '#pragma once' in VarjoOpenXR.h"
#endif
#define VARJOOPENXR_VarjoOpenXR_generated_h

#define FID_HostProject_Plugins_VarjoOpenXR_Source_VarjoOpenXR_Public_VarjoOpenXR_h_20_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetMarkerTrackingMode); \
	DECLARE_FUNCTION(execSetMarkerTrackingMode); \
	DECLARE_FUNCTION(execSetMarkerTimeout); \
	DECLARE_FUNCTION(execIsVarjoMarkersEnabled); \
	DECLARE_FUNCTION(execSetVarjoMarkersEnabled); \
	DECLARE_FUNCTION(execIsVarjoMarkersSupported); \
	DECLARE_FUNCTION(execIsFoveatedRenderingEnabled); \
	DECLARE_FUNCTION(execIsFoveatedRenderingSupported); \
	DECLARE_FUNCTION(execIsEnvironmentDepthEstimationEnabled); \
	DECLARE_FUNCTION(execIsEnvironmentDepthEstimationSupported); \
	DECLARE_FUNCTION(execSetEnvironmentDepthEstimationEnabled); \
	DECLARE_FUNCTION(execGetDepthTestRange); \
	DECLARE_FUNCTION(execSetDepthTestRange); \
	DECLARE_FUNCTION(execIsDepthTestEnabled); \
	DECLARE_FUNCTION(execIsDepthTestSupported); \
	DECLARE_FUNCTION(execSetDepthTestEnabled); \
	DECLARE_FUNCTION(execGetViewOffset); \
	DECLARE_FUNCTION(execSetViewOffset); \
	DECLARE_FUNCTION(execIsMixedRealityEnabled); \
	DECLARE_FUNCTION(execIsMixedRealitySupported);


#define FID_HostProject_Plugins_VarjoOpenXR_Source_VarjoOpenXR_Public_VarjoOpenXR_h_20_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUVarjoOpenXRFunctionLibrary(); \
	friend struct Z_Construct_UClass_UVarjoOpenXRFunctionLibrary_Statics; \
public: \
	DECLARE_CLASS(UVarjoOpenXRFunctionLibrary, UBlueprintFunctionLibrary, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/VarjoOpenXR"), NO_API) \
	DECLARE_SERIALIZER(UVarjoOpenXRFunctionLibrary)


#define FID_HostProject_Plugins_VarjoOpenXR_Source_VarjoOpenXR_Public_VarjoOpenXR_h_20_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UVarjoOpenXRFunctionLibrary(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	UVarjoOpenXRFunctionLibrary(UVarjoOpenXRFunctionLibrary&&); \
	UVarjoOpenXRFunctionLibrary(const UVarjoOpenXRFunctionLibrary&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UVarjoOpenXRFunctionLibrary); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UVarjoOpenXRFunctionLibrary); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UVarjoOpenXRFunctionLibrary) \
	NO_API virtual ~UVarjoOpenXRFunctionLibrary();


#define FID_HostProject_Plugins_VarjoOpenXR_Source_VarjoOpenXR_Public_VarjoOpenXR_h_17_PROLOG
#define FID_HostProject_Plugins_VarjoOpenXR_Source_VarjoOpenXR_Public_VarjoOpenXR_h_20_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_HostProject_Plugins_VarjoOpenXR_Source_VarjoOpenXR_Public_VarjoOpenXR_h_20_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_HostProject_Plugins_VarjoOpenXR_Source_VarjoOpenXR_Public_VarjoOpenXR_h_20_INCLASS_NO_PURE_DECLS \
	FID_HostProject_Plugins_VarjoOpenXR_Source_VarjoOpenXR_Public_VarjoOpenXR_h_20_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> VARJOOPENXR_API UClass* StaticClass<class UVarjoOpenXRFunctionLibrary>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_HostProject_Plugins_VarjoOpenXR_Source_VarjoOpenXR_Public_VarjoOpenXR_h


#define FOREACH_ENUM_EMARKERTRACKINGMODE(op) \
	op(EMarkerTrackingMode::Stationary) \
	op(EMarkerTrackingMode::Dynamic) 

enum class EMarkerTrackingMode : uint8;
template<> struct TIsUEnumClass<EMarkerTrackingMode> { enum { Value = true }; };
template<> VARJOOPENXR_API UEnum* StaticEnum<EMarkerTrackingMode>();

PRAGMA_ENABLE_DEPRECATION_WARNINGS
