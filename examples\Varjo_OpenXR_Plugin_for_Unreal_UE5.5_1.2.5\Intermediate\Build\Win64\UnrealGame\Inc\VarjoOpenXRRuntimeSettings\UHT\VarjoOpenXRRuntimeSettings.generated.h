// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "VarjoOpenXRRuntimeSettings.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
#ifdef VARJOOPENXRRUNTIMESETTINGS_VarjoOpenXRRuntimeSettings_generated_h
#error "VarjoOpenXRRuntimeSettings.generated.h already included, missing '#pragma once' in VarjoOpenXRRuntimeSettings.h"
#endif
#define VARJOOPENXRRUNTIMESETTINGS_VarjoOpenXRRuntimeSettings_generated_h

#define FID_HostProject_Plugins_VarjoOpenXR_Source_VarjoOpenXRRuntimeSettings_Public_VarjoOpenXRRuntimeSettings_h_26_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUVarjoOpenXRRuntimeSettings(); \
	friend struct Z_Construct_UClass_UVarjoOpenXRRuntimeSettings_Statics; \
public: \
	DECLARE_CLASS(UVarjoOpenXRRuntimeSettings, UObject, COMPILED_IN_FLAGS(0 | CLASS_DefaultConfig | CLASS_Config), CASTCLASS_None, TEXT("/Script/VarjoOpenXRRuntimeSettings"), NO_API) \
	DECLARE_SERIALIZER(UVarjoOpenXRRuntimeSettings) \
	static const TCHAR* StaticConfigName() {return TEXT("Engine");} \



#define FID_HostProject_Plugins_VarjoOpenXR_Source_VarjoOpenXRRuntimeSettings_Public_VarjoOpenXRRuntimeSettings_h_26_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UVarjoOpenXRRuntimeSettings(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	UVarjoOpenXRRuntimeSettings(UVarjoOpenXRRuntimeSettings&&); \
	UVarjoOpenXRRuntimeSettings(const UVarjoOpenXRRuntimeSettings&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UVarjoOpenXRRuntimeSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UVarjoOpenXRRuntimeSettings); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UVarjoOpenXRRuntimeSettings) \
	NO_API virtual ~UVarjoOpenXRRuntimeSettings();


#define FID_HostProject_Plugins_VarjoOpenXR_Source_VarjoOpenXRRuntimeSettings_Public_VarjoOpenXRRuntimeSettings_h_22_PROLOG
#define FID_HostProject_Plugins_VarjoOpenXR_Source_VarjoOpenXRRuntimeSettings_Public_VarjoOpenXRRuntimeSettings_h_26_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_HostProject_Plugins_VarjoOpenXR_Source_VarjoOpenXRRuntimeSettings_Public_VarjoOpenXRRuntimeSettings_h_26_INCLASS_NO_PURE_DECLS \
	FID_HostProject_Plugins_VarjoOpenXR_Source_VarjoOpenXRRuntimeSettings_Public_VarjoOpenXRRuntimeSettings_h_26_ENHANCED_CONSTRUCTORS \
public: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> VARJOOPENXRRUNTIMESETTINGS_API UClass* StaticClass<class UVarjoOpenXRRuntimeSettings>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_HostProject_Plugins_VarjoOpenXR_Source_VarjoOpenXRRuntimeSettings_Public_VarjoOpenXRRuntimeSettings_h


#define FOREACH_ENUM_VARJORENDERINGMODE(op) \
	op(VarjoRenderingMode_QuadView) \
	op(VarjoRenderingMode_Stereo) 

enum VarjoRenderingMode : int;
template<> VARJOOPENXRRUNTIMESETTINGS_API UEnum* StaticEnum<VarjoRenderingMode>();

PRAGMA_ENABLE_DEPRECATION_WARNINGS
