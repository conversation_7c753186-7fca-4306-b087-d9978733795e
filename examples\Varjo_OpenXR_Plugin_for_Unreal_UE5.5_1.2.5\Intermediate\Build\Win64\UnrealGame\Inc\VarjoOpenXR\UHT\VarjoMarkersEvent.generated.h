// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "VarjoMarkersEvent.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
#ifdef VARJOOPENXR_VarjoMarkersEvent_generated_h
#error "VarjoMarkersEvent.generated.h already included, missing '#pragma once' in VarjoMarkersEvent.h"
#endif
#define VARJOOPENXR_VarjoMarkersEvent_generated_h

#define FID_HostProject_Plugins_VarjoOpenXR_Source_VarjoOpenXR_Public_VarjoMarkersEvent_h_22_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUVarjoMarkerDelegates(); \
	friend struct Z_Construct_UClass_UVarjoMarkerDelegates_Statics; \
public: \
	DECLARE_CLASS(UVarjoMarkerDelegates, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/VarjoOpenXR"), NO_API) \
	DECLARE_SERIALIZER(UVarjoMarkerDelegates)


#define FID_HostProject_Plugins_VarjoOpenXR_Source_VarjoOpenXR_Public_VarjoMarkersEvent_h_22_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UVarjoMarkerDelegates(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	UVarjoMarkerDelegates(UVarjoMarkerDelegates&&); \
	UVarjoMarkerDelegates(const UVarjoMarkerDelegates&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UVarjoMarkerDelegates); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UVarjoMarkerDelegates); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UVarjoMarkerDelegates) \
	NO_API virtual ~UVarjoMarkerDelegates();


#define FID_HostProject_Plugins_VarjoOpenXR_Source_VarjoOpenXR_Public_VarjoMarkersEvent_h_19_PROLOG
#define FID_HostProject_Plugins_VarjoOpenXR_Source_VarjoOpenXR_Public_VarjoMarkersEvent_h_22_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_HostProject_Plugins_VarjoOpenXR_Source_VarjoOpenXR_Public_VarjoMarkersEvent_h_22_INCLASS_NO_PURE_DECLS \
	FID_HostProject_Plugins_VarjoOpenXR_Source_VarjoOpenXR_Public_VarjoMarkersEvent_h_22_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> VARJOOPENXR_API UClass* StaticClass<class UVarjoMarkerDelegates>();

#define FID_HostProject_Plugins_VarjoOpenXR_Source_VarjoOpenXR_Public_VarjoMarkersEvent_h_42_DELEGATE \
static void FNewVarjoMarkerDetected_DelegateWrapper(const FMulticastScriptDelegate& NewVarjoMarkerDetected, int32 MarkerId, FVector const& Position, FRotator const& Rotation, FVector2D const& Size);


#define FID_HostProject_Plugins_VarjoOpenXR_Source_VarjoOpenXR_Public_VarjoMarkersEvent_h_46_DELEGATE \
static void FVarjoMarkerMoved_DelegateWrapper(const FMulticastScriptDelegate& VarjoMarkerMoved, int32 MarkerId, FVector const& Position, FRotator const& Rotation, FVector2D const& Size);


#define FID_HostProject_Plugins_VarjoOpenXR_Source_VarjoOpenXR_Public_VarjoMarkersEvent_h_50_DELEGATE \
static void FVarjoMarkerLost_DelegateWrapper(const FMulticastScriptDelegate& VarjoMarkerLost, int32 MarkerId);


#define FID_HostProject_Plugins_VarjoOpenXR_Source_VarjoOpenXR_Public_VarjoMarkersEvent_h_39_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUVarjoMarkersEvent(); \
	friend struct Z_Construct_UClass_UVarjoMarkersEvent_Statics; \
public: \
	DECLARE_CLASS(UVarjoMarkersEvent, UActorComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/VarjoOpenXR"), NO_API) \
	DECLARE_SERIALIZER(UVarjoMarkersEvent)


#define FID_HostProject_Plugins_VarjoOpenXR_Source_VarjoOpenXR_Public_VarjoMarkersEvent_h_39_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UVarjoMarkersEvent(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	UVarjoMarkersEvent(UVarjoMarkersEvent&&); \
	UVarjoMarkersEvent(const UVarjoMarkersEvent&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UVarjoMarkersEvent); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UVarjoMarkersEvent); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UVarjoMarkersEvent) \
	NO_API virtual ~UVarjoMarkersEvent();


#define FID_HostProject_Plugins_VarjoOpenXR_Source_VarjoOpenXR_Public_VarjoMarkersEvent_h_36_PROLOG
#define FID_HostProject_Plugins_VarjoOpenXR_Source_VarjoOpenXR_Public_VarjoMarkersEvent_h_39_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_HostProject_Plugins_VarjoOpenXR_Source_VarjoOpenXR_Public_VarjoMarkersEvent_h_39_INCLASS_NO_PURE_DECLS \
	FID_HostProject_Plugins_VarjoOpenXR_Source_VarjoOpenXR_Public_VarjoMarkersEvent_h_39_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> VARJOOPENXR_API UClass* StaticClass<class UVarjoMarkersEvent>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_HostProject_Plugins_VarjoOpenXR_Source_VarjoOpenXR_Public_VarjoMarkersEvent_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
